import { pickBy } from 'lodash';

type TicketSystemOptions = {
  [x: string]: any;
  header_1?: string;
  value_1?: string;
  header_2?: string;
  value_2?: string;
  header_3?: string;
  value_3?: string;
  header_4?: string;
  value_4?: string;
  header_5?: string;
  value_5?: string;
};

export const extractTicketingSystemOptionFields = (opts: TicketSystemOptions) => {
  const {
    header_1 = null,
    header_2 = null,
    header_3 = null,
    header_4 = null,
    header_5 = null,
    value_1 = null,
    value_2 = null,
    value_3 = null,
    value_4 = null,
    value_5 = null,
    ...rest
  } = opts;
  return {
    opts: pickBy(
      {
        header_1,
        header_2,
        header_3,
        header_4,
        header_5,
        value_1,
        value_2,
        value_3,
        value_4,
        value_5,
      },
      (val) => val,
    ),
    rest,
  };
};
