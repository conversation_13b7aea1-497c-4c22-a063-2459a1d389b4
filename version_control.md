# Version Control Documentation

- **Version**: 1.1.2
- **Deployment Date**: November 23, 2024
- **Deployment Environment**: Dev
- **Change Summary**:

  1. add the api for unity to retrieve the list of redeem code
  2. add the api for unity to redeem an item

- **Version**: 1.1.1
- **Deployment Date**: November 19, 2024
- **Deployment Environment**: Dev
- **Change Summary**:
  1. start inventory api
  2. add try catch error at unity product api
  3. update unity product api
