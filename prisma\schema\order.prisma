/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model order_items {
  order_item_id   Int          @id @default(autoincrement())
  order_id        Int
  product_id      Int?
  sku_id          Int?
  quantity        Int
  unit_price      Decimal      @db.Decimal(12, 2)
  original_price  Decimal      @db.Decimal(12, 2)
  total_price     Decimal      @db.Decimal(12, 2)
  discount_amount Decimal      @db.Decimal(12, 2)
  created_at      Int
  updated_at      Int
  orders          orders       @relation(fields: [order_id], references: [order_id], onDelete: NoAction, onUpdate: NoAction, map: "order_items_ibfk_1")
  products        products?    @relation(fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction, map: "order_items_ibfk_2")
  product_sku     product_sku? @relation(fields: [sku_id], references: [sku_id], onDelete: NoAction, onUpdate: NoAction, map: "order_items_ibfk_3")
  
  // event related
  tickets         ticket[]     @relation("order_ticket")
  ticket_type_id  Int?
  ticket_type     ticket_type?  @relation("order_ticket_type", fields: [ticket_type_id], references: [id])
  ticket_section_id Int?
  ticket_section  ticket_section?  @relation("order_ticket_section", fields: [ticket_section_id], references: [id])     

  @@unique([order_id, product_id, sku_id], map: "uk_order")
  @@index([order_id], map: "idx_order_id")
  @@index([product_id], map: "idx_product_id")
  @@index([sku_id], map: "idx_sku_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model order_shipping {
  order_shipping_id Int                       @id @default(autoincrement())
  order_id          Int                       @unique(map: "uk_order_id")
  shipping_status   order_shipping_status       
  shipping_type     order_shipping_type
  tracking_no       String? @db.VarChar(255)
  consignee         String? @db.VarChar(255)
  country_code      String? @db.VarChar(5)
  contract_no       String? @db.VarChar(20)
  address           String? @db.Text
  created_at        Int
  updated_at        Int
  orders            orders  @relation(fields: [order_id], references: [order_id], onDelete: NoAction, onUpdate: NoAction, map: "order_shipping_ibfk_1")

  @@index([order_id], map: "idx_order_id")
  @@index([shipping_status], map: "idx_shipping_status")
  @@index([shipping_type], map: "idx_shipping_type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model orders {
  order_id                   Int                      @id @default(autoincrement())
  user_id                    Int
  order_no                   String                   @unique(map: "uk_order_number") @db.VarChar(50)
  region                     String                   @db.VarChar(3)
  status                     order_status             
  type                       order_type
  total_amount               Decimal                  @db.Decimal(12, 2)
  sub_total                  Decimal                  @db.Decimal(12, 2)
  shipping_fee               Decimal                  @db.Decimal(12, 2)
  tax_amount                 Decimal                  @db.Decimal(12, 2)
  discount_amount            Decimal                  @db.Decimal(12, 2)
  coupon_code                String?                  @db.VarChar(50)
  coupon_discount            Decimal?                 @db.Decimal(12, 2)
  currency                   String                   @default("HKD") @db.VarChar(3)
  payment_method             String?                  @db.VarChar(50)
  payment_platform           String?                  @db.VarChar(100)
  admin_note                 String?                  @db.Text
  customer_note              String?                  @db.Text
  labpay_create_time         Int?
  labpay_end_time            Int?
  labpay_order_amount        Int?
  labpay_txn_state           String?                  @db.VarChar(20)
  labpay_txn_ref_no          String?                  @db.VarChar(255)
  labpay_return_url          String?                  @db.Text
  labpay_type                String?                  @db.VarChar(20)
  labpay_trade_info          Json?
  order_contact_name         String?                  @db.VarChar(50)
  order_contact_email        String?                  @db.VarChar(50)
  order_contact_country_code String?                  @db.VarChar(10)
  order_contact_tel          String?                  @db.VarChar(20)
  order_billing_name         String?                  @db.VarChar(50)
  order_billing_address      String?                  @db.VarChar(255)
  order_billing_country_code String?                  @db.VarChar(10)
  order_billing_tel          String?                  @db.VarChar(20)
  cancel_at                  Int?
  complete_at                Int?
  expire_at                  Int
  created_at                 Int
  updated_at                 Int
  order_items                order_items[]
  order_shipping             order_shipping?
  User                       User                     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "orders_ibfk_1")
  product_inventory_hold     product_inventory_hold[]

  @@index([order_no], map: "idx_order_no")
  @@index([region], map: "idx_region")
  @@index([status], map: "idx_status")
  @@index([user_id], map: "idx_user_id")
}

enum order_status {
  inProgress
  complete
  cancel
}

enum order_type {
  product
  ticket
}

enum order_shipping_status {
  pending
  delivered
}

enum order_shipping_type {
  storePickup
  express
}