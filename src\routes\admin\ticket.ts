import { Router } from 'express';
import * as ticketController from '@modules/ticket/ticket.controller';

const router = Router();

router.post('/', ticketController.createTicketSetting);
router.put('/:id', ticketController.updateTicketSetting);

router.post('/section', ticketController.createTicketSection);

router.post('/variation', ticketController.createTicketVariation);
router.put('/variation/:id', ticketController.updateTicketVariation);

router.post('/type', ticketController.createTicketType);
router.put('/type/:id', ticketController.updateTicketType);

router.post('/export', ticketController.exportTicket);

export default router;
