import { Router } from 'express';
import * as productController from '@modules/product/product.controller';

const router = Router();

router.get('/', productController.getAllProducts);
router.get('/sort-fields', productController.getProductsSearchSortFields);
router.get('/search-criteria', productController.getProductSearchCriteria);
router.get('/related-products/:id', productController.getRelatedProductsById);
router.get('/:id', productController.getProductById);

export default router;
