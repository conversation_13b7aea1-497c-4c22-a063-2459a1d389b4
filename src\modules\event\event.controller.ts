import { Request, Response, Router } from 'express';
import _ from 'lodash';
import { handleAsync, jwtMiddleware, validateRequest } from '@middleware';
import * as eventService from '@modules/event/event.service';
import { CreateEventDto, UpdateEventDto, UpdateEventStatusDto } from './event.dto';
import { requestSchema } from '@/shared/schema/request.schema';

import * as eventSchema from './event.dto';
import { UPLOAD_IMAGE_FILE_CATEGORY, uploader } from '@/middleware/file';
import { sendSuccessResponse } from '@/utils/apiResponse';

// admin only
// create event
export const createEvent = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(eventSchema.CreateEventDto)),
  handleAsync(async (req, res) => {
    const {
      body,
    } = req

    await eventService.createEvent(body);
    return sendSuccessResponse(res, {});
  }),
];

// update event
export const updateEvent = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(eventSchema.UpdateEventDto)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req
    const { id } = params;

    await eventService.updateEvent(Number(id), body);
    return sendSuccessResponse(res, {});
  }),
];

// change event status
export const updateEventStatus = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(eventSchema.UpdateEventStatusDto)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req
    const { id } = params;

    await eventService.updateEventStatus(Number(id), body);
    return sendSuccessResponse(res, {});
  }),
];

export const updateEventMedia = [
  // jwtMiddleware,
  uploader.array(UPLOAD_IMAGE_FILE_CATEGORY.EVENT, 5),
  handleAsync(async (req, res) => {
    const {
      files,
      body
    } = req
    const { id } = req.params;
    await eventService.updateEventMedias(Number(id), body, files);
    return sendSuccessResponse(res, {});
  }),
];

export const updateEventSetting = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(eventSchema.UpdateEventSetting)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req
    const { id } = params;
    await eventService.updateEventSetting(Number(id), body.content);
    return sendSuccessResponse(res, {});
  }),
]

export const updateEventQaa = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(eventSchema.UpdateEventQaaDto)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req

    const { id } = params;
    await eventService.updateEventQaa(Number(id), body);
    return sendSuccessResponse(res, {});
  }),
]

export const updateEventTerms = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(eventSchema.UpdateEventTerms)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req

    const { id } = params;
    await eventService.updateEventTerms(Number(id), body);
    return sendSuccessResponse(res, {});
  }),
]

// public
export const listEvent = [
  validateRequest(requestSchema.query(eventSchema.ListEvent)),
  handleAsync(async (req, res) => {
    const {
      query
    } = req.validatedData
    const events = await eventService.listEvent(query);
    return sendSuccessResponse(res, { events });
  }),
]

export const getEvent = [
  validateRequest(requestSchema.params(eventSchema.GetEvent)),
  handleAsync(async (req, res) => {
    const {
      params
    } = req.validatedData
    const event = await eventService.getEvent(params);
    return sendSuccessResponse(res, { event });
  }),
]