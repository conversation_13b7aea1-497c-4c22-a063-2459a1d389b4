import { Queue, Worker } from 'bullmq';
import { redis } from './redis';
import { NOTIFICATION_QUEUE_TOPIC } from './constant';
import db from '@prismaClient';
import { readdir, readFileSync } from 'node:fs';
import Mustache from "mustache";
import { RECEIPT_CONTENT } from '@/shared/constants/email';
import dayjs from 'dayjs';
import { sendHTMLMail } from '@/utils/mail';

export class OrderMQ {
    private queue: any;
    private worker: any;

    constructor() {
        this.initWorker()
        console.info("[setup] inited order worker")
    }

    initWorker() {
        this.worker = new Worker(
            'order',
            async job => {
                console.log("job >> ", job.name)
                console.log("received event >> ", job.data)
                switch (job.name) {
                    case NOTIFICATION_QUEUE_TOPIC.ORDER_COMPLETED:
                        console.info(`[complete order] >> order id: ${job.data?.orderId}`)
                        await this.completeOrder(job.data.orderId)
                        break;
                    default:
                        // TODO: do sth
                        break;
                }
            },
            { connection: redis },
        );
    }

    async completeOrder(orderId: number) {
        try {
            const orderInfo = await db.orders.findUnique({
                select: {
                    User: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            preference_language: true,
                        }
                    },
                    order_no: true,
                    total_amount: true,
                    tax_amount: true,
                    currency: true,
                    created_at: true,
                    order_items: {
                        select: {
                            product_sku: true,
                            products: {
                                select: {
                                    event: true,
                                    product_translations: true
                                }
                            },
                            quantity: true,
                            tickets: {
                                select: {
                                    ticket_type: {
                                        select: {
                                            name: true,
                                            price: true,
                                            ticket_setting: {
                                                select: {
                                                    event_info: {
                                                        select: {
                                                            name: true,
                                                            start_date: true,
                                                            start_time: true,
                                                            end_date: true,
                                                            end_time: true,
                                                            region_code: true,
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                where: {
                    order_id: orderId
                }
            })
            if (!orderInfo) {
                console.error(`[complete order] >> ${orderId} not found`)
                return;
            }

            const {
                User,
                order_no,
                created_at,
                total_amount,
                currency,
                order_items = [],
            } = orderInfo

            let event: any = undefined;
            let eventPeriod: number[] = []

            const {
                preference_language,
                email,
                id
            } = User

            if (!email) {
                console.error(`[complete order error] >> user: ${id} not email`)
                return;
            }

            const LANG = (preference_language || 'EN').toUpperCase()

            const items: {
                itemName: string,
                quantity: number| string,
                price: string,
                subTotal: string
            }[] = []
            
            order_items.map((item) => {
                const {
                    products,
                    product_sku,
                    tickets,
                    quantity
                } = item
                
                if (products) {
                    if (!event) event = products.event
                    const productTranslation = products.product_translations.find((item) => item.language_code === LANG)
                    items.push({
                        itemName: productTranslation?.name ?? "",
                        quantity,
                        price: `${currency} ${product_sku?.sale_price ?? 0}`,
                        subTotal: `${currency} ${quantity * Number(product_sku?.sale_price)}`
                    })
                }
                if (tickets?.length > 0) {
                    if (!event) event = order_items[0]?.tickets?.[0]?.ticket_type?.ticket_setting?.event_info
                    const ticketDetail = tickets[0].ticket_type
                    items.push({
                        itemName: ticketDetail.name,
                        quantity,
                        price: `${currency} ${ticketDetail?.price ?? 0}`,
                        subTotal: `${currency} ${quantity * (ticketDetail?.price ?? 0)}`
                    })
                }
            })

            if (event) {
                const {
                    start_date = 0,
                    start_time = 0,
                    end_date = 0,
                    end_time = 0
                } = event

                eventPeriod = [
                    Number(start_date)+ Number(start_time),
                    Number(end_date)+ Number(end_time)
                ]
            }

            const STATIC_CONTENT = RECEIPT_CONTENT?.[LANG] ?? RECEIPT_CONTENT['en']
            
            const template = await readFileSync('./src/shared/template/receipt.html')
            const templateContent = template.toString()
            const htmlContent = Mustache.render(templateContent, {
                ...STATIC_CONTENT,
                orderNo: order_no,
                eventName: event.name,
                createdAt: dayjs.unix(created_at).utc(),
                reminder: `${
                    eventPeriod.filter(item => item > 0)
                    .map((unixTimestamp) => dayjs.unix(unixTimestamp).utc()).join(" - ")
                }`,
                total: total_amount,
                items
            })
            await sendHTMLMail(
                email,
                `[incutix] - No.${order_no}`,
                htmlContent,
            )

        } catch(err) {
            console.error(`[complete order error] >> `, err)
        }
    }
}