/*
  Warnings:

  - The primary key for the `events` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `description` on the `events` table. All the data in the column will be lost.
  - You are about to drop the column `event_name` on the `events` table. All the data in the column will be lost.
  - You are about to drop the column `events_id` on the `events` table. All the data in the column will be lost.
  - Added the required column `event_id` to the `events` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX `uk_event_name` ON `events`;

-- AlterTable
ALTER TABLE `events` DROP PRIMARY KEY,
    DROP COLUMN `description`,
    DROP COLUMN `event_name`,
    DROP COLUMN `events_id`,
    ADD COLUMN `event_id` INTEGER NOT NULL AUTO_INCREMENT,
    ADD PRIMARY KEY (`event_id`);
