import { OrderCheckStrategy } from '../policies/abstractions/orderCheck.interface';

export enum UseCase {
  updateOrderShippingStatus = 'updateOrderShippingStatus',
  batchUpdateOrderShippingStatus = 'batchUpdateOrderShippingStatus',
  updateOrderStatus = 'updateOrderStatus',
  updateOrderAdminNote = 'updateOrderAdminNote',
}
export type StrategyMap<T> = {
  [key in UseCase]: OrderCheckStrategy<T>[];
};
