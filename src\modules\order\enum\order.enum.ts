import { $Enums } from '@prisma/client';

export const PrismaOrderType = $Enums.order_type;
export const PrismaOrderStatus = $Enums.order_status;
export const PrismaOrderShippingStatus = $Enums.order_shipping_status;
export const PrismaOrderShippingType = $Enums.order_shipping_type;

export type OrderType = typeof PrismaOrderType.product | typeof PrismaOrderType.ticket;
export type OrderStatus =
  | typeof PrismaOrderStatus.cancel
  | typeof PrismaOrderStatus.complete
  | typeof PrismaOrderStatus.inProgress;
export type OrderShippingStatus =
  | typeof PrismaOrderShippingStatus.delivered
  | typeof PrismaOrderShippingStatus.pending;
export type OrderShippingType =
  | typeof PrismaOrderShippingType.express
  | typeof PrismaOrderShippingType.storePickup;
