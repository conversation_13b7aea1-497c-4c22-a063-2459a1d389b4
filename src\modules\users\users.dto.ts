import _ from 'lodash';
import { z } from 'zod';
import { GenderDto } from '../../models/commonDto';

const FIELD_SEP = '\n';

const BaseDto = z.object({
  identity: z.enum(['personal', 'company']).default('personal'),
  name: z.string().nullish(),
  gender: GenderDto.nullish(),
  phoneNumber: z.string().nullish(),
  countryCode: z.string().nullish(),
  photoUrl: z.string().nullish(),
  receivePromotions: z.boolean(),
});

export const UpdateDto = BaseDto.extend({
  dateOfBirth: z
    .string()
    .transform((date) => new Date(date))
    .nullish(),
  address: z
    .array(z.string())
    .transform((arr) => arr.join(FIELD_SEP))
    .nullish(),
  socialUrls: z
    .record(
      z.object({
        url: z.string(),
      }),
    )
    .default({})
    .transform((obj) =>
      Object.keys(obj).map((method) => ({
        url: obj[method].url,
        method,
      })),
    ),
  contacts: z
    .record(
      z.object({
        value: z.string(),
      }),
    )
    .default({})
    .transform((obj) =>
      Object.keys(obj).map((method) => ({
        value: obj[method].value,
        method,
      })),
    ),
});

export const GetOneDto = BaseDto.extend({
  email: z.string().email(),
  dateOfBirth: z.date().nullish(),
  address: z
    .string()
    .nullish()
    .transform((str) => str?.split(FIELD_SEP)),
  socialUrls: z
    .array(
      z.object({
        url: z.string(),
        method: z.string(),
      }),
    )
    .default([])
    .transform((arr) =>
      arr.reduce((acc: Record<string, any>, cur) => {
        acc[cur.method] = _.pick(cur, ['url']);
        return acc;
      }, {}),
    ),
  contacts: z
    .array(
      z.object({
        value: z.string(),
        method: z.string(),
      }),
    )
    .default([])
    .transform((arr) =>
      arr.reduce((acc: Record<string, any>, cur) => {
        acc[cur.method] = _.pick(cur, ['value']);
        return acc;
      }, {}),
    ),
  role: z.string().nullish(),
});
