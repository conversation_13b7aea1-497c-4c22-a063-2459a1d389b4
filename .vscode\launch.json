{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug with No<PERSON><PERSON>",
      "program": "${workspaceFolder}\\src\\server.ts",
      "runtimeExecutable": "nodemon", // 如果用 nodemon 需修改
      "restart": true,
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"],
      "outputCapture": "std"
    }
  ]
}
