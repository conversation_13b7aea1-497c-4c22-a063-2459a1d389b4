import { z } from 'zod';
import { PaymentType } from '../Enum/payment.enum';
import { languageQuerySchema } from '@/shared/schema/request.schema';

export interface PreCreateOrderBody {
  body?: any;
  feeType: string;
  locale: string;
  orderNo: string;
  paymentMethod: string;
  paymentPlatform: string;
  paymentType: PaymentType;
  totalFee: number;
  orderId: number;
}

const preCreateOrderSchema = z.object({
  lab_merchant_app_id: z.string(),
  body: z.unknown().optional(),
  fee_type: z.string(),
  lab_merchant_id: z.string(),
  nonce_str: z.string(),
  merchant_order_no: z.string(),
  payment_platform: z.string(),
  payment_type: z.enum(['QRCODE', 'MOBILEWEB', 'WEB', 'REDIRECT']).nullish(),
  sign: z.string(),
  total_fee: z.number(),
  notify_url: z.string(),
});

const wechatPayPreCheckoutType = preCreateOrderSchema.merge(
  z.object({
    expiry_period: z.number().optional(),
    payment_platform_wallet_region: z.string().optional(),
  }),
);
const aliPayPreCheckoutType = preCreateOrderSchema.merge(
  z.object({
    expiry_period: z.number().optional(),
    payment_platform_wallet_region: z.string().optional(),
  }),
);
const cardPreCheckout = preCreateOrderSchema.merge(
  z.object({
    return_url: z.string().optional(),
    locale: z.string().optional(),
    payment_channels: z.object({ payment_channel: z.string() }).array().optional(),
    is_with_billing: z.boolean().optional(),
  }),
);
export type WechatPayPreCheckoutType = z.infer<typeof wechatPayPreCheckoutType>;
export type AliPayPreCheckoutType = z.infer<typeof aliPayPreCheckoutType>;
export type CardPreCheckout = z.infer<typeof cardPreCheckout>;
export type LabPayCreatePreOrderBodyType =
  | WechatPayPreCheckoutType
  | AliPayPreCheckoutType
  | CardPreCheckout
  | undefined
  | null;

export const LabPayNotificationBodySchema = z.object({
  data: z.string(),
  data_type: z.string(),
  fee_type: z.string(),
  lab_merchant_app_id: z.string(),
  lab_merchant_id: z.string(),
  merchant_order_no: z.string(),
  nonce_str: z.string(),
  payload_type: z.string(),
  payment_platform: z.string(),
  payment_type: z.string(),
  response_code: z.string(),
  response_desc: z.string(),
  time_create: z.string(),
  time_expire: z.string().optional(),
  total_fee: z.string(),
  txn_ref_no: z.string(),
  txn_state: z.string(),
  txn_state_desc: z.string(),
  trade_info: z.string(),
  
  total_refund: z.string().optional(),
  sign: z.string().optional(),
  time_end: z.string().optional(),
});
export type LabPayNotificationBodyType = z.infer<typeof LabPayNotificationBodySchema>;

export const retrieveLabPayOrderQuerySchema = z
  .object({
    orderId: z.string().transform((x) => Number(x)),
  })
  .merge(languageQuerySchema);

export type RetrieveLabPayOrderQueryType = z.infer<typeof retrieveLabPayOrderQuerySchema>;
