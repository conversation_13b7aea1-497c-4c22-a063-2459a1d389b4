/*
  Warnings:

  - You are about to drop the column `collection_id` on the `products` table. All the data in the column will be lost.
  - You are about to drop the `collections` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `event_id` to the `products` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `product_translations` DROP FOREIGN KEY `product_translations_ibfk_1`;

-- DropIndex
DROP INDEX `idx_collection_id` ON `products`;

-- AlterTable
ALTER TABLE `products` DROP COLUMN `collection_id`,
    ADD COLUMN `event_id` INTEGER NOT NULL;

-- DropTable
DROP TABLE `collections`;

-- CreateIndex
CREATE INDEX `idx_event_id` ON `products`(`event_id`);

-- AddForeignKey
ALTER TABLE `product_translations` ADD CONSTRAINT `product_translations_product_id_fkey` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `products` ADD CONSTRAINT `products_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `events`(`event_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
