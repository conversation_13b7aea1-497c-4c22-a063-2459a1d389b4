import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { CartItemStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import { PrismaProductStatus } from '@modules/product/enums/product.enum';

type ParameterType = CartItemStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const skuValidateStrategy: Strategy = async (parameter: ParameterType) => {
  const countSku = await db.product_sku.count({
    where: {
      product_id: parameter.productId,
      sku_id: parameter.skuId,
      products: { status: PrismaProductStatus.listed },
    },
  });
  if (countSku <= 0) {
    throw new CustomErrorException(errorCodeTable.cartAddItemSkuNotValidate, {
      productId: parameter.productId,
      skuId: parameter.skuId,
    });
  }
};
