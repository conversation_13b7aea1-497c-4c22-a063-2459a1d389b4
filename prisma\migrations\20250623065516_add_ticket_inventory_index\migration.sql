/*
  Warnings:

  - A unique constraint covering the columns `[timestamp,ticket_section_id]` on the table `ticket_date_inventory` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX `ticket_distribution_ticket_section_id_fkey` ON `ticket_distribution`;

-- CreateIndex
CREATE UNIQUE INDEX `ticket_date_inventory_timestamp_ticket_section_id_key` ON `ticket_date_inventory`(`timestamp`, `ticket_section_id`);

-- Add<PERSON>oreignKey
ALTER TABLE `ticket_distribution` ADD CONSTRAINT `ticket_distribution_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_distribution` ADD CONSTRAINT `ticket_distribution_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
