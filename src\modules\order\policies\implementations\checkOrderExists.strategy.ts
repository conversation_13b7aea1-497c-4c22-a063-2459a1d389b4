import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { OrderCheckStrategyParameterType } from '@modules/order/schema/strategy.schema';
import { OrderCheckStrategy } from '@modules/order/policies/abstractions/orderCheck.interface';

type ParameterType = OrderCheckStrategyParameterType;
type Strategy = OrderCheckStrategy<ParameterType>;

export const checkOrderExistStrategy: Strategy = async (parameter: ParameterType) => {
  const { orderId } = parameter;

  const countOrder = await db.orders.count({
    where: { order_id: orderId },
  });

  if (countOrder === 0) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingOrderIdNotExists);
  }
};
