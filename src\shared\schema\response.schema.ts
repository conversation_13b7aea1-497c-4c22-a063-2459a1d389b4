// schemas/response.schema.ts
import { z } from 'zod';

// Basic response
export const BaseApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z
    .object({
      code: z.string(),
      details: z.unknown().optional(),
    })
    .optional(),
  meta: z
    .object({
      page: z.number().optional(),
      size: z.number().optional(),
      total: z.number().optional(),
    })
    .optional(),
});

// Success Response
export const SuccessApiResponseSchema = BaseApiResponseSchema.extend({
  success: z.literal(true),
  data: z.unknown(),
});

// Error Response
export const ErrorApiResponseSchema = BaseApiResponseSchema.extend({
  success: z.literal(false),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.unknown().optional(),
  }),
});
