// server-wrapper.js - Wraps the serverless handler for traditional HTTP server
const { handler } = require('./dist/handler');
const http = require('http');
const PORT = process.env.PORT || 5000;

console.log('Starting server wrapper on port', PORT);

// Create HTTP server
const server = http.createServer(async (req, res) => {
  try {
    // Prepare event object similar to what API Gateway would send to Lambda
    const event = {
      httpMethod: req.method,
      path: req.url,
      headers: req.headers,
      queryStringParameters: {},
      body: '',
      isBase64Encoded: false
    };

    // Parse query parameters
    const urlParts = req.url.split('?');
    if (urlParts.length > 1) {
      const queryParams = new URLSearchParams(urlParts[1]);
      queryParams.forEach((value, key) => {
        event.queryStringParameters[key] = value;
      });
    }

    // Collect request body if present
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const chunks = [];
      for await (const chunk of req) {
        chunks.push(chunk);
      }
      event.body = Buffer.concat(chunks).toString();
    }

    // Call the Lambda handler
    const result = await handler(event, {});

    // Set status code
    res.statusCode = result.statusCode;

    // Set headers
    Object.entries(result.headers || {}).forEach(([key, value]) => {
      res.setHeader(key, value);
    });

    // Send response
    res.end(result.body);
  } catch (error) {
    console.error('Error handling request:', error);
    res.statusCode = 500;
    res.end(JSON.stringify({ error: 'Internal Server Error' }));
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});