import { OrderCheckStrategy } from '@modules/order/policies/abstractions/orderCheck.interface';
import { OrderCheckStrategyParameterType } from '@modules/order/schema/strategy.schema';

import { checkOrderExistStrategy } from '../implementations';

type ParameterType = OrderCheckStrategyParameterType;
export const updateOrderShippingStatusStrategyMap: OrderCheckStrategy<ParameterType>[] = [
  checkOrderExistStrategy,
];
