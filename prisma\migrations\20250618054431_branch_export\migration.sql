/*
  Warnings:

  - Added the required column `branch_id` to the `ticket` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `ticket` DROP FOREIGN KEY `ticket_order_item_id_fkey`;

-- DropForeignKey
ALTER TABLE `ticket` DROP FOREIGN KEY `ticket_user_id_fkey`;

-- AlterTable
ALTER TABLE `ticket` ADD COLUMN `branch_id` INTEGER NOT NULL,
    MODIFY `user_id` INTEGER NULL,
    MODIFY `order_item_id` INTEGER NULL;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_branch_id_fkey` FOREIGN KEY (`branch_id`) REFERENCES `ticket_distribution`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_order_item_id_fkey` FOREIGN KEY (`order_item_id`) REFERENCES `order_items`(`order_item_id`) ON DELETE SET NULL ON UPDATE CASCADE;
