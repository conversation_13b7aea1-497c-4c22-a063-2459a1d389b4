/*
import { Prisma, User } from '@prisma/client';
import { copyTmpObject, deleteObjectByUrl, deleteTmpObjects, moveTmpObject } from '../../utils/s3';
import db from '@prismaClient';
import { z } from 'zod';
import { UpdateDto } from './users.dto';
import _ from 'lodash';
import { logger } from '../../utils/logger';

export const writeFields = [
  'name',
  'gender',
  'phoneNumber',
  'dateOfBirth',
  'countryCode',
  'address',
  'photoUrl',
  'receivePromotions',
  'socialUrls',
];

export const readFields = [...writeFields, 'email'];

// async function updateById(id: number, data: z.infer<typeof UpdateDto>) {
//     // move tmp photo to user folder
//     if (data?.photoUrl) {
//         const user = await db.user.findFirstOrThrow({ where: { id } })

//         if (user.photoUrl && user.photoUrl !== data.photoUrl && data.photoUrl.includes('/tmp')) {
//             try {
//                 data.photoUrl = await copyTmpObject(data.photoUrl, id);
//             } catch (error) {
//                 logger.error(error)
//             }
//         }
//     }

//     const socialIdsToDelete = await db.socialURL.findMany({
//         where: {
//             userId: id, method: {
//                 notIn: data.socialUrls.map((socialUrl) => socialUrl.method)
//             }
//         }
//     })
//     const contactIdsToDelete = await db.contact.findMany({
//         where: {
//             userId: id, method: {
//                 notIn: data.contacts.map((contact) => contact.method)
//             }
//         }
//     })

//     const userData: Prisma.UserUpdateInput = {
//         ..._.omit(data, ['socialUrls', 'contacts']),
//         // socialUrls: {
//         //     upsert: data.socialUrls.map((socialUrl) => ({
//         //         where: {
//         //             userId_method: {
//         //                 userId: id,
//         //                 method: socialUrl.method
//         //             }
//         //         },
//         //         update: {
//         //             url: socialUrl.url,
//         //         },
//         //         create: {
//         //             url: socialUrl.url,
//         //             method: socialUrl.method
//         //         }
//         //     })),
//         //     deleteMany: socialIdsToDelete
//         // },
//         contacts: {
//             upsert: data.contacts.map((contact) => ({
//                 where: {
//                     userId_method: {
//                         userId: id,
//                         method: contact.method
//                     }
//                 },
//                 update: {
//                     value: contact.value
//                 },
//                 create: {
//                     value: contact.value,
//                     method: contact.method
//                 }
//             })),
//             deleteMany: contactIdsToDelete
//         }
//     }

//     const user = await db.user.update({
//         where: {
//             id
//         },
//         data: userData
//     })

//     try {
//         await deleteTmpObjects([data.photoUrl || ''])
//     } catch (error) {
//         logger.error(error)
//     }

//     return user
// }

function update(email: string, data: any) {
  return db.user.update({
    where: {
      email,
    },
    data,
  });
}

function findOne(email: string, opt?: { [x: string]: any }) {
  return db.user.findUnique({ where: { email, ...opt } });
}

// function findOneBy(where: any) {
//     return db.user.findFirstOrThrow({
//         where,
//         include: {
//             socialUrls: true,
//             contacts: true
//         }
//     });
// }

function findOneBySafe(field: string, value: string | number | boolean) {
  return db.user.findFirstOrThrow({
    where: {
      [field]: value,
    },
    select: readFields.reduce((acc, field) => ({ ...acc, [field]: true }), {}),
  });
}

export enum Role {
  Creator = 'creator',
  Visitor = 'visitor',
}

function create(email: string, role: Role, password: string, verificationCode: number) {
  return db.user.create({
    data: {
      email,
      password,
      verificationCode,
      role,
    },
  });
}

const createBySocialMedia = (
  email: string,
  opt: { supportGoogle?: boolean; supportFacebook?: boolean },
) => {
  return db.user.create({
    data: {
      email,
      password: '',
      role: 'visitor',
      isActive: true,
      ...opt,
    },
  });
};

export default {
  findOne,
  // findOneBy,
  findOneBySafe,
  create,
  createBySocialMedia,
  update,
  // updateById
};
*/