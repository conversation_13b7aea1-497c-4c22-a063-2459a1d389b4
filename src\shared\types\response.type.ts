// Basic API Response
export interface BaseApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message?: string;
    details?: string[];
  };
  meta?: {
    page?: number;
    size?: number;
    total?: number;
  };
}

// Success Response
export type SuccessApiResponse<T> = BaseApiResponse<T> & {
  success: true;
  data: T;
};

// Error Response
export type ErrorApiResponse = BaseApiResponse & {
  success: false;
  error: {
    code: string;
    message?: string;
    details?: unknown;
  };
};
