import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkPriceStrategy: Strategy = async (parameter: ParameterType) => {
  const { sku } = parameter as CreateProductRequestType;

  sku.forEach(({ originalPrice, salePrice }) => {
    if (originalPrice < salePrice) {
      throw new CustomErrorException(errorCodeTable.addProductPrice);
    }
  });
};
