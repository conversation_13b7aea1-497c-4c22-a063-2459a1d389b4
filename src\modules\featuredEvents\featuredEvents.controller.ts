// import { handleAsync } from '@middleware';
// import featuredEventsService from '@modules/featuredEvents/featuredEvents.service';
// import { FeaturedEventDTO } from '@modules/featuredEvents/featuredEvents.dto';
// import { JwtDto } from '@models/commonDto';

// export const getAllFeatureEvents = handleAsync(async (req, res) => {
//   try {
//     const { region, category, sortOrder, page } = req.query;

//     const result = await featuredEventsService.findAll(region, category, sortOrder, page);

//     res.send(result);
//   } catch (err) {
//     return res.status(500).send({ error: 'Internal Server Error' });
//   }
// });

// export const getFeatureEventById = handleAsync(async (req, res) => {
//   const { id } = req.params;

//   const idNum = Number(id);

//   const result = await featuredEventsService.findById(idNum);

//   res.status(200).json(result);
// });

// export const createFeatureEvent = handleAsync(async (req, res) => {
//   const featuredEventDTO: FeaturedEventDTO = req.body;

//   const user = JwtDto.parse(req.user);

//   const result = await featuredEventsService.create(user.sub, featuredEventDTO);

//   res.status(200).json(result);
// });
