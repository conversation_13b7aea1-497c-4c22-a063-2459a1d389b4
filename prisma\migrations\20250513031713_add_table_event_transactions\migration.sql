-- CreateTable
CREATE TABLE `event_translations` (
    `event_translation_id` INTEGER NOT NULL AUTO_INCREMENT,
    `event_id` INTEGER NOT NULL,
    `language_code` ENUM('EN', 'TC', 'TS') NULL DEFAULT 'EN',
    `name` VARCHAR(255) NOT NULL,
    `description` LONGTEXT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_language_code`(`language_code`),
    INDEX `idx_event_id`(`event_id`),
    UNIQUE INDEX `uk_event_language`(`event_id`, `language_code`),
    PRIMARY KEY (`event_translation_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `event_translations` ADD CONSTRAINT `event_translations_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `events`(`event_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
