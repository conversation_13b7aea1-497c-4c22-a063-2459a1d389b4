import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { CartItemStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import { PrismaProductStatus } from '@modules/product/enums/product.enum';

type ParameterType = CartItemStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkProductListedStrategy: Strategy = async (parameter: ParameterType) => {
  const countProductId = await db.products.count({
    where: { product_id: parameter.productId, status: PrismaProductStatus.listed },
  });
  if (countProductId <= 0) {
    throw new CustomErrorException(errorCodeTable.cartAddItemProductNoListed, {
      productId: parameter.productId,
    });
  }
};