import { Request, Response } from 'express';
import { NextFunction, ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';

export function handleAsync(middleware: (...args: any[]) => Promise<any>) {
  return async function (req: Request, res: Response, next: NextFunction) {
    try {
      await middleware(req, res, next);
    } catch (err) {
      next(err);
    }
  };
}
