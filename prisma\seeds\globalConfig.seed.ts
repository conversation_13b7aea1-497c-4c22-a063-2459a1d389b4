import db from '@prismaClient';
import { generateTimeNow } from '@/utils/common';

const data = [
  {
    group: 'shipping',
    key: 'express_shipping_fee',
    value: '50',
  },
];

// Seed for LabPay payment
export default async () => {
  const now = generateTimeNow();

  await db.global_config.createMany({
    data: data.map((x) => ({
      config_group: x.group,
      config_key: x.key,
      config_value: x.value,
      is_active: true,
      created_at: now,
      updated_at: now,
    })),
  });

  console.log('[Prisma] Seed payment method insert progress.');
};
