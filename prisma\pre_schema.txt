// since the prisma is out-sync from the actual db
// create this file to record the schema first
// in order to modify it
// TODO:: make the prisma schema sync with the MySQL db

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "linux-musl-openssl-3.0.x"]
}

model User {
  id                   Int       @id @default(autoincrement())
  email                String    @unique
  password             String
  name                 String?
  verificationCode     Int?
  resetPasswordToken   String?
  resetPasswordExpires DateTime?
  isActive             Boolean   @default(false)
  gender               Gender?
  phoneNumber          String?
  dateOfBirth          DateTime?
  countryCode          String?   @db.VarChar(2)
  address              String?
  photoUrl             String?
  receivePromotions    Boolean   @default(false)
  identity             String    @default("personal")
  role                 Role
  supportGoogle        Boolean?
  supportFacebook      Boolean?

  // fields used only by User, cascadable
  contacts   Contact[]
  socialUrls SocialURL[]
  addressBook  Address[]
  votes      Voting[]

  // fields used by others, no cascade
  tags              Tag[]
  productCategories ProductCategory[]
  memberGroups      MemberGroup[]
  rooms             Room[]
  owners            Owner[]
  collections       Collection[]

  @@index([email], name: "email_idx")
  @@index([name], name: "name_idx")
}

model Contact {
  value  String
  userId Int
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  method String

  @@id([userId, method])
}

model SocialURL {
  url    String
  userId Int
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  method String

  @@id([userId, method])
}

model OwnerSocialUrl {
  url     String
  ownerId Int
  owner   Owner  @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  method  String

  @@id([ownerId, method])
}

enum Gender {
  male
  female
  transgender_female
  transgender_male
  non_binary
  agender
  not_listed
  not_to_state
}

enum Role {
  creator
  visitor
}

enum PaymentPlatform {
  WeChatPay
  AliPay
  Airwallex
}

enum PaymentType {
  QRCODE
  MOBILEWEB
  WEB
  REDIRECT
}

enum Currency {
  HKD
  USD
}

enum TransactionStatus {
  pending
  success
  cancel
  refund
}

enum DeliveryStatus {
  pending
  delivered
  cancelled
}

model Room {
  id               Int           @id @default(autoincrement())
  roomName         String
  roomUrl          String
  description      String?
  tags             Tag[]
  logoUrl          String?
  thumbnailUrl     String?
  bgMusicUrl       String?
  visibleToPublic  Boolean
  memberGroups     MemberGroup[]
  openToAllMembers Boolean

  // fields used only by User, cascadable
  roomContacts     RoomContact[]
  roomSocialUrls   RoomSocialUrl[]

  VotingConfigs    VotingConfig[]

  userId    Int
  createdBy User @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
}

model RoomContact {
  value  String
  roomId Int
  room   Room   @relation(fields: [roomId], references: [id], onDelete: Cascade)
  method String

  @@id([roomId, method])
}

model RoomSocialUrl {
  value  String
  roomId Int
  room   Room   @relation(fields: [roomId], references: [id], onDelete: Cascade)
  method String

  @@id([roomId, method])
}

model RoomMember {
  id               Int           @id @default(autoincrement())
  creatorId        Int
  userId           Int

  createdDate      DateTime @default(now())
  updatedDate      DateTime

}

model MemberToGroup {
  id               Int           @id @default(autoincrement())
  creatorId        Int
  userId           Int

  memberGroupId    Int

  createdDate      DateTime @default(now())
  updatedDate      DateTime

}

model Product {
  id               Int           @id @default(autoincrement())
  name             String
  description      String?
  thumbnail        String
  audio            String
  owner            Int
  createdAt        Int
  updatedAt        Int?
  status           Int
  price            Decimal @db.Decimal(9,2)
  tax              Decimal @db.Decimal(9,2)
  type             Int
  currency         String
  weight           Decimal @db.Decimal(9,4)
  weightUnit       String
  isDigital        Boolean

  productWarehouses ProductWarehouse[]
}

model ProductWarehouse {
  id               Int           @id @default(autoincrement())
  productId        Int
  product        Product       @relation(fields: [productId], references: [id], onDelete: Cascade)
  quantity         Int
  createdAt      Int
  updatedAt      Int?
}

model WarehouseHistory {
  id                     Int           @id @default(autoincrement())
  productWarehouseId     Int
  beforeQuantity         Int
  afterQuantity          Int
  modifiedBy             Int
  createdDate      Int
  updatedDate      Int?
}

model Order {
  id               Int           @id @default(autoincrement())
  orderNo          String
  paymentPlatform  Int
  paymentType      Int
  paymentValid     Int
  amount           Decimal @db.Decimal(9,2)
  currency         Currency
  type             Int
  description      String
  createdAt        Int
  updatedAt        Int
  status           Int
  clientName       String
  clientEmail      String
  clientContactN   String
  transactionStatus TransactionStatus
  total            Decimal @db.Decimal(9,2)
  deliveryStatus   DeliveryStatus?
  deliveryNoteId   String?
  handlingStaff    String?
  remarks          String?
  billingAddressCountry String?
  billingAddressAddressline1 String?
  billingAddressAddressline2 String?
  billingAddressPostalCode String?
  deliveryAddressCountry String?
  deliveryAddressAddressline1  String?
  deliveryAddressAddressline2  String?
  deliveryAddressPostalCode String?
  grossTotal       Decimal @db.Decimal(9,2)
  promoCode        String?
  promoCodeDiscount Int?
  taxVat           String?
  grandTotal       Decimal @db.Decimal(9,2)
  paymentMethod    String?

  Products         OrderProduct[]
}

model OrderProduct {
  id              Int   @id @default(autoincrement())
  orderId         Int
  productId       Int
  quantity        Int
  price           Int
  currency        Int
  metaData        String

  order           Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model Transaction {
  id               Int           @id @default(autoincrement())
  refNo            Int
  orderNo          String
  url              String
  urlType          String
  status           Int
  orderId          Int
  total            Decimal @db.Decimal(9,2)
  currency         Int
  createdDate      Int
  updatedDate      Int?
}

model AuctionDetail {
  id               Int           @id @default(autoincrement())
  productId        Int
  bidUnit          Decimal @db.Decimal(9,2)
  startDate        Int
  duration         Int
  createdDate      Int
  updatedDate      Int?
}

model AuctionSituation {
  id               Int           @id @default(autoincrement())
  auctionDetailId  Int
  previousPrice    Decimal @db.Decimal(9,2)
  currentPrice     Decimal @db.Decimal(9,2)
  status           Int
  bidBy            Int
  createdDate      Int
  updatedDate      Int?
}

model BidHistory {
  id                 Int           @id @default(autoincrement())
  auctionSituationId Int
  previousPrice      Decimal @db.Decimal(9,2)
  currentPrice       Decimal @db.Decimal(9,2)
  status             Int
  bidBy              Int
  createdDate        Int
  updatedDate        Int?
}

model Tag {
  id    Int    @id @default(autoincrement())
  name  String
  rooms Room[]

  userId    Int
  createdBy User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([name, userId]) // no same tag names created by one user
  @@index([name], name: "name_idx")
}

model MemberGroup {
  id   Int    @id @default(autoincrement())
  name String

  userId    Int
  createdBy User @relation(fields: [userId], references: [id], onDelete: Cascade)

  rooms Room[]
  
  @@unique([name, userId]) // no same group names created by one user
}

model ProductCategory {
  id   Int    @id @default(autoincrement())
  name String

  userId    Int
  createdBy User @relation(fields: [userId], references: [id], onDelete: Cascade)

  parentId Int?
  parent   ProductCategory?  @relation("product_category", fields: [parentId], references: [id], onDelete: Cascade)
  children ProductCategory[] @relation("product_category")

  @@unique([name, userId])
}

model Owner {
  id           Int      @id @default(autoincrement())
  email        String?   @unique
  introduction String?   @db.LongText
  name         String
  gender       Gender?
  countryCode  String?   @db.VarChar(2)
  photoUrl     String?
  createdAt    DateTime @default(now())

  userId    Int
  createdBy User @relation(fields: [userId], references: [id])

  // fields used only by Owner, cascadable
  socialUrls OwnerSocialUrl[]

  @@index([email], name: "email_idx")
  @@index([name], name: "name_idx")
}

model Collection {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  photoUrl    String?  @db.VarChar(255)
  createdAt   DateTime @default(now())
  updatedAt   DateTime?
  
  userId    Int
  createdBy User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Invitation {
  id              Int     @id @default(autoincrement())
  name            String?
  email           String?
  status          Int?
  emailSentDate   DateTime?
  createdAt       DateTime?
  updatedAt       DateTime?
  creatorId       Int?

}

model InvitationToGroup {
  id            Int     @id @default(autoincrement())
  invitationId  Int
  memberGroupId Int
  creatorId     Int
}

model Address {
  id            Int     @id @default(autoincrement())
  userId        Int
  user          User    @relation(fields: [userId], references: [id])
  name          String
  phoneNumber   String
  email         String
  countryCode   String
  location      String
  postalCode    String
  isDefaultBilling     Boolean
  isDefaultDelivery     Boolean
  createdAt     Int
  updatedAt     Int
}

model VotingConfig {
  id            Int     @id @default(autoincrement())
  roomId        Int
  room          Room    @relation(fields: [roomId], references: [id])
  description   String  @db.TinyText
  isKnockoutMode  Boolean
  type          Int
  noOfVote      Int
  maxNoOfVote   Int
  status        Int
  createdAt     Int
  updatedAt     Int

  votingSessions  VotingSession[]
  contestants     Contestant[]
}

model VotingSession {
  id            Int     @id @default(autoincrement())
  votingConfigId  Int
  votingConfig  VotingConfig @relation(fields: [votingConfigId], references: [id])
  title         String
  description   String  @db.TinyText
  isRoot        Boolean
  startAt       Int
  endAt         Int
  createdAt     Int
  updatedAt     Int

  contestantInAdvances ContestantInAdvance[]
  contestantPoster  ContestantPoster[]
  voting  Voting[]
}

model Contestant  {
  id            Int     @id @default(autoincrement())
  votingConfigId  Int
  votingConfig  VotingConfig  @relation(fields: [votingConfigId], references: [id])
  name          String
  description   String  @db.TinyText
  order         Int
  icon          String
  createdAt     Int
  updatedAt     Int

  contestantInAdvances ContestantInAdvance[]
  contestantPoster  ContestantPoster[]
  voting  Voting[]
}

model ContestantInAdvance {
  contestantId  Int
  contestants   Contestant  @relation(fields: [contestantId], references: [id])
  votingSessionId Int
  votingSession   VotingSession @relation(fields: [votingSessionId], references: [id])
  noOfVote  Int

  @@id([contestantId, votingSessionId])
}

model ContestantPoster {
  id            Int     @id @default(autoincrement())
  votingSessionId Int
  votingSession   VotingSession @relation(fields: [votingSessionId], references: [id])
  contestantId    Int
  contestants     Contestant  @relation(fields: [contestantId], references: [id])
  title           String
  description     String  @db.TinyText
  url             String
  order           Int
  createdAt       Int
  updatedAt       Int
}

model Voting {
  userId        Int
  user          User  @relation(fields: [userId], references: [id])
  issueKey      Int
  contestantId  Int
  contestants   Contestant  @relation(fields: [contestantId], references: [id])
  votingSessionId Int
  votingSession VotingSession @relation(fields: [votingSessionId], references: [id])
  createdAt     Int
  updatedAt     Int

  @@id([userId, issueKey])
}