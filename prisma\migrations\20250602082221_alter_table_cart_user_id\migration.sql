/*
  Warnings:

  - You are about to alter the column `user_id` on the `shopping_cart` table. The data in that column could be lost. The data in that column will be cast from `<PERSON>r(36)` to `Int`.
  - You are about to alter the column `user_id` on the `shopping_cart_activity_log` table. The data in that column could be lost. The data in that column will be cast from `Char(36)` to `Int`.

*/
-- DropForeignKey
ALTER TABLE `shopping_cart` DROP FOREIGN KEY `shopping_cart_user_id_fkey`;

-- AlterTable
ALTER TABLE `shopping_cart` MODIFY `user_id` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `shopping_cart_activity_log` MODIFY `user_id` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `shopping_cart` ADD CONSTRAINT `shopping_cart_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
