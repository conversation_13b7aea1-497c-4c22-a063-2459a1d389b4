import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { CartItemStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';

type ParameterType = CartItemStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkStockStrategy: Strategy = async (parameter: ParameterType) => {
  const { skuId, productId, quantity = 0 } = parameter as CartItemStrategyParameterType;

  // search inventory
  const stock = await db.product_inventory.findUnique({
    select: { total_quantity: true, available_quantity: true },
    where: { product_id: productId, sku_id: skuId },
  });

  if (stock) {
    const { total_quantity: totalQty, available_quantity: availableQty } = stock;
    const isAvailable = totalQty >= quantity && availableQty >= quantity;
    if (!isAvailable) {
      throw new CustomErrorException(errorCodeTable.cartAddItemUnderStocking);
    }
  } else {
    throw new CustomErrorException(errorCodeTable.productSKUInvalidate);
  }
};
