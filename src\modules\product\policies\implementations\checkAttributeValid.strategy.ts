import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@/errors';
import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkAttributeValidStrategy: Strategy = async (parameter: ParameterType) => {
  const { sku } = parameter as CreateProductRequestType;

  // Retrieve DB category,value
  const categoryData = await db.product_attribute_categories.findMany({
    select: { product_attribute_category_id: true },
  });
  const skuCategories = categoryData.map((x) => x.product_attribute_category_id);
  const valueData = await db.product_attribute_values.findMany({
    select: { product_attribute_value_id: true },
  });
  const skuValues = valueData.map((x) => x.product_attribute_value_id);

  // Retrieve SKU category,value
  const { category, value } = sku.reduce<{ category: Set<number>; value: Set<number> }>(
    (result, { attribute }) => {
      return attribute.reduce<{ category: Set<number>; value: Set<number> }>(
        (a, { category, value }) => {
          a.category.add(category);
          a.value.add(value);
          return a;
        },
        result,
      );
    },
    { category: new Set<number>(), value: new Set<number>() },
  );

  // if any sku.category/value not in db.category/value, throw error.
  if (
    [...category].some((x) => !skuCategories.includes(x)) ||
    [...value].some((x) => !skuValues.includes(x))
  ) {
    throw new CustomErrorException(errorCodeTable.addProductInvalidAttribute);
  }
};
