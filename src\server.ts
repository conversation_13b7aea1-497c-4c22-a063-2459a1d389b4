import express, { Request, Response, NextFunction } from 'express';
import * as bodyParser from 'body-parser';
import xmlparser from 'express-xml-bodyparser';
import cors from 'cors';

import passport from '@config/passport';
import { logger } from '@utils/logger';
import apiRoute from '@routes';
import { errorHandler, httpLogger, notFoundHandler } from '@middleware';

import '@config/loadEnv';
import '@config/dayjsConfig';
import { redis } from './modules/mq/redis';
import { ServiceQueue, InitMQ } from './modules/mq';

const {
    redisHost = '127.0.0.1',
    redisPort = 6379
} = process.env

const app = express();

app.use(passport.initialize());
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(xmlparser());

// HTTP Request logger
app.use(httpLogger);

// Health check endpoints
app.get('/ping', (req: Request, res: Response) => {
  res.status(200).json({});
});

app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'unknown'
  });
});

app.use('/api', apiRoute);

// handle not found error
app.use(notFoundHandler);

// handle prisma error
app.use(errorHandler);

// NOTE:
// external payment
// app.use('/labPay', labPayRouter)
// common usage for both website and unity
// app.use('/common/voting', commonVotingRoute)
// app.use('/featured-event',featuredEventsRoute)

// app.use((err: Error, _: Request, res: Response, next: NextFunction) => {
//   // NOTE: Development env, error log
//   console.log(err);

//   if (process.env.NODE_ENV !== 'test') {
//     logger.error(err);
//   }

//   if (err instanceof HttpException) {
//     return res.status(err.errorCode).send({
//       messages: err.message,
//       err,
//     });
//   }

//   if (err instanceof PrismaClientKnownRequestError) {
//     if (err.code === 'P2025') {
//       return res.status(StatusCodes.NOT_FOUND).send({
//         messages: messages.not_found,
//         err,
//       });
//     }

//     if (err.code === 'P2002') {
//       return res.status(StatusCodes.CONFLICT).send({
//         messages: messages.conflict,
//         err,
//       });
//     }
//   }
//   if ((err as AWSError).code === 'NoSuchKey') {
//     return res.status(400).send({
//       messages: messages.file_not_exist,
//       err,
//     });
//   }

//   if (err instanceof ZodError) {
//     return res.status(400).send({
//       messages: messages.bad_request,
//       err,
//     });
//   }

// return res.status(500).json({
//   message: messages.internal_server_error,
//   err,
// });
// });

if (process.env.NODE_ENV === 'dev') {
  const PORT = process.env.APP_PORT || 5000;

  app.listen(PORT, () => {
    logger.info(`server up on port ${PORT}`);
  });
}


redis.on('connect', () => {
  // console.info(`connect to redis server >> host: ${redisHost} >> port: ${redisPort}`);
  InitMQ()
});

redis.on('error', (err) => {
  // console.error('Redis error:', err);
});

export default app;
