import db from '@prismaClient';
import { Language } from '@shared/enums';
import { PrismaProductStatus } from '@modules/product/enums/product.enum';
import { getAllInfoByISO } from 'iso-country-currency';
import { languageCodeSorter } from '@/utils/common';

interface RetrieveCountries {
  (lang: Language): Promise<{ [key: string]: { countryCode: string; name: string } }>;
}
interface RetrieveCountry {
  (
    countryCode: string,
    lang: Language,
  ): Promise<{ countryCode: string; name: string } | undefined | null>;
}

export const retrieveAllCountry: RetrieveCountries = async (lang) => {
  const countryItems = await db.country.findMany({
    select: {
      country_code: true,
      countryTranslations: {
        select: { language_code: true, name: true },
        where: { language_code: { in: ['EN', lang] } },
      },
    },
    where: { is_active: true },
  });

  return countryItems.reduce<{ [key: string]: { countryCode: string; name: string } }>((a, v) => {
    const ct = v?.countryTranslations.sort(languageCodeSorter)?.[0];
    return { ...a, [v.country_code]: { countryCode: v.country_code, name: ct?.name ?? '' } };
  }, {});
};

export const retrieveCountryByListedProduct: RetrieveCountries = async (lang) => {
  const countryItems = await db.country.findMany({
    select: {
      country_code: true,
      countryTranslations: {
        select: { language_code: true, name: true },
        where: { language_code: { in: ['EN', lang] } },
      },
    },
    where: {
      is_active: true,
      products: {
        some: { status: PrismaProductStatus.listed, is_active: true },
      },
    },
  });

  return countryItems.reduce<{ [key: string]: { countryCode: string; name: string } }>((a, v) => {
    const ct = v?.countryTranslations.sort(languageCodeSorter)?.[0];
    return { ...a, [v.country_code]: { countryCode: v.country_code, name: ct.name } };
  }, {});
};

export const retrieveCountryByCode: RetrieveCountry = async (countryCode, lang) => {
  const countryItem = await db.country.findFirst({
    select: {
      country_code: true,
      countryTranslations: {
        select: { language_code: true, name: true },
        where: { language_code: { in: ['EN', lang] } },
      },
    },
    where: { is_active: true, country_code: countryCode },
  });

  if (!countryItem || countryItem?.countryTranslations?.length < 1) {
    return null;
  }
  return {
    countryCode: countryItem.country_code,
    name: countryItem.countryTranslations?.sort(languageCodeSorter)?.[0].name ?? '',
  };
};

export const retrieveCurrencyByCountryCode = (countryCode: string) => {
  try {
    const info = getAllInfoByISO(countryCode);
    return info.currency;
  } catch (error) {
    return '';
  }
};
