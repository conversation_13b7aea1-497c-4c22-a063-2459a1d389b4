# Stage 1: Build the application
FROM ubuntu:22.04 AS builder

# Install required libraries
RUN apt-get update && apt-get install -y \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs=20.18.0-1nodesource1

# Set working directory
WORKDIR /app

# Install dependencies (including dev dependencies for build)
COPY package*.json ./
RUN npm install -g npm@10.8.2 && HUSKY=0 npm ci --ignore-scripts

# Copy application code (excluding .env files)
COPY src ./src
COPY prisma ./prisma
COPY tsconfig.json ./
COPY nodemon.json ./
COPY jest.config.js ./
COPY eslint.config.mjs ./
COPY handler.ts ./
COPY prisma.config.ts ./

# Generate Prisma client
RUN npx prisma generate

# Build TypeScript to JavaScript
RUN npm run build

# Stage 2: Serve the application
FROM ubuntu:22.04

# Install required libraries
RUN apt-get update && apt-get install -y \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs=20.18.0-1nodesource1

# Set working directory
WORKDIR /app

# Copy built application from the builder stage
COPY --from=builder /app ./

# Install production dependencies
RUN npm install -g npm@10.8.2 && HUSKY=0 npm ci --only=production --ignore-scripts

# Install Nodemon globally
RUN npm install -g nodemon

# Create a non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose ports
EXPOSE 5000
EXPOSE 8080

# Build-time arguments with default values from .env
ARG NODE_ENV="production"
ARG FRONT_END_URL="https://dev-market.incutix.com"
ARG S3_PUBLIC="https://dev-market.incutix.com"
ARG S3_BUCKET_NAME="dev-web-incutix.fun-verse.io"
ARG S3_BUCKET_PRIVATE="dev-incutix"
ARG ENCRYPT_METHOD="MD5"
ARG NOTIFY_URL="https://dev-api2.incutix.com"
ARG RETURN_URL="https://dev-market.incutix.com"
ARG LAB_PAY_API_URL="https://wapi.uat.hk.lab-pay.com"
ARG SENDER_EMAIL="<EMAIL>"
ARG SUPPORT_EMAIL="<EMAIL>,<EMAIL>"
ARG ERROR_EMAIL="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
# Environment variables with default values from build args
ENV NODE_ENV=${NODE_ENV}
ENV FRONT_END_URL=${FRONT_END_URL}
ENV S3_PUBLIC=${S3_PUBLIC}
ENV S3_BUCKET_NAME=${S3_BUCKET_NAME}
ENV S3_BUCKET_PRIVATE=${S3_BUCKET_PRIVATE}
ENV ENCRYPT_METHOD=${ENCRYPT_METHOD}
ENV NOTIFY_URL=${NOTIFY_URL}
ENV RETURN_URL=${RETURN_URL}
ENV LAB_PAY_API_URL=${LAB_PAY_API_URL}
ENV SENDER_EMAIL=${SENDER_EMAIL}
ENV SUPPORT_EMAIL=${SUPPORT_EMAIL}
ENV ERROR_EMAIL=${ERROR_EMAIL}

# Sensitive environment variables (will be overridden by Kubernetes secrets)
ENV DATABASE_URL=""
ENV DATABASE_READ_URL=""
ENV AWS_ACCESS_KEY_ID=""
ENV AWS_SECRET_ACCESS_KEY=""
ENV AWS_REGION=""
ENV JWT_SECRET=""
ENV LAB_PAY_MERCHANT_APP_ID=""
ENV LAB_PAY_MERCHANT_ID=""
ENV LAB_PAY_KEY=""

# Note: Health checks are handled by Kubernetes liveness/readiness probes
# HEALTHCHECK is not supported in OCI format and not needed in K8s environment

# Create startup script that handles database migrations and seeding
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting application..."\n\
\n\
# Run database migrations if DATABASE_URL is provided\n\
if [ ! -z "$DATABASE_URL" ]; then\n\
    echo "Running database migrations..."\n\
    npx prisma migrate deploy\n\
    \n\
    # Run database seeding only in development/UAT environments\n\
    if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "uat" ]; then\n\
        echo "Running database seeding..."\n\
        npm run db:seed || echo "Seeding failed or already completed"\n\
    fi\n\
fi\n\
\n\
# Start the application (using compiled JavaScript)\n\
exec node dist/src/server.js' > /app/start.sh && \
    chmod +x /app/start.sh && \
    chown appuser:appuser /app/start.sh

# Start the application using the startup script
CMD ["/app/start.sh"]





