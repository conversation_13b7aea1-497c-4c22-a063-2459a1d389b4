# Stage 1: Build the application
FROM ubuntu:22.04 AS builder

# Install required libraries
RUN apt-get update && apt-get install -y \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs=20.18.0-1nodesource1

# Set working directory
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install -g npm@10.8.2 && npm install

# Install Prisma CLI
RUN npm install prisma --save-dev

# Copy application code (excluding .env files)
COPY src ./src
COPY prisma ./prisma
COPY tsconfig.json ./
COPY nodemon.json ./
COPY jest.config.js ./
COPY eslint.config.mjs ./
COPY handler.ts ./
COPY prisma.config.ts ./

# Generate Prisma client (this will be done at runtime with proper env vars)
RUN npx prisma generate

# Stage 2: Serve the application
FROM ubuntu:22.04

# Install required libraries
RUN apt-get update && apt-get install -y \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs=20.18.0-1nodesource1

# Set working directory
WORKDIR /app

# Copy built application from the builder stage
COPY --from=builder /app ./

# Install production dependencies
RUN npm install -g npm@10.8.2 && npm install --only=production

# Install Nodemon globally
RUN npm install -g nodemon

# Create a non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose ports
EXPOSE 5000
EXPOSE 8080

# Environment variables that will be provided by Kubernetes
ARG ENCRYPT_METHOD=MD5
ARG NOTIFY_URL="https://dev-api2.incutix.com"
ARG RETURN_URL=""
ARG TICKET_SYSTEM_API_KEY=""
ARG DEFAULT_EVENT_ID=""
ARG SENDER_EMAIL=""
ARG SUPPORT_EMAIL=""
ENV NODE_ENV=production
ENV DATABASE_URL=""
ENV DATABASE_READ_URL=""
ENV AWS_ACCESS_KEY_ID=""
ENV AWS_SECRET_ACCESS_KEY=""
ENV AWS_REGION=""
ENV JWT_SECRET=""
ENV FRONT_END_URL=""
ENV LAB_PAY_MERCHANT_APP_ID=""
ENV LAB_PAY_MERCHANT_ID=""
ENV LAB_PAY_KEY=""
ENV LAB_PAY_API_URL=""
ENV ENCRYPT_METHOD=ENCRYPT_METHOD
ENV NOTIFY_URL=""
ENV RETURN_URL=""
ENV TICKET_SYSTEM_API_KEY=""
ENV DEFAULT_EVENT_ID=""
ENV SENDER_EMAIL=""
ENV SUPPORT_EMAIL=""

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/ping || exit 1

# Create startup script that handles database migrations and seeding
COPY --chown=appuser:appuser <<EOF /app/start.sh
#!/bin/bash
set -e

echo "Starting application..."

# Run database migrations if DATABASE_URL is provided
if [ ! -z "\$DATABASE_URL" ]; then
    echo "Running database migrations..."
    npx prisma migrate deploy

    # Run database seeding only in development/UAT environments
    if [ "\$NODE_ENV" = "development" ] || [ "\$NODE_ENV" = "uat" ]; then
        echo "Running database seeding..."
        npm run db:seed || echo "Seeding failed or already completed"
    fi
fi

# Start the application
exec nodemon src/server.ts
EOF

RUN chmod +x /app/start.sh

# Start the application using the startup script
CMD ["/app/start.sh"]





