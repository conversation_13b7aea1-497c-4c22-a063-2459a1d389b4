import { Router } from 'express';
import { jwtMiddleware } from '@middleware';
// import * as featureEventController from '@modules/featuredEvents/featuredEvents.controller';

const router = Router();

//TODO: the jwtMiddleware is need ?

// router.get('/', jwtMiddleware, featureEventController.getAllFeatureEvents);
// router.get('/:id', jwtMiddleware, featureEventController.getFeatureEventById);
// router.post('/:id', jwtMiddleware, featureEventController.createFeatureEvent);

export default router;
