import db from '@prismaClient';

import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkDuplicateSkuCode: Strategy = async (parameter: ParameterType) => {
  const { sku } = parameter as CreateProductRequestType;

  // Retrieve DB sku-code
  const data = await db.product_sku.findMany({
    select: { sku_code: true },
  });
  const skuData = data.map((x) => x.sku_code);

  // Retrieve sku sku-code
  const skuCode = sku.reduce<Set<string>>((result, { skuCode }) => {
    return result.add(skuCode);
  }, new Set<string>());

  if ([...skuCode].some((x) => skuData.includes(x))) {
    throw new CustomErrorException(errorCodeTable.addProductDuplicateSkuCode);
  }
};
