// modules/shipping/adapters/order-status.adapter.ts
import { CartType, PrismaCartType } from '@modules/shoppingCart/enums/shoppingCart.enum';
import { OrderType, PrismaOrderType } from '../enum/order.enum';

export const adaptOrderStatus = (cartType: CartType): OrderType => {
  const adapter: Record<CartType, OrderType> = {
    // FIXME: handle hybrid
    [PrismaCartType.hybrid]: PrismaOrderType.product,
    [PrismaCartType.productOnly]: PrismaOrderType.product,
    [PrismaCartType.ticketOnly]: PrismaOrderType.ticket,
  };
  return adapter[cartType];
};
