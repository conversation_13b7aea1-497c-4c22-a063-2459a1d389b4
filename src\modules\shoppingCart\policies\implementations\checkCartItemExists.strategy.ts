import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { generateTimeNow } from '@utils/common';
import {
  CartCheckStrategyParameterType,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';

type ParameterType = CartCheckStrategyParameterType & CartItemStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkCartItemExistStrategy: Strategy = async (parameter: ParameterType) => {
  const { userId, cartItemType, productId, skuId } = parameter;

  const { cart_id: cartId } = await db.shopping_cart.findUniqueOrThrow({
    select: { cart_id: true },
    where: { user_id: userId, expires_at: { gte: generateTimeNow() } },
  });

  const findCartItem = await db.shopping_cart_item.findUnique({
    select: { shopping_cart_item_id: true },
    where: {
      cart_id_cart_item_type_product_id_sku_id: {
        cart_id: cartId,
        cart_item_type: cartItemType,
        product_id: productId,
        sku_id: skuId,
      },
    },
  });

  if (!findCartItem) {
    throw new CustomErrorException(errorCodeTable.cartItemNotExist, {
      cartItemType,
      productId,
      skuId,
    });
  }
};
