model ticket_setting {
  id           Int           @id @default(autoincrement())
  sale_start_datetime    Int?
  sale_end_datetime      Int?
  currency             Currency
  sale_time_thershold    Int?

  created_at    Int
  updated_at    Int

  event_id     Int  @unique
  event_info   event @relation("TicketSetting", fields: [event_id], references: [id], onDelete: Cascade)

  ticket_section   ticket_section[] @relation("TicketSection")
  ticket_variation ticket_variation[] @relation("TicketVariation")
  ticket_type      ticket_type[] @relation("TicketType")
}

model ticket_section {
  id           Int           @id @default(autoincrement())
  day          Int?
  date         Int?
  start_time    Int
  end_time      Int
  quantity     Int
  used         Int  @default(0)
  seq          Int
  created_at    Int
  updated_at    Int

  ticket_setting_id   Int
  ticket_setting    ticket_setting @relation("TicketSection", fields: [ticket_setting_id], references: [id], onDelete: Cascade)
  ticket_type_to_section ticket_type_to_section[] @relation("TicketSessionToType")
  ticket_info   ticket[] @relation("ticket_to_ticket_section")
  order_ticket  order_items[]  @relation("order_ticket_section")
  ticket_adjustment ticket_adjustment[] @relation("ticket_adjustment")
  ticket_date_inventory  ticket_date_inventory[]  @relation("ticket_date_inventory")
  ticket_distribution ticket_distribution[] @relation("ticket_distribution")
}

model ticket_date_inventory {
  id Int @id @default(autoincrement())
  total Int 
  available Int
  on_hold Int
  timestamp Int 
  created_at Int
  updated_at Int

  ticket_section_id Int
  ticket_section  ticket_section  @relation("ticket_date_inventory", fields: [ticket_section_id], references: [id], onDelete: Cascade)
  ticket_distribution ticket_distribution[] @relation("ticket_date_inventory")
  tickets ticket[] @relation("ticket_selected_date")

  @@unique([timestamp, ticket_section_id])
}

model ticket_adjustment {
  id          Int           @id @default(autoincrement())
  amount      Int
  before      Int
  after       Int
  created_at  Int
  updated_at  Int

  ticket_section_id   Int
  ticket_section ticket_section @relation("ticket_adjustment", fields: [ticket_section_id], references: [id], onDelete: Cascade)
}

model ticket_distribution {
  id  Int @id @default(autoincrement())
  ticket_section_id Int
  ticket_section  ticket_section @relation("ticket_distribution", fields: [ticket_section_id], references: [id], onDelete: Cascade)
  ticket_type_id    Int
  ticket_type ticket_type @relation("ticket_type_distribution", fields: [ticket_type_id], references: [id], onDelete: Cascade)
  ticket_date_inventory_id Int
  ticket_date_inventory ticket_date_inventory @relation("ticket_date_inventory", fields: [ticket_date_inventory_id], references: [id], onDelete: Cascade)

  ticket_distribution_adjustment  ticket_distribution_adjustment[] @relation("ticket_distribution_adjustment")
  ticket  ticket[] @relation("ticket_branch")
  @@unique([ticket_type_id, ticket_date_inventory_id])
}

model ticket_distribution_adjustment {
  id  Int @id @default(autoincrement())
  amount  Int
  before  Int
  after   Int
  created_at  Int
  updated_at  Int

  ticket_distribution_id  Int
  ticket_distribution ticket_distribution @relation("ticket_distribution_adjustment", fields: [ticket_distribution_id], references: [id], onDelete: Cascade)
}

model ticket_variation {
  id           Int           @id @default(autoincrement())
  name         String
  type         VariationType
  created_at    Int
  updated_at    Int

  ticket_setting_id   Int
  ticket_setting    ticket_setting @relation("TicketVariation", fields: [ticket_setting_id], references: [id], onDelete: Cascade)
  ticket_variation_option ticket_variation_option[] @relation("ticket_variation_option")
  ticket_variation_to_type ticket_type_to_variation[] @relation("ticket_variation_to_type")
}

model ticket_variation_option {
  id           Int           @id @default(autoincrement())
  value        String?
  from         Int?
  to           Int?
  created_at    Int
  updated_at    Int
  
  ticket_variation_id Int
  ticket_variation   ticket_variation @relation("ticket_variation_option", fields: [ticket_variation_id], references: [id], onDelete: Cascade)
}

model third_party {
  id  Int @id @default(autoincrement())
  name  String
  created_at  Int

  ticket_type ticket_type[] @relation("third_party")
}

model ticket_type {
  id           Int           @id @default(autoincrement())
  multiply     Int
  name         String
  description  String @db.Text
  is_all_day     Boolean
  pre_sale_start_time  Int?
  pre_sale_end_time    Int?
  selling_type Int @default(1)
  price        Int?
  price_unit    String?
  price_unit_en  String?
  allow_multiple_entry    Boolean
  have_gift     Boolean
  status        TicketTypeStatus @default(ACTIVE)
  created_at    Int
  updated_at    Int

  ticket_setting_id   Int
  ticket_setting    ticket_setting @relation("TicketType", fields: [ticket_setting_id], references: [id], onDelete: Cascade)
  ticket_type_to_section ticket_type_to_section[] @relation("TicketTypeToSection")
  ticket_type_to_variation ticket_type_to_variation[] @relation("ticket_type_to_variation")
  ticket_to_gift    ticket_to_gift[] @relation("TicketToGift")
  ticket_info   ticket[] @relation("ticket_to_type")
  order_item  order_items[]  @relation("order_ticket_type")
  ticket_type_distribution ticket_distribution[] @relation("ticket_type_distribution")
  third_party_id  Int?
  third_party third_party? @relation("third_party", fields: [third_party_id], references: [id], onDelete: Cascade)
}

model ticket_type_to_section {
  ticket_type_id      Int
  ticket_type ticket_type @relation("TicketTypeToSection", fields: [ticket_type_id], references: [id], onDelete: Cascade)
  ticket_section_id   Int
  ticket_section ticket_section @relation("TicketSessionToType", fields: [ticket_section_id], references: [id], onDelete: Cascade)

  @@unique([ticket_type_id, ticket_section_id])
}

model ticket_type_to_variation {
  ticket_type_id     Int
  ticket_type ticket_type @relation("ticket_type_to_variation", fields: [ticket_type_id], references: [id], onDelete: Cascade)
  ticket_variation_id   Int
  ticket_variation ticket_variation @relation("ticket_variation_to_type", fields: [ticket_variation_id], references: [id], onDelete: Cascade)

  @@unique([ticket_type_id, ticket_variation_id])
}

model ticket_to_gift {
  ticket_type_id     Int
  ticket_type        ticket_type @relation("TicketToGift", fields: [ticket_type_id], references: [id], onDelete: Cascade)
  product_id         Int
  products           products @relation("gift_to_ticket", fields: [product_id], references: [product_id], onDelete: Cascade)

  @@unique([ticket_type_id, product_id])
}

model ticket {
  id                Int @id @default(autoincrement())
  status            TicketType
  qrcode            String
  url               String?
  created_at        Int
  updated_at        Int

  branch_id  Int?
  branch ticket_distribution? @relation("ticket_branch", fields: [branch_id], references: [id])
  user_id           Int?
  user_info         User? @relation("user_ticket", fields: [user_id], references: [id])
  order_item_id     Int? 
  order_item        order_items? @relation("order_ticket", fields: [order_item_id], references: [order_item_id])
  ticket_type_id    Int
  ticket_type       ticket_type @relation("ticket_to_type", fields: [ticket_type_id], references: [id])
  ticket_section_id Int
  ticket_section    ticket_section @relation("ticket_to_ticket_section", fields: [ticket_section_id], references: [id])
  ticket_date_inventory_id Int
  ticket_date_inventory ticket_date_inventory @relation("ticket_selected_date", fields: [ticket_date_inventory_id], references: [id])
  entry_record      entry_record[] @relation("entry_record")
  redeem_record     redeem_gift_record[] @relation("redeem_record")

  @@unique([qrcode])
}

model entry_record {
  id                Int @id @default(autoincrement())
  created_at        Int
  updated_at        Int

  ticket_id         Int
  ticket_info       ticket @relation("entry_record", fields: [ticket_id], references: [id])
}

model redeem_gift_record {
  id                Int @id @default(autoincrement())
  created_at        Int
  updated_at        Int

  ticket_id         Int
  ticket_info       ticket @relation("redeem_record", fields: [ticket_id], references: [id])
  product_id        Int
  products          products @relation("redeem_gift", fields: [product_id], references: [product_id])
}

enum Currency {
  MYD
  HKD
}

enum VariationType {
  NORMAL
  SPECIAL
}

enum TicketType {
  ISSUING
  ISSUED
  USED
  EXPIRED
  VOIDED
}

enum TicketTypeStatus {
  ACTIVE
  INACTIVE
}