import db from '@prismaClient';
import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkEventStrategy: Strategy = async (parameter: ParameterType) => {
  const { eventId } = parameter as CreateProductRequestType;

  const event = await db.event.findUnique({
    where: { id: eventId },
  });

  if (!event) {
    throw new CustomErrorException(errorCodeTable.addProductInvalidRegion);
  }
};
