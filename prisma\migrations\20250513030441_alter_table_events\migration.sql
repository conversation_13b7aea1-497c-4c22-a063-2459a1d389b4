/*
  Warnings:

  - You are about to drop the `FeaturedEvents` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE `FeaturedEvents`;

-- CreateTable
CREATE TABLE `events` (
    `events_id` INTEGER NOT NULL AUTO_INCREMENT,
    `event_name` VARCHAR(255) NOT NULL,
    `description` LONGTEXT NULL,
    `event_start_date` INTEGER NOT NULL,
    `event_end_date` INTEGER NOT NULL,
    `region` VARCHAR(3) NOT NULL,
    `sale_start_date` INTEGER NULL,
    `sale_end_date` INTEGER NULL,
    `opening_start_time` TIME NULL,
    `opening_end_time` TIME NULL,
    `about_time` INTEGER NULL,
    `thumbnail` VARCHAR(191) NULL,
    `venue` VARCHAR(191) NULL,
    `category` VARCHAR(191) NULL,
    `note` TEXT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    UNIQUE INDEX `uk_event_name`(`event_name`),
    PRIMARY KEY (`events_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
