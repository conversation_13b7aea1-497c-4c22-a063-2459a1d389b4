import { z } from 'zod';

import {
  languageCodeSchema,
  languageQuerySchema,
  paginationQuerySchema,
} from '@/shared/schema/request.schema';
import { PaginatedDataSchema } from '@shared/schema/pagination.schema';

import { PrismaProductMediaType, PrismaProductStatus } from '../enums/product.enum';
import { attributeSchema } from '@/shared/schema/productAttribute.schema';

// REGION: getAllProducts request schema
const getProductsSearchFieldSchema = z.object({
  region: z.string().length(2).optional(),
  eventId: z
    .string()
    .optional()
    .transform((x) => Number(x)),
  sort: z.enum(['productId', 'region', 'event', 'saleDate', 'pickupDate']),
});
export const getAllProductsQuerySchema = paginationQuerySchema
  .merge(languageQuerySchema)
  .merge(getProductsSearchFieldSchema);
export type GetAllProductsRequestType = z.infer<typeof getAllProductsQuerySchema>;

// getAllProducts response schema
const productListSchema = z.object({
  productId: z.number().int().positive(),
  productName: z.string(),
  thumbnail: z.string(), // FIXME: using z.string().url()
  region: z.string().length(2),
  currency: z.string(),
  saleStartDate: z.number(),
  saleEndDate: z.number(),
  country: z.object({
    code: z.string().length(2),
    name: z.string(),
  }),
  event: z.object({
    id: z.number(),
    name: z.string(),
  }),
  price: z.number(),
  inventory: z.object({ stock: z.string().transform((price) => Number.parseFloat(price)) }),
  isAvailable: z.boolean(),
});
export const getAllProductsResponse = PaginatedDataSchema(productListSchema);

// REGION: getProductsSearchSortFields response schema
export const getProductsSearchSortFieldsResponse = z.object({
  sortFields: z.array(z.string()),
  defaultSort: z.record(z.string(), z.number().min(-1).max(1)),
});

// REGION: getProductById request schema
export const getProductByIdParamSchema = z.object({
  id: z.string().transform((id) => Number(id)),
});
export const getProductByIdQuerySchema = languageQuerySchema;
export type GetProductByIdRequestType = {
  params: z.infer<typeof getProductByIdParamSchema>;
  query: z.infer<typeof getProductByIdQuerySchema>;
};

// getProductById response schema
export const getProductByIdResponseSchema = z.object({
  productId: z.number(),
  skuId: z.number(),
  productName: z.string(),
  description: z.string(),
  productIntroduction: z.string(),
  productPickupVenue: z.string(),
  saleStartDate: z.number(),
  salesEndDate: z.number(),
  category: z.number(),
  currency: z.string(),
  price: z.object({
    originalPrice: z.string().transform((price) => Number.parseFloat(price)),
    salePrice: z.string().transform((price) => Number.parseFloat(price)),
  }),
  event: z.object({
    id: z.number(),
    name: z.string(),
  }),
  media: z.object({
    image: z.array(z.object({ isMain: z.boolean(), path: z.string(), priority: z.number() })),
    video: z.array(z.object({ isMain: z.boolean(), path: z.string(), priority: z.number() })),
  }),
  shipping: z.object({
    pickupStartDate: z.number(),
    pickupEndDate: z.number(),
    shippingMethod: z.array(z.string()),
    pickupVenue: z.string(),
  }),
  isAvailable: z.boolean(),
  productAttributes: z.object({
    default: attributeSchema.single,
    skuAttributes: z.record(z.string(), attributeSchema.single),
    attributes: attributeSchema.multiple,
  }),
  productSKU: z.array(
    z.object({
      skuId: z.number(),
      price: z.object({
        originalPrice: z.string().transform((price) => Number.parseFloat(price)),
        salePrice: z.string().transform((price) => Number.parseFloat(price)),
      }),
      media: z.object({
        image: z.array(z.object({ isMain: z.boolean(), path: z.string(), priority: z.number() })),
        video: z.array(z.object({ isMain: z.boolean(), path: z.string(), priority: z.number() })),
      }),
      skuAttribute: attributeSchema.single,
      isAvailable: z.boolean(),
    }),
  ),
});

// REGION:  getRelatedProductsById request schema
export const getRelatedProductsByIdParamSchema = z.object({
  id: z.string().transform((id) => Number(id)),
});
export const getRelatedByIdQuerySchema = languageQuerySchema;
export type GetRelatedProductByIdRequestType = {
  params: z.infer<typeof getRelatedProductsByIdParamSchema>;
  query: z.infer<typeof getRelatedByIdQuerySchema>;
};

// getRelatedProductsById response schema
export const getRelatedProductsResponse = PaginatedDataSchema(productListSchema);

// REGION: getProductSearchCriteria request schema
export const getProductSearchCriteriaQuerySchema = languageQuerySchema;
export type GetProductSearchCriteriaType = z.infer<typeof getProductSearchCriteriaQuerySchema>;

export const getProductSearchCriteriaResponseSchema = z.object({
  events: z.array(
    z.object({
      eventId: z.number(),
      eventName: z.string(),
    }),
  ),
  productAttributes: attributeSchema.multiple,
  region: z.record(
    z.string(),
    z.object({
      countryCode: z.string(),
      name: z.string(),
    }),
  ),
});

// REGION: createProduct request schema
const createProductTransaction = z.object({
  languageCode: languageCodeSchema,
  name: z.string(),
  description: z.string(),
  introduction: z.string(),
  tag: z.array(z.string()),
  pickupVenue: z.string(),
});
const createProductMedia = z.object({
  mediaType: z.enum([PrismaProductMediaType.image, PrismaProductMediaType.video]),
  isMain: z.boolean(),
  url: z.string(),
  priority: z.number(),
});

const createProductInventory = z.object({
  totalQuantity: z.number(),
});
const createProductAttribute = z.object({
  category: z.number(),
  value: z.number(),
});
const createProductSku = z.object({
  isDefault: z.boolean(),
  skuCode: z.string(),
  originalPrice: z.number(),
  salePrice: z.number(),
  costPrice: z.number(),
  tax: z.number(),
  weight: z.number(),
  volume: z.number(),
  length: z.number(),
  width: z.number(),
  height: z.number(),
  media: z.array(createProductMedia),
  productInventory: createProductInventory,
  attribute: z.array(createProductAttribute),
});
export const createProductBodySchema = z.object({
  region: z.string(),
  eventId: z.number(),
  pickupStartDate: z.number(),
  pickupEndDate: z.number(),
  shippingMethod: z.array(z.string()),
  saleStartDate: z.number(),
  saleEndDate: z.number(),
  transaction: z.array(createProductTransaction),
  sku: z.array(createProductSku),
});
export type CreateProductRequestType = z.infer<typeof createProductBodySchema>;
export type CreateProductTransactionType = z.infer<typeof createProductTransaction>;
export type CreateProductMediaType = z.infer<typeof createProductMedia>;

// REGION: update product status
export const updateProductStatusParamSchema = z.object({
  id: z.string().transform((id) => Number(id)),
});
export const updateProductStatusBodySchema = z.object({
  status: z.enum([
    PrismaProductStatus.draft,
    PrismaProductStatus.listed,
    PrismaProductStatus.unListed,
  ]),
});
export type UpdateProductStatusRequestType = {
  params: z.infer<typeof updateProductStatusParamSchema>;
  body: z.infer<typeof updateProductStatusBodySchema>;
};

// REGION: batch update product status +z.array to under
export const batchUpdateProductStatusBodySchema = z.object({
  batch: z.array(
    z.object({
      id: z.number(),
      status: z.enum([
        PrismaProductStatus.draft,
        PrismaProductStatus.listed,
        PrismaProductStatus.unListed,
      ]),
    }),
  ),
});
export type BatchUpdateProductStatusRequestType = z.infer<
  typeof batchUpdateProductStatusBodySchema
>;
