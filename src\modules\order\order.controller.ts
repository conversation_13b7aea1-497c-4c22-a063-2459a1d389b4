import { Request, Response, NextFunction } from 'express';

import { handleAsync, validateRequest, verifyIdempotencyToken } from '@/middleware';
import { languageQuerySchema, requestSchema } from '@/shared/schema/request.schema';
import { sendSuccessResponse } from '@/utils/apiResponse';
import * as paymentService from '@modules/payment/payment.service';
import * as idempotencyKeyService from '@modules/idempotencyKey/idempotencyKey.service';
import { UserType } from '@/shared/types/user.type';

import * as orderSchema from './schema/order.schema';
import * as orderService from './order.service';
import { OrderShippingStatus } from './enum/order.enum';

export const getOrderById = [
  validateRequest(
    requestSchema.all({
      params: orderSchema.getOrderByIdParamSchema,
      query: orderSchema.getOrderByIdQuerySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const {
      params: { id: orderId },
      query: { language },
    } = req.validatedData as orderSchema.GetOrderByIdRequestType;
    const result = await orderService.retrieveOrderById(orderId, language);

    const data = orderSchema.getOrderByIdResponseSchema.parse(result);

    return sendSuccessResponse(res, data);
  }),
];

export const getOrderPaymentIdempotencyKey = [
  validateRequest(requestSchema.body(orderSchema.orderPaymentBodySchema)),
  handleAsync(async (req: Request, res: Response) => {
    const { userId } = req.user as UserType;
    const order = req.validatedData.body as orderSchema.OrderPaymentBodySchemaType;

    const token = await orderService.getOrderPaymentIdempotencyKey(userId, order);

    return sendSuccessResponse(res, { token });
  }),
];

export const orderPayment = [
  verifyIdempotencyToken,
  validateRequest(
    requestSchema.all({
      body: orderSchema.orderPaymentBodySchema,
      query: languageQuerySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const { user } = req;
    const {
      body: order,
      query: { language },
    } = req.validatedData as orderSchema.OrderPaymentRequestSchemaType;
    const { idempotency_key: token } = req.tokenData;

    // Get original response by same post request.
    const responseByToken = await idempotencyKeyService.retrieveIdempotencyKeyResponse(token);
    if (responseByToken) {
      return sendSuccessResponse(res, responseByToken);
    }

    await orderService.updateProductOrder(user.userId, order);
    const response = await paymentService.sendLabPayPreCreateOrder(order, language);
    await idempotencyKeyService.updateIdempotencyKeyResponse(token, { url: response });

    return sendSuccessResponse(res, { url: response });
  }),
];

export const updateOrderShipping = [
  validateRequest(requestSchema.body(orderSchema.updateOrderShippingBodySchema)),
  handleAsync(async (req, res) => {
    const { user } = req;
    const { body: order } = req.validatedData as orderSchema.UpdateOrderShippingRequestType;

    await orderService.updateOrderShipping(user.userId, order);

    return sendSuccessResponse(res, {});
  }),
];

// NOTE: Admin get order list api
export const getOrderList = [
  validateRequest(requestSchema.query(orderSchema.getOrderListQuerySchema)),
  handleAsync(async (req, res) => {
    const query = req.validatedData?.query as orderSchema.GetOrderListRequestType;
    const { page, size } = query;

    const { result, count } = await orderService.retrieveOrderList(query);

    const paginatedData = orderSchema.getOrderListResponse.parse({
      items: result,
      meta: {
        page: Number(page),
        size: Number(size),
        total: count,
      },
    });

    return sendSuccessResponse(res, paginatedData);
  }),
];

// NOTE: Admin order info
export const getSalesOrderById = [
  validateRequest(
    requestSchema.all({
      params: orderSchema.getOrderByIdParamSchema,
      query: orderSchema.getOrderByIdQuerySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const {
      params: { id: orderId },
      query: { language },
    } = req.validatedData as orderSchema.GetOrderByIdRequestType;
    const result = await orderService.retrieveOrderById(orderId, language);
    const orderInfo = orderSchema.getOrderByIdResponseSchema.parse(result);
    const orderExtraInfo = await orderService.retrieveOrderExtraInfoById(orderId);
    const saleOrder = await orderService.handleSaleOrderInfo(orderInfo, orderExtraInfo);
    const data = orderSchema.getSaleOrderByIdResponseSchema.parse(saleOrder);

    return sendSuccessResponse(res, data);
  }),
];

// NOTE: Update order delivery status
export const updateOrderShippingStatus = [
  validateRequest(requestSchema.body(orderSchema.updateOrderShippingStatusSchema)),
  handleAsync(async (req, res) => {
    const order = req.validatedData?.body as orderSchema.UpdateOrderShippingStatusType;

    const { orderId, shippingStatus } = order;
    await orderService.updateOrderShippingStatus(orderId, shippingStatus);

    return sendSuccessResponse(res, {});
  }),
];

// NOTE: Batch update order delivery status
export const batchUpdateOrderShippingStatus = [
  validateRequest(requestSchema.body(orderSchema.batchUpdateOrderShippingStatusSchema)),
  handleAsync(async (req, res) => {
    const order = req.validatedData?.body as orderSchema.BatchUpdateOrderShippingStatusType;

    const { orderIds, shippingStatus } = order;
    await orderService.batchUpdateOrderShippingStatus(orderIds, shippingStatus);

    return sendSuccessResponse(res, {});
  }),
];

// NOTE: Update order delivery status
export const updateOrderStatus = [
  validateRequest(requestSchema.body(orderSchema.updateOrderStatusSchema)),
  handleAsync(async (req, res) => {
    const order = req.validatedData?.body as orderSchema.UpdateOrderStatusType;

    const { orderId, orderStatus } = order;
    await orderService.updateOrderStatus(orderId, orderStatus);

    return sendSuccessResponse(res, {});
  }),
];

// NOTE: Update order admin note
export const updateOrderAdminNote = [
  validateRequest(requestSchema.body(orderSchema.updateOrderAdminNoteSchema)),
  handleAsync(async (req, res) => {
    const order = req.validatedData?.body as orderSchema.UpdateOrderAdminNoteType;

    const { orderId, remark } = order;
    await orderService.updateOrderAdminNote(orderId, remark);

    return sendSuccessResponse(res, {});
  }),
];
