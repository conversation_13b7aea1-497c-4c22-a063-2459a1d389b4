import { Request, Response, NextFunction } from 'express';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

import { HttpException, InternalServerErrorException, prismaErrorHandler } from '@errors';
import { sendErrorResponse } from '@utils/apiResponse';
import { StatusCodes } from 'http-status-codes';
import { errorHandler as generalErrorHandler } from '@errors';

export const errorHandler = (
  err: Error | HttpException,
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  generalErrorHandler(err);
  // console.error(err);

  // DEV/UAT/PROD log error
  // if (process.env.NODE_ENV !== 'test') {
  //   logger.error(err);
  // }

  if (err instanceof PrismaClientKnownRequestError) {
    const prismaError: HttpException | undefined = prismaErrorHandler(err);
    if (prismaError) {
      return sendErrorResponse(res, prismaError.statusCode, prismaError);
    }
  }

  if (err instanceof HttpException) {
    return sendErrorResponse(res, err.statusCode, err);
  }

  // logger.error('Unexpected error:', err);
  return sendErrorResponse(
    res,
    StatusCodes.INTERNAL_SERVER_ERROR,
    new InternalServerErrorException(),
  );
};
