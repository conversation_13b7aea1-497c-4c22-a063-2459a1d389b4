import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import {
  CartCheckParameter,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';

import {
  boundaryProductQtyStrategy,
  checkCartExistStrategy,
  checkProductListedStrategy,
  checkRegionStrategy,
  checkStockStrategy,
  skuValidateStrategy,
} from '../implementations';

type ParameterType = CartCheckParameter & CartItemStrategyParameterType;
export const addToCartStrategyMap: CartCheckStrategy<ParameterType>[] = [
  skuValidateStrategy,
  checkProductListedStrategy,
  boundaryProductQtyStrategy,
  checkRegionStrategy,
  checkStockStrategy,
  checkCartExistStrategy,
];
