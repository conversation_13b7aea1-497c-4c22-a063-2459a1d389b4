import { z } from 'zod';
import { emailSchema } from '@shared/schema/email.schema';
import {
  PrismaOrderShippingStatus,
  PrismaOrderShippingType,
  PrismaOrderStatus,
  PrismaOrderType,
} from '../enum/order.enum';
import { languageQuerySchema, paginationQuerySchema } from '@shared/schema/request.schema';
import { PrismaCartType } from '@modules/shoppingCart/enums/shoppingCart.enum';
import { attributeSchema } from '@/shared/schema/productAttribute.schema';
import { PaymentPlatform, PaymentType } from '@/modules/payment/Enum/payment.enum';
import { PaginatedDataSchema } from '@/shared/schema/pagination.schema';

// REGION: create order
const createOrderOptional = z.object({
  subTotal: z.number().optional(),
  shippingFee: z.number().optional(),
  taxAmount: z.number().optional(),
  couponCode: z.string().optional(),
  couponDiscount: z.number().optional(),
  adminNote: z.string().optional(),
  customerNote: z.string().optional(),
  orderBillingCountryCode: z.string().optional(),
  orderBillingEmail: z.string().optional(),
  orderBillingName: z.string().optional(),
  orderBillingTel: z.number().optional(),
  orderContactCountryCode: z.string().optional(),
  orderContactEmail: z.string().optional(),
  orderContactName: z.string().optional(),
  orderContactTel: z.number().optional(),
  expireAt: z.number().optional(),
  region: z.string().length(2).optional(),
  status: z
    .enum([PrismaOrderStatus.inProgress, PrismaOrderStatus.complete, PrismaOrderStatus.cancel])
    .optional(),
  createdAt: z.number().optional(),
  updatedAt: z.number().optional(),
  cancelAt: z.number().optional(),
  completeAt: z.number().optional(),
});
const createProductOrderItemParameter = z.object({
  productId: z.number(),
  skuId: z.number(),
  quantity: z.number(),
  unitPrice: z.number(),
  originalPrice: z.number(),
  totalPrice: z.number(),
  discountAmount: z.number(),
  createdAt: z.number().optional(),
  updatedAt: z.number().optional(),
});
const createProductOrder = z.object({
  userId: z.number(),
  totalAmount: z.number(),
  discountAmount: z.number(),
  currency: z.string().length(3),
  orderItems: createProductOrderItemParameter.array(),
});
const customCreateProductOrderParameter = z.object({
  cartType: z.enum([PrismaCartType.hybrid, PrismaCartType.productOnly, PrismaCartType.ticketOnly]),
});
export const createProductOrderSchema = createOrderOptional
  .merge(createProductOrder)
  .merge(customCreateProductOrderParameter);
export type CreateProductOrderSchemaType = z.infer<typeof createProductOrderSchema>;

// REGION: get order by order id
export const getOrderByIdParamSchema = z.object({
  id: z.string().transform((id) => Number(id)),
});
export const getOrderByIdQuerySchema = languageQuerySchema;
export type GetOrderByIdRequestType = {
  params: z.infer<typeof getOrderByIdParamSchema>;
  query: z.infer<typeof getOrderByIdQuerySchema>;
};
const orderSummary = z.object({
  orderTotalQuantity: z.number(),
  orderTotalAmount: z.number(),
  orderSubTotal: z.number(),
  orderShippingFee: z.number(),
  orderTaxAmount: z.number(),
  orderDiscountAmount: z.number(),
});
const orderItem = z.object({
  orderItemId: z.number(),
  productId: z.number(),
  skuId: z.number(),
  productName: z.string(),
  thumbnail: z.string(),
  price: z.object({
    unitPrice: z.number(),
    originalPrice: z.number(),
    totalPrice: z.number(),
  }),
  skuAttribute: attributeSchema.single,
  quantity: z.number(),
});
const orderItemEvent = z.object({
  event_id: z.number(),
  eventName: z.string(),
});
const contract = z.object({
  name: z.string(),
  countryCode: z.string(),
  tel: z.string(),
});
const orderContact = contract.merge(z.object({ email: emailSchema }));
const orderShipping = contract.merge(
  z.object({
    shippingType: z
      .enum([PrismaOrderShippingType.express, PrismaOrderShippingType.storePickup])
      .nullish(),
    address: z.string(),
  }),
);
const orderBilling = contract.merge(z.object({ address: z.string() }));
const paymentMethod = z.object({
  paymentCode: z.string(),
  paymentPlatform: z.string(),
  paymentType: z.string(),
});
export const getOrderByIdResponseSchema = z.object({
  orderId: z.number(),
  userId: z.number(),
  orderType: z.enum([PrismaOrderType.product, PrismaOrderType.ticket]),
  region: z.string(),
  currency: z.string(),
  orderStatus: z.enum([
    PrismaOrderStatus.inProgress,
    PrismaOrderStatus.complete,
    PrismaOrderStatus.cancel,
  ]),
  orderAdminNote: z.string(),
  orderUserNote: z.string(),
  orderExpireAt: z.number(),
  orderSummary: orderSummary,
  paymentMethod: paymentMethod.array(),
  events: orderItemEvent.array(),
  orderItems: orderItem.array(),
  orderContact: orderContact,
  orderShipping: orderShipping,
  orderBilling: orderBilling,
});
export type GetOrderByIdResponseType = z.infer<typeof getOrderByIdResponseSchema>;

// REGION: orderPayment
export const orderPaymentBodySchema = z.object({
  orderId: z.number(),
  shippingType: z.enum([PrismaOrderShippingType.express, PrismaOrderShippingType.storePickup]),
  paymentMethod: z.string(),
  paymentPlatform: z.enum([
    PaymentPlatform.airwallex,
    PaymentPlatform.weChatPay,
    PaymentPlatform.alipay,
  ]),
  paymentType: z.enum([PaymentType.redirect, PaymentType.qrCode]),
  orderContact: orderContact,
  orderShipping: orderShipping.optional(),
  orderBilling: orderBilling,
});
export type OrderPaymentBodySchemaType = z.infer<typeof orderPaymentBodySchema>;
export type OrderPaymentRequestSchemaType = {
  body: OrderPaymentBodySchemaType;
  query: z.infer<typeof languageQuerySchema>;
};

// REGION: update order shipping
export const updateOrderShippingBodySchema = z.object({
  orderId: z.number(),
  shippingType: z.enum([PrismaOrderShippingType.express, PrismaOrderShippingType.storePickup]),
  orderShipping: orderShipping.optional(),
});
export type UpdateOrderShippingBodyType = z.infer<typeof updateOrderShippingBodySchema>;
export type UpdateOrderShippingRequestType = {
  body: UpdateOrderShippingBodyType;
  query: z.infer<typeof languageQuerySchema>;
};

// REGION: getOrderList request schema
const orderListSort = [
  'orderStatus',
  'orderNo',
  'userName',
  'userEmail',
  'orderAmount',
  'shippingStatus',
  'create_at',
] as const;
const searchFieldSchema = z.object({
  orderNo: z.string().optional(),
  sort: z.enum(orderListSort).optional(),
});
export const getOrderListQuerySchema = paginationQuerySchema
  .merge(languageQuerySchema)
  .merge(searchFieldSchema);
export type GetOrderListRequestType = z.infer<typeof getOrderListQuerySchema>;

const orderListSchema = z.object({
  orderId: z.number(),
  orderNo: z.string(),
  orderStatus: z.string(),
  userEmail: z.string(),
  userName: z.string(),
  orderAmount: z.number(),
  currency: z.string(),
  shippingType: z.string().optional(),
  shippingStatus: z.string().optional(),
  create_at: z.number(),
});
export const getOrderListResponse = PaginatedDataSchema(orderListSchema);

const saleOrderExtraInfo = z.object({
  paymentMethod: z.string(),
  paymentPlatform: z.string(),
});
const saleOrderShipping = orderShipping.merge(
  z.object({
    shippingStatus: z.enum([
      PrismaOrderShippingStatus.pending,
      PrismaOrderShippingStatus.delivered,
    ]),
  }),
);
export const getSaleOrderByIdResponseSchema = getOrderByIdResponseSchema
  .omit({ paymentMethod: true, orderShipping: true })
  .merge(saleOrderExtraInfo)
  .merge(
    z.object({
      orderNo: z.string(),
      txnRefNo: z.string(),
      orderShipping: saleOrderShipping,
      createAt: z.number(),
    }),
  );

// REGION: Update order delivery status
export const updateOrderShippingStatusSchema = z.object({
  orderId: z.number(),
  shippingStatus: z.enum([PrismaOrderShippingStatus.delivered, PrismaOrderShippingStatus.pending]),
});
export type UpdateOrderShippingStatusType = z.infer<typeof updateOrderShippingStatusSchema>;

// REGION: Batch update order delivery status
export const batchUpdateOrderShippingStatusSchema = z.object({
  orderIds: z.number().array(),
  shippingStatus: z.enum([PrismaOrderShippingStatus.delivered, PrismaOrderShippingStatus.pending]),
});
export type BatchUpdateOrderShippingStatusType = z.infer<
  typeof batchUpdateOrderShippingStatusSchema
>;

// REGION: update order status
export const updateOrderStatusSchema = z.object({
  orderId: z.number(),
  orderStatus: z.enum([
    PrismaOrderStatus.cancel,
    PrismaOrderStatus.inProgress,
    PrismaOrderStatus.complete,
  ]),
});
export type UpdateOrderStatusType = z.infer<typeof updateOrderStatusSchema>;


// REGION: update order status
export const updateOrderAdminNoteSchema = z.object({
  orderId: z.number(),
  remark: z.string(),
});
export type UpdateOrderAdminNoteType = z.infer<typeof updateOrderAdminNoteSchema>;
