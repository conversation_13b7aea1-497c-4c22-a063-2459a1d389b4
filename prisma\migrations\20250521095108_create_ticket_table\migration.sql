/*
  Warnings:

  - You are about to drop the `Event` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `EventMedia` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `EventQAA` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `EventSetting` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `EventTerms` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `Event` DROP FOREIGN KEY `Event_parentId_fkey`;

-- DropForeignKey
ALTER TABLE `EventMedia` DROP FOREIGN KEY `EventMedia_eventId_fkey`;

-- DropForeignKey
ALTER TABLE `EventQAA` DROP FOREIGN KEY `EventQAA_eventId_fkey`;

-- DropForeignKey
ALTER TABLE `EventSetting` DROP FOREIGN KEY `EventSetting_eventId_fkey`;

-- DropFore<PERSON>Key
ALTER TABLE `EventTerms` DROP FOREIGN KEY `EventTerms_eventId_fkey`;

-- DropTable
DROP TABLE `Event`;

-- DropTable
DROP TABLE `EventMedia`;

-- DropTable
DROP TABLE `EventQAA`;

-- DropTable
DROP TABLE `EventSetting`;

-- DropTable
DROP TABLE `EventTerms`;

-- CreateTable
CREATE TABLE `event` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `name_en` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `region_code` ENUM('MY', 'HK') NULL,
    `timezone` ENUM('MST', 'HKT') NULL,
    `start_date` INTEGER NULL,
    `end_date` INTEGER NULL,
    `start_time` INTEGER NULL,
    `end_time` INTEGER NULL,
    `duration` INTEGER NULL,
    `venue` VARCHAR(191) NULL,
    `status` ENUM('DRAFT', 'PUBLISHED', 'ACTIVE', 'INACTIVE') NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `parent_id` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `event_media` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `src` VARCHAR(191) NOT NULL,
    `title` TEXT NULL,
    `description` TEXT NULL,
    `seq` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `event_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `event_setting` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `content` TEXT NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `event_id` INTEGER NOT NULL,

    UNIQUE INDEX `event_setting_event_id_key`(`event_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `event_qaa` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `question` TEXT NOT NULL,
    `answer` TEXT NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `event_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `event_terms` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `content` TEXT NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `event_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_setting` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `sale_start_datetime` INTEGER NULL,
    `sale_end_datetime` INTEGER NULL,
    `currency` ENUM('MYD', 'HKD') NOT NULL,
    `sale_time_thershold` INTEGER NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `event_id` INTEGER NOT NULL,

    UNIQUE INDEX `ticket_setting_event_id_key`(`event_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_section` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `day` INTEGER NULL,
    `date` INTEGER NULL,
    `start_time` INTEGER NOT NULL,
    `end_time` INTEGER NOT NULL,
    `quantity` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_setting_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_variation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `type` ENUM('TIME', 'AGE') NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_setting_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_variation_option` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `value` VARCHAR(191) NULL,
    `from` INTEGER NULL,
    `to` INTEGER NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_variation_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_type` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `multiply` INTEGER NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NOT NULL,
    `is_all_day` BOOLEAN NOT NULL,
    `pre_sale_start_time` INTEGER NULL,
    `pre_sale_end_time` INTEGER NULL,
    `price` INTEGER NULL,
    `price_unit` VARCHAR(191) NULL,
    `price_unit_en` VARCHAR(191) NULL,
    `allow_multiple_entry` BOOLEAN NOT NULL,
    `have_gift` BOOLEAN NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_setting_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_type_to_section` (
    `ticket_type_id` INTEGER NOT NULL,
    `ticket_section_id` INTEGER NOT NULL,

    UNIQUE INDEX `ticket_type_to_section_ticket_type_id_ticket_section_id_key`(`ticket_type_id`, `ticket_section_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_type_to_variation` (
    `ticket_type_id` INTEGER NOT NULL,
    `ticket_variation_id` INTEGER NOT NULL,

    UNIQUE INDEX `ticket_type_to_variation_ticket_type_id_ticket_variation_id_key`(`ticket_type_id`, `ticket_variation_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_to_gift` (
    `ticket_type_id` INTEGER NOT NULL,
    `product_id` INTEGER NOT NULL,

    UNIQUE INDEX `ticket_to_gift_ticket_type_id_product_id_key`(`ticket_type_id`, `product_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `status` ENUM('ISSUING', 'ISSUED', 'USED', 'EXPIRED', 'VOIDED') NOT NULL,
    `qrcode` VARCHAR(191) NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `user_id` INTEGER NOT NULL,
    `ticket_type_id` INTEGER NOT NULL,
    `ticket_section_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `entry_record` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `redeem_gift_record` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_id` INTEGER NOT NULL,
    `product_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `event` ADD CONSTRAINT `event_parent_id_fkey` FOREIGN KEY (`parent_id`) REFERENCES `event`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_media` ADD CONSTRAINT `event_media_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_setting` ADD CONSTRAINT `event_setting_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_qaa` ADD CONSTRAINT `event_qaa_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `event_terms` ADD CONSTRAINT `event_terms_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_setting` ADD CONSTRAINT `ticket_setting_event_id_fkey` FOREIGN KEY (`event_id`) REFERENCES `event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_section` ADD CONSTRAINT `ticket_section_ticket_setting_id_fkey` FOREIGN KEY (`ticket_setting_id`) REFERENCES `ticket_setting`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_variation` ADD CONSTRAINT `ticket_variation_ticket_setting_id_fkey` FOREIGN KEY (`ticket_setting_id`) REFERENCES `ticket_setting`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_variation_option` ADD CONSTRAINT `ticket_variation_option_ticket_variation_id_fkey` FOREIGN KEY (`ticket_variation_id`) REFERENCES `ticket_variation`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_type` ADD CONSTRAINT `ticket_type_ticket_setting_id_fkey` FOREIGN KEY (`ticket_setting_id`) REFERENCES `ticket_setting`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_type_to_section` ADD CONSTRAINT `ticket_type_to_section_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_type_to_section` ADD CONSTRAINT `ticket_type_to_section_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_type_to_variation` ADD CONSTRAINT `ticket_type_to_variation_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_type_to_variation` ADD CONSTRAINT `ticket_type_to_variation_ticket_variation_id_fkey` FOREIGN KEY (`ticket_variation_id`) REFERENCES `ticket_variation`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_to_gift` ADD CONSTRAINT `ticket_to_gift_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_to_gift` ADD CONSTRAINT `ticket_to_gift_product_id_fkey` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `entry_record` ADD CONSTRAINT `entry_record_ticket_id_fkey` FOREIGN KEY (`ticket_id`) REFERENCES `ticket`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `redeem_gift_record` ADD CONSTRAINT `redeem_gift_record_ticket_id_fkey` FOREIGN KEY (`ticket_id`) REFERENCES `ticket`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `redeem_gift_record` ADD CONSTRAINT `redeem_gift_record_product_id_fkey` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
