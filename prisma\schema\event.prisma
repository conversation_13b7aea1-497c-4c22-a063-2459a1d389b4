model event {
  id           Int           @id @default(autoincrement())
  name         String
  name_en       String?
  description  String?
  region_code   RegionCode?
  timezone     TimeZone?
  start_date    Int?
  end_date      Int?
  start_time    Int?
  end_time      Int?
  duration     Int?
  venue        String?
  status       EventStatus
  created_at    Int
  updated_at    Int

  parent_id     Int?

  sub_event     event[] @relation("ip_to_event")
  parent_event  event? @relation("ip_to_event", fields: [parent_id], references: [id])
  event_media   event_media[] @relation("event_media")
  event_setting event_setting? @relation("event_setting")
  event_qaa     event_qaa[] @relation("event_qaa")
  event_terms    event_terms? @relation("event_terms")
  products      products[] @relation("event_products")
  ticket_setting ticket_setting? @relation("TicketSetting")
}

model event_media {
  id           Int           @id @default(autoincrement())
  src          String
  title        String?       @db.Text
  description  String?       @db.Text
  seq          Int
  created_at    Int
  updated_at    Int

  event_id      Int
  event_info    event @relation("event_media", fields: [event_id], references: [id], onDelete: Cascade)
}

model event_setting {
  id           Int           @id @default(autoincrement())
  content      String        @db.Text
  created_at    Int
  updated_at    Int

  event_id      Int           @unique
  event_info    event @relation("event_setting", fields: [event_id], references: [id], onDelete: Cascade)
}

model event_qaa {
  id           Int           @id @default(autoincrement())
  question     String        @db.Text
  answer       String        @db.Text
  created_at    Int
  updated_at    Int

  event_id      Int
  event_info    event @relation("event_qaa", fields: [event_id], references: [id], onDelete: Cascade)
}

model event_terms {
  id           Int           @id @default(autoincrement())
  content      String        @db.Text
  created_at    Int
  updated_at    Int

  event_id      Int          @unique
  event_info    event @relation("event_terms", fields: [event_id], references: [id], onDelete: Cascade)
}

enum RegionCode {
  MY
  HK
}

enum TimeZone {
  MST
  HKT
}

enum EventStatus {
  DRAFT
  PUBLISHED
  ACTIVE
  INACTIVE
  EXPIRED
}