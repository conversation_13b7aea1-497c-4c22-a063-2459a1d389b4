import db from '../client/index';
import { default as eventSeed } from './seed.event';
import { default as countrySeed } from './seed.country';
import { default as productSeed } from './seed.products';
import { default as productMediaSeed } from './seed.product-media';
import { default as paymentMethodSeed } from './paymentMethod.seed';
import { default as globalConfigSeed } from './globalConfig.seed';

import '@config/dayjsConfig';

const runSeeds = [
  countrySeed,
  // eventSeed,
  // productSeed,
  // productMediaSeed,
  // paymentMethodSeed,
  // globalConfigSeed,
];
// const runSeeds = [paymentMethodSeed];

runSeeds
  .reduce((p, fn) => p.then(fn), Promise.resolve())
  .then(async () => {
    await db.$disconnect();
    console.log('[Prisma] Seed insert to database success!');
  })
  .catch(async (e) => {
    console.error(e);
    await db.$disconnect();
    process.exit(1);
  });
