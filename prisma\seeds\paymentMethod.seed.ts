import { $Enums } from '@prisma/client';
import db from '../client/index';
import { generateTimeNow } from '@/utils/common';

const dataPaymentMethod = [
  {
    payment_code: 'alipayhk',
    payment_name: 'AlipayHK',
    payment_region: 'HK',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'alipayhk',
    payment_name: 'AlipayHK',
    payment_region: 'MO',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'fps',
    payment_name: 'FPS',
    payment_region: 'HK',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'fps',
    payment_name: 'FPS',
    payment_region: 'MO',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'wechatpay',
    payment_name: 'WeChat Pay',
    payment_region: 'HK',
    payment_platform: 'WeChatPay',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.QRCODE,
  },
  {
    payment_code: 'wechatpay',
    payment_name: 'WeChat Pay',
    payment_region: 'MO',
    payment_platform: 'WeChatPay',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.QRCODE,
  },
  {
    payment_code: 'wechatpay',
    payment_name: 'WeChat Pay',
    payment_region: 'TW',
    payment_platform: 'WeChatPay',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.QRCODE,
  },
  {
    payment_code: 'dana',
    payment_name: 'DANA',
    payment_region: 'ID',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'bank_transfer',
    payment_name: 'Bank Transfer',
    payment_region: 'ID',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'grabpay',
    payment_name: 'GrabPay',
    payment_region: 'MY',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'tng',
    payment_name: "Touch 'n Go eWallet",
    payment_region: 'MY',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'truemoney',
    payment_name: 'TrueMoney',
    payment_region: 'TH',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'prompt_pay',
    payment_name: 'PromptPay',
    payment_region: 'TH',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'online_banking',
    payment_name: 'Online Banking',
    payment_region: 'TH',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'kakaopay',
    payment_name: 'KakaoPay',
    payment_region: 'KR',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'alfamart',
    payment_name: 'Alfamart Payment',
    payment_region: 'TW',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'esun',
    payment_name: 'E.SUN Bank',
    payment_region: 'TW',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'HK',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'ID',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'MY',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'TH',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'KR',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'TW',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
  {
    payment_code: 'card',
    payment_name: 'Credit/Debit Card',
    payment_region: 'MO',
    payment_platform: 'Airwallex',
    status: 'enabled',
    createdAt: '2025-06-17T00:00:00Z',
    updatedAt: '2025-06-17T00:00:00Z',
    payment_type: $Enums.payment_type.REDIRECT,
  },
];

// Seed for LabPay payment
export default async () => {
  const now = generateTimeNow();

  // Retrieve db payment method
  const oldPaymentMethod = await db.payment_method.findMany({
    select: { payment_code: true, payment_region: true },
  });

  // filter duplicate payment method
  const newPaymentMethod = dataPaymentMethod.filter((data) => {
    return !oldPaymentMethod.some(
      (x) => x.payment_code === data.payment_code && x.payment_region === data.payment_region,
    );
  });

  const paymentMethods = newPaymentMethod.map((data) => ({
    payment_code: data.payment_code,
    payment_name: data.payment_name,
    payment_region: data.payment_region,
    payment_platform: data.payment_platform,
    payment_type: $Enums.payment_type.REDIRECT,
    status: $Enums.payment_status.enabled,
    createdAt: now,
    updatedAt: now,
  }));

  if (paymentMethods?.length > 0) {
    await db.payment_method.createMany({
      data: paymentMethods,
    });
  }

  console.log('[Prisma] Seed payment method insert progress.');
};
