import { OrderCheckStrategyParameterType } from '@modules/order/schema/strategy.schema';
import { StrategyMap, UseCase } from '@modules/order/enum/useCase.enum';

import {
  updateOrderShippingStatusStrategyMap,
  batchUpdateOrderShippingStatusStrategyMap,
  updateOrderStatusStrategyMap,
} from '../strategy';

type ParameterType = OrderCheckStrategyParameterType;
export class ValidatorContext {
  private static validateStrategy: StrategyMap<ParameterType> = {
    [UseCase.updateOrderShippingStatus]: updateOrderShippingStatusStrategyMap,
    [UseCase.batchUpdateOrderShippingStatus]: batchUpdateOrderShippingStatusStrategyMap,
    [UseCase.updateOrderStatus]: updateOrderStatusStrategyMap,
    [UseCase.updateOrderAdminNote]: updateOrderStatusStrategyMap,
  };

  public static execute = async (useCase: UseCase, parameter: OrderCheckStrategyParameterType) => {
    for (const strategy of ValidatorContext.validateStrategy[useCase]) {
      await strategy(parameter as ParameterType);
    }
  };
}
