import { handleAsync, validateRequest } from '@middleware';
import { requestSchema } from '@shared/schema/request.schema';
import { sendSuccessResponse } from '@utils/apiResponse';

import * as productService from './product.service';
import * as productSchema from './schema/product.schema';

export const getAllProducts = [
  validateRequest(requestSchema.query(productSchema.getAllProductsQuerySchema)),
  handleAsync(async (req, res) => {
    const { region, eventId, page, size, sort, sortOrder, language } = req.validatedData
      ?.query as productSchema.GetAllProductsRequestType;
    const { result, count } = await productService.retrieveAllProducts(
      page,
      size,
      language,
      sort,
      sortOrder,
      region,
      eventId,
    );

    const paginatedData = productSchema.getAllProductsResponse.parse({
      items: result,
      meta: {
        page: Number(page),
        size: Number(size),
        total: count,
      },
    });

    return sendSuccessResponse(res, paginatedData);
  }),
];

export const getProductsSearchSortFields = handleAsync(async (req, res) => {
  const result = await productService.retrieveSortFields();

  const productSortFields = productSchema.getProductsSearchSortFieldsResponse.parse(result);

  return sendSuccessResponse(res, productSortFields);
});

export const getProductById = [
  validateRequest(
    requestSchema.all({
      params: productSchema.getProductByIdParamSchema,
      query: productSchema.getProductByIdQuerySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const {
      params: { id: productId },
      query: { language },
    } = req.validatedData as productSchema.GetProductByIdRequestType;
    const result = await productService.retrieveProductById(productId, language);

    const data = productSchema.getProductByIdResponseSchema.parse(result);

    return sendSuccessResponse(res, data);
  }),
];

export const getRelatedProductsById = [
  validateRequest(
    requestSchema.all({
      params: productSchema.getRelatedProductsByIdParamSchema,
      query: productSchema.getRelatedByIdQuerySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const {
      params: { id: productId },
      query: { language },
    } = req.validatedData as productSchema.GetProductByIdRequestType;

    const { result, count } = await productService.retrieveRelatedProductsById(productId, language);

    const paginatedData = productSchema.getRelatedProductsResponse.parse({
      items: result,
      meta: {
        page: 1, // FIXME: hardcode
        size: 100, // FIXME: hardcode
        total: count,
      },
    });

    return sendSuccessResponse(res, paginatedData);
  }),
];

export const getProductSearchCriteria = [
  validateRequest(requestSchema.query(productSchema.getProductSearchCriteriaQuerySchema)),
  handleAsync(async (req, res) => {
    const { language } = req.validatedData?.query as productSchema.GetProductSearchCriteriaType;

    const result = await productService.retrieveProductSearchCriteria(language);
    const productSearchCriteria =
      productSchema.getProductSearchCriteriaResponseSchema.parse(result);

    return sendSuccessResponse(res, productSearchCriteria);
  }),
];

export const addProduct = [
  validateRequest(requestSchema.body(productSchema.createProductBodySchema)),
  handleAsync(async (req, res) => {
    const createProduct = req.validatedData?.body as productSchema.CreateProductRequestType;

    await productService.createProduct(createProduct);

    return sendSuccessResponse(res, {});
  }),
];

export const updateProductStatus = [
  validateRequest(
    requestSchema.all({
      params: productSchema.updateProductStatusParamSchema,
      body: productSchema.updateProductStatusBodySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const {
      params: { id: productId },
      body: { status },
    } = req.validatedData as productSchema.UpdateProductStatusRequestType;

    await productService.updateProductStatus({ productId, status });

    return sendSuccessResponse(res, {});
  }),
];

export const batchUpdateProductStatus = [
  validateRequest(requestSchema.body(productSchema.batchUpdateProductStatusBodySchema)),
  handleAsync(async (req, res) => {
    const batchUpdateParameter = req.validatedData
      .body as productSchema.BatchUpdateProductStatusRequestType;

    await productService.batchPublishProduct(batchUpdateParameter);

    return sendSuccessResponse(res, {});
  }),
];
