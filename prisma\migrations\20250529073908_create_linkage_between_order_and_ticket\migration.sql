/*
  Warnings:

  - Added the required column `ticket_section_id` to the `order_items` table without a default value. This is not possible if the table is not empty.
  - Added the required column `ticket_type_id` to the `order_items` table without a default value. This is not possible if the table is not empty.
  - Added the required column `order_item_id` to the `ticket` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `order_items` ADD COLUMN `ticket_section_id` INTEGER NOT NULL,
    ADD COLUMN `ticket_type_id` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `ticket` ADD COLUMN `order_item_id` INTEGER NOT NULL,
    ADD COLUMN `url` VARCHAR(191) NULL;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_order_item_id_fkey` FOREIGN KEY (`order_item_id`) REFERENCES `order_items`(`order_item_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
