import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { checkAttributeValidStrategy } from '../implementations/checkAttributeValid.strategy';
import {
  checkDuplicateSkuCode,
  checkEventStrategy,
  checkPickupDateValidStrategy,
  checkPriceStrategy,
  checkRegionStrategy,
  checkSaleDateValidStrategy,
} from '../implementations';

export const addProductStrategyMap: ProductCheckStrategy<ProductCheckStrategyParameterType>[] = [
  checkAttributeValidStrategy,
  checkDuplicateSkuCode,
  checkEventStrategy,
  checkPickupDateValidStrategy,
  checkPriceStrategy,
  checkRegionStrategy,
  checkSaleDateValidStrategy,
];
