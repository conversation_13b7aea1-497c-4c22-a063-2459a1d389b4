// import { PrismaClient } from '@prisma/client';
// import { generateTimeNow } from '@utils/common';
// import { FeaturedEventDTO } from './featuredEvents.dto';

// const prisma = new PrismaClient();

// const findAll = async (region: string, category: string, sortOrder = 'desc', page = 1) => {
//   const whereConditions: { region?: string; category?: string } = {};

//   if (region) whereConditions.region = region;
//   if (category) whereConditions.category = category;

//   const uniqueRegions = await prisma.featuredEvents.findMany({
//     select: {
//       region: true,
//     },
//     distinct: ['region'],
//   });

//   const totalCount = await prisma.featuredEvents.count({
//     where: whereConditions,
//   });

//   const eventList = await prisma.featuredEvents.findMany({
//     where: whereConditions,
//     orderBy: {
//       createdAt: sortOrder === 'desc' ? 'desc' : 'asc',
//     },
//     skip: (page - 1) * 10,
//     take: 10,
//   });

//   return {
//     events: eventList,
//     totalPages: Math.ceil(totalCount / 10),
//     uniqueRegions: uniqueRegions.map((item) => item.region),
//   };
// };

// const findById = async (id: number) => {
//   const selectedProduct = await prisma.featuredEvents.findFirst({
//     where: {
//       id,
//     },
//   });

//   return selectedProduct;
// };

// const create = async (userId: number, featuredEventDTO: FeaturedEventDTO) => {
//   const timenow = generateTimeNow();

//   try {
//     const createdEvent = await prisma.featuredEvents.create({
//       data: {
//         eventName: featuredEventDTO.eventName,
//         description: featuredEventDTO.description,
//         eventStartDate: featuredEventDTO.eventStartDate,
//         eventEndDate: featuredEventDTO.eventEndDate,
//         region: featuredEventDTO.region,
//         timeZone: featuredEventDTO.timeZone,
//         eventTime: featuredEventDTO.eventTime,
//         aboutTime: featuredEventDTO.aboutTime,
//         thumbnail: featuredEventDTO?.thumbnail,
//         Venue: featuredEventDTO.Venue,
//         category: featuredEventDTO.category,
//         createdAt: timenow,
//         userId: userId,
//       },
//     });

//     return createdEvent;
//   } finally {
//     await prisma.$disconnect();
//   }
// };

// export default {
//   findAll,
//   findById,
//   create,
// };
