import { $Enums } from '@prisma/client';

export const PrismaCartType = $Enums.cart_type;
export const PrismaCartItemType = $Enums.cart_item_type;
export const PrismaCartActivityType = $Enums.cart_activity_type;

export type CartType =
  | typeof PrismaCartType.hybrid
  | typeof PrismaCartType.productOnly
  | typeof PrismaCartType.ticketOnly;

export type CartItemType = typeof PrismaCartItemType.product | typeof PrismaCartItemType.ticket;

export type CartActivityType =
  | typeof PrismaCartActivityType.addCartItem
  | typeof PrismaCartActivityType.checkout
  | typeof PrismaCartActivityType.clearCart
  | typeof PrismaCartActivityType.removeCartItem
  | typeof PrismaCartActivityType.selectCartItem
  | typeof PrismaCartActivityType.unSelectCartItem
  | typeof PrismaCartActivityType.updateQuantity;
