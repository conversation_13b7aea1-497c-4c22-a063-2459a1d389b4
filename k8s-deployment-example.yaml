# Example Kubernetes deployment configuration for <PERSON><PERSON>
# This file shows how to configure environment variables in Rancher Dashboard
# DO NOT apply this directly - use Rancher Dashboard to create the deployment

apiVersion: apps/v1
kind: Deployment
metadata:
  name: incutix-nodejs-api
  namespace: incutix-private  # or incutix-public for pre-prod/prod
  labels:
    app: incutix-nodejs-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: incutix-nodejs-api
  template:
    metadata:
      labels:
        app: incutix-nodejs-api
    spec:
      containers:
      - name: incutix-nodejs-api
        image: your-ecr-repo/incutix-nodejs-api:latest
        ports:
        - containerPort: 5000
          name: api-port
        - containerPort: 8080
          name: admin-port
        env:
        # Database Configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: incutix-db-secret
              key: database-url
        - name: DATABASE_READ_URL
          valueFrom:
            secretKeyRef:
              name: incutix-db-secret
              key: database-read-url
        
        # AWS Configuration
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: incutix-aws-secret
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: incutix-aws-secret
              key: secret-access-key
        - name: AWS_REGION
          value: "ap-southeast-1"
        
        # JWT Configuration
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: incutix-jwt-secret
              key: jwt-secret
        
        # Lab Pay Configuration
        - name: LAB_PAY_MERCHANT_APP_ID
          valueFrom:
            secretKeyRef:
              name: incutix-labpay-secret
              key: merchant-app-id
        - name: LAB_PAY_MERCHANT_ID
          valueFrom:
            secretKeyRef:
              name: incutix-labpay-secret
              key: merchant-id
        - name: LAB_PAY_KEY
          valueFrom:
            secretKeyRef:
              name: incutix-labpay-secret
              key: lab-pay-key
        
        # Application Configuration
        - name: NODE_ENV
          value: "production"  # or "uat" for UAT environment
        - name: FRONT_END_URL
          value: "https://dev-market.incutix.com"  # Update per environment
        - name: S3_PUBLIC
          value: "https://dev-market.incutix.com"  # Update per environment
        - name: S3_BUCKET_NAME
          value: "dev-web-incutix.fun-verse.io"  # Update per environment
        - name: S3_BUCKET_PRIVATE
          value: "dev-incutix"  # Update per environment
        - name: ENCRYPT_METHOD
          value: "MD5"
        - name: NOTIFY_URL
          value: "https://dev-api2.incutix.com"  # Update per environment
        - name: RETURN_URL
          value: "https://dev-market.incutix.com"  # Update per environment
        - name: LAB_PAY_API_URL
          value: "https://wapi.uat.hk.lab-pay.com"  # Update per environment
        - name: SENDER_EMAIL
          value: "<EMAIL>"
        - name: SUPPORT_EMAIL
          value: "<EMAIL>,<EMAIL>"
        - name: ERROR_EMAIL
          value: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
        
        # Health check
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        
        # Resource limits
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
# Service configuration
apiVersion: v1
kind: Service
metadata:
  name: incutix-nodejs-api-service
  namespace: incutix-private  # or incutix-public for pre-prod/prod
  labels:
    app: incutix-nodejs-api
spec:
  selector:
    app: incutix-nodejs-api
  ports:
  - name: api-port
    port: 5000
    targetPort: 5000
    protocol: TCP
  - name: admin-port
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP  # or LoadBalancer if you need external access

---
# Example secrets that need to be created in Rancher Dashboard
# These are just examples - create them through Rancher UI

# Database Secret
apiVersion: v1
kind: Secret
metadata:
  name: incutix-db-secret
  namespace: incutix-private  # or incutix-public for pre-prod/prod
type: Opaque
data:
  # Base64 encoded values - use Rancher Dashboard to create these
  database-url: <base64-encoded-database-url>
  database-read-url: <base64-encoded-database-read-url>

---
# AWS Secret
apiVersion: v1
kind: Secret
metadata:
  name: incutix-aws-secret
  namespace: incutix-private  # or incutix-public for pre-prod/prod
type: Opaque
data:
  # Base64 encoded values - use Rancher Dashboard to create these
  access-key-id: <base64-encoded-aws-access-key-id>
  secret-access-key: <base64-encoded-aws-secret-access-key>

---
# JWT Secret
apiVersion: v1
kind: Secret
metadata:
  name: incutix-jwt-secret
  namespace: incutix-private  # or incutix-public for pre-prod/prod
type: Opaque
data:
  # Base64 encoded values - use Rancher Dashboard to create these
  jwt-secret: <base64-encoded-jwt-secret>

---
# Lab Pay Secret
apiVersion: v1
kind: Secret
metadata:
  name: incutix-labpay-secret
  namespace: incutix-private  # or incutix-public for pre-prod/prod
type: Opaque
data:
  # Base64 encoded values - use Rancher Dashboard to create these
  merchant-app-id: <base64-encoded-merchant-app-id>
  merchant-id: <base64-encoded-merchant-id>
  lab-pay-key: <base64-encoded-lab-pay-key>
