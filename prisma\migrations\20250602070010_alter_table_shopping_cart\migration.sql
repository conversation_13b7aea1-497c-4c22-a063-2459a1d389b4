/*
  Warnings:

  - You are about to alter the column `cart_type` on the `shopping_cart` table. The data in that column could be lost. The data in that column will be cast from `TinyInt` to `Enum(EnumId(12))`.
  - You are about to alter the column `cart_item_type` on the `shopping_cart_item` table. The data in that column could be lost. The data in that column will be cast from `TinyInt` to `Enum(EnumId(13))`.
  - A unique constraint covering the columns `[user_ref_id]` on the table `User` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[cart_id,cart_item_type,product_id,sku_id]` on the table `shopping_cart_item` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `token` to the `User` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_ref_id` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `shopping_cart` DROP FOREIGN KEY `shopping_cart_ibfk_1`;

-- AlterTable
ALTER TABLE `User` ADD COLUMN `token` VARCHAR(200) NOT NULL,
    ADD COLUMN `user_ref_id` VARCHAR(36) NOT NULL;

-- AlterTable
ALTER TABLE `shopping_cart` MODIFY `user_id` CHAR(36) NOT NULL,
    MODIFY `cart_type` ENUM('productOnly', 'ticketOnly', 'hybrid') NOT NULL;

-- AlterTable
ALTER TABLE `shopping_cart_item` ADD COLUMN `is_selected` BOOLEAN NOT NULL DEFAULT false,
    MODIFY `cart_item_type` ENUM('product', 'ticket') NOT NULL;

-- CreateTable
CREATE TABLE `shopping_cart_activity_log` (
    `shopping_cart_activity_log` INTEGER NOT NULL AUTO_INCREMENT,
    `cart_id` INTEGER NOT NULL,
    `user_id` CHAR(36) NOT NULL,
    `cart_activity_type` ENUM('addCartItem', 'removeCartItem', 'updateQuantity', 'clearCart', 'checkout', 'selectCartItem', 'unSelectCartItem') NOT NULL,
    `shopping_cart_item_id` INTEGER NOT NULL,
    `old_value` JSON NOT NULL,
    `new_value` JSON NOT NULL,
    `extra_info` JSON NOT NULL,
    `operation_time` BIGINT NOT NULL,

    INDEX `idx_cart_id`(`cart_id`),
    INDEX `idx_user_id_operation`(`user_id`, `operation_time`),
    INDEX `idx_operation_time`(`operation_time`),
    INDEX `idx_shopping_cart_item_id`(`shopping_cart_item_id`),
    PRIMARY KEY (`shopping_cart_activity_log`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `shopping_cart_checkout_idempotency_token` (
    `idempotency_key` VARCHAR(128) NOT NULL,
    `status` ENUM('unused', 'used', 'expired', 'failed') NOT NULL,
    `created_at` INTEGER NOT NULL,
    `expired_at` INTEGER NOT NULL,

    INDEX `idx_created_at`(`created_at`),
    PRIMARY KEY (`idempotency_key`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `User_user_ref_id_key` ON `User`(`user_ref_id`);

-- CreateIndex
CREATE INDEX `user_ref_id_idx` ON `User`(`user_ref_id`);

-- CreateIndex
CREATE INDEX `token_idx` ON `User`(`token`);

-- CreateIndex
CREATE INDEX `idx_cart_type` ON `shopping_cart`(`cart_type`);

-- CreateIndex
CREATE INDEX `idx_uid_cart_check` ON `shopping_cart`(`user_id`, `cart_type`, `is_checked_out`);

-- CreateIndex
CREATE UNIQUE INDEX `uk_cart_item` ON `shopping_cart_item`(`cart_id`, `cart_item_type`, `product_id`, `sku_id`);

-- AddForeignKey
ALTER TABLE `shopping_cart` ADD CONSTRAINT `shopping_cart_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`user_ref_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `shopping_cart_item` ADD CONSTRAINT `shopping_cart_item_cart_id_fkey` FOREIGN KEY (`cart_id`) REFERENCES `shopping_cart`(`cart_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
