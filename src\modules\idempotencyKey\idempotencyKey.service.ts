import db from '@prismaClient';
import crypto from 'node:crypto';

import { generateTimeNow, generateExpireAt } from '@/utils/common';
import { IdempotencyKeyType } from './schema/idempotencyKey.schema';
import { IdempotencyKeyStatus, PrismaIdempotencyKeyStatus } from './enum/idempotencyKey.enum';
import { PrismaTransaction } from '@/shared/types/prisma.type';

export const getIdempotencyKey = async (userId: number, param: IdempotencyKeyType) => {
  const now = generateTimeNow();
  const expireAt = generateExpireAt(30, 'minute');

  const requestHash = crypto.createHash('sha256').update(param.requestHash).digest('hex');

  const findCheckoutIdempotencyKey = await db.idempotency_keys.findFirst({
    select: { idempotency_key: true },
    where: {
      user_id: userId,
      route_path: param.routePath,
      request_hash: requestHash,
      expires_at: { gt: now },
      status: PrismaIdempotencyKeyStatus.unused,
    },
  });

  if (!findCheckoutIdempotencyKey) {
    const { idempotency_key: idempotencyKey } = await db.idempotency_keys.create({
      select: { idempotency_key: true },
      data: {
        idempotency_key: crypto.randomUUID(),
        user_id: userId,
        status: 'unused',
        http_method: param.httpMethod,
        route_path: param.routePath,
        request_hash: requestHash,
        created_at: now,
        updated_at: now,
        expires_at: expireAt,
      },
    });
    return idempotencyKey;
  }
  return findCheckoutIdempotencyKey.idempotency_key;
};

export const updateIdempotencyKeyStatus = async (
  prisma: PrismaTransaction,
  idempotencyKey: string,
  status: IdempotencyKeyStatus,
) => {
  const now = generateTimeNow();

  await prisma.idempotency_keys.update({
    data: { status, updated_at: now },
    where: { idempotency_key: idempotencyKey },
  });
};

export const updateIdempotencyKeyResponse = async (idempotencyKey: string, response: any) => {
  const now = generateTimeNow();

  await db.idempotency_keys.update({
    data: { response_body: JSON.stringify(response), updated_at: now },
    where: { idempotency_key: idempotencyKey },
  });
};

export const retrieveIdempotencyKeyResponse = async (idempotencyKey: string) => {
  const findIdempotencyKey = await db.idempotency_keys.findUnique({
    select: { response_body: true },
    where: { idempotency_key: idempotencyKey },
  });

  if (!findIdempotencyKey || findIdempotencyKey?.response_body === null) {
    return undefined;
  }
  return JSON.parse(findIdempotencyKey.response_body?.toString() ?? '');
};
