import db from '../client/index';

type AttributeReturnType = {
  category: { colorId: number; sizeId: number };
  value: {
    color: { lightCoral: number; lightPink: number; mediumOrchid: number };
    size: { sizeS: number; sizeM: number; sizeL: number };
  };
};
export const attributeSeed = async (): Promise<AttributeReturnType | null> => {
  const count1 = await db.product_attribute_categories.count();
  const count2 = await db.product_attribute_values.count();
  const count3 = await db.product_attribute_translation.count();

  const isAdd = count1 + count2 + count3 === 0;

  if (!isAdd) {
    const { product_attribute_category_id: colorId } =
      (await db.product_attribute_categories.findFirst({
        select: { product_attribute_category_id: true },
        where: { category_code: 'COLOR' },
      })) ?? {};
    const { product_attribute_category_id: sizeId } =
      (await db.product_attribute_categories.findFirst({
        select: { product_attribute_category_id: true },
        where: { category_code: 'SIZE' },
      })) ?? {};

    const colorCollection = await db.product_attribute_values.findMany({
      select: {
        product_attribute_value_id: true,
        product_attribute_translation: {
          select: { language_code: true, name: true },
        },
      },
      where: {
        product_attribute_category_id: colorId,
      },
    });
    const sizeCollection = await db.product_attribute_values.findMany({
      select: {
        product_attribute_value_id: true,
        product_attribute_translation: {
          select: { language_code: true, name: true },
        },
      },
      where: {
        product_attribute_category_id: sizeId,
      },
    });

    // lightCoral, lightPink, mediumOrchid
    const colors: Record<string, number> = colorCollection.reduce((a, x) => {
      const attributeValuesColorLightCoral = x.product_attribute_translation.some(
        (y) => y.language_code === 'EN' && y.name === 'LightCoral',
      );
      const attributeValuesColorLightPink = x.product_attribute_translation.some(
        (y) => y.language_code === 'EN' && y.name === 'LightPink',
      );
      const attributeValuesColorMediumOrchid = x.product_attribute_translation.some(
        (y) => y.language_code === 'EN' && y.name === 'MediumOrchid',
      );

      if (attributeValuesColorLightCoral) {
        return { ...a, lightCoral: x.product_attribute_value_id };
      } else if (attributeValuesColorLightPink) {
        return { ...a, lightPink: x.product_attribute_value_id };
      } else if (attributeValuesColorMediumOrchid) {
        return { ...a, mediumOrchid: x.product_attribute_value_id };
      }
      return a;
    }, {});

    const sizes: Record<string, number> = sizeCollection.reduce((a, x) => {
      const sizeS = x.product_attribute_translation.some(
        (y) => y.language_code === 'EN' && y.name === 'S',
      );
      const sizeM = x.product_attribute_translation.some(
        (y) => y.language_code === 'EN' && y.name === 'M',
      );
      const sizeL = x.product_attribute_translation.some(
        (y) => y.language_code === 'EN' && y.name === 'L',
      );

      if (sizeS) {
        return { ...a, sizeS: x.product_attribute_value_id };
      } else if (sizeM) {
        return { ...a, sizeM: x.product_attribute_value_id };
      } else if (sizeL) {
        return { ...a, sizeL: x.product_attribute_value_id };
      }
      return a;
    }, {});

    return {
      category: { colorId: colorId ?? 0, sizeId: sizeId ?? 0 },
      value: {
        color: {
          lightCoral: colors.lightCoral,
          lightPink: colors.lightPink,
          mediumOrchid: colors.mediumOrchid,
        },
        size: { sizeS: sizes.sizeS, sizeM: sizes.sizeM, sizeL: sizes.sizeL },
      },
    };
  }

  const { product_attribute_category_id: colorId } = await db.product_attribute_categories.create({
    data: { category_code: 'COLOR' },
  });
  const { product_attribute_category_id: sizeId } = await db.product_attribute_categories.create({
    data: { category_code: 'SIZE' },
  });

  const attributeValuesColorLightCoral = await db.product_attribute_values.create({
    data: { product_attribute_category_id: colorId },
  });
  const attributeValuesColorLightPink = await db.product_attribute_values.create({
    data: { product_attribute_category_id: colorId },
  });
  const attributeValuesColorMediumOrchid = await db.product_attribute_values.create({
    data: { product_attribute_category_id: colorId },
  });

  const attributeValuesSizeS = await db.product_attribute_values.create({
    data: { product_attribute_category_id: sizeId },
  });
  const attributeValuesSizeM = await db.product_attribute_values.create({
    data: { product_attribute_category_id: sizeId },
  });
  const attributeValuesSizeL = await db.product_attribute_values.create({
    data: { product_attribute_category_id: sizeId },
  });

  await db.product_attribute_translation.createMany({
    data: [
      {
        language_code: 'EN',
        name: 'color',
        product_attribute_category_id: colorId,
      },
      {
        language_code: 'TC',
        name: '顏色',
        product_attribute_category_id: colorId,
      },
      {
        language_code: 'TS',
        name: '顏色',
        product_attribute_category_id: colorId,
      },
    ],
  });

  await db.product_attribute_translation.create({
    data: {
      language_code: 'EN',
      name: 'size',
      product_attribute_categories: {
        connect: { product_attribute_category_id: sizeId },
      },
    },
  });
  await db.product_attribute_translation.create({
    data: {
      language_code: 'TC',
      name: '尺碼',
      product_attribute_categories: {
        connect: { product_attribute_category_id: sizeId },
      },
    },
  });
  await db.product_attribute_translation.create({
    data: {
      language_code: 'TS',
      name: '尺碼',
      product_attribute_categories: {
        connect: { product_attribute_category_id: sizeId },
      },
    },
  });

  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorLightCoral.product_attribute_value_id,
      language_code: 'EN',
      name: 'LightCoral',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorLightCoral.product_attribute_value_id,
      language_code: 'TC',
      name: '淺珊瑚色',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorLightCoral.product_attribute_value_id,
      language_code: 'TS',
      name: '淺珊瑚色',
    },
  });

  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorLightPink.product_attribute_value_id,
      language_code: 'EN',
      name: 'LightPink',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorLightPink.product_attribute_value_id,
      language_code: 'TC',
      name: '淺粉紅色',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorLightPink.product_attribute_value_id,
      language_code: 'TS',
      name: '淺粉紅色',
    },
  });

  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorMediumOrchid.product_attribute_value_id,
      language_code: 'EN',
      name: 'MediumOrchid',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorMediumOrchid.product_attribute_value_id,
      language_code: 'TC',
      name: '中籣花紫',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesColorMediumOrchid.product_attribute_value_id,
      language_code: 'TS',
      name: '中兰花紫',
    },
  });

  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeS.product_attribute_value_id,
      language_code: 'EN',
      name: 'S',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeS.product_attribute_value_id,
      language_code: 'TC',
      name: 'S',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeS.product_attribute_value_id,
      language_code: 'TS',
      name: 'S',
    },
  });

  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeM.product_attribute_value_id,
      language_code: 'EN',
      name: 'M',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeM.product_attribute_value_id,
      language_code: 'TC',
      name: 'M',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeM.product_attribute_value_id,
      language_code: 'TS',
      name: 'M',
    },
  });

  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeL.product_attribute_value_id,
      language_code: 'EN',
      name: 'L',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeL.product_attribute_value_id,
      language_code: 'TC',
      name: 'L',
    },
  });
  await db.product_attribute_translation.create({
    data: {
      product_attribute_value_id: attributeValuesSizeL.product_attribute_value_id,
      language_code: 'TS',
      name: 'L',
    },
  });

  return {
    category: { colorId, sizeId },
    value: {
      color: {
        lightCoral: attributeValuesColorLightCoral.product_attribute_value_id,
        lightPink: attributeValuesColorLightPink.product_attribute_value_id,
        mediumOrchid: attributeValuesColorMediumOrchid.product_attribute_value_id,
      },
      size: {
        sizeS: attributeValuesSizeS.product_attribute_value_id,
        sizeM: attributeValuesSizeM.product_attribute_value_id,
        sizeL: attributeValuesSizeL.product_attribute_value_id,
      },
    },
  };

  console.log('[Prisma] Seed attributes insert progress.');
};
