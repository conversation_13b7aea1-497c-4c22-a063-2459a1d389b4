/*
  Warnings:

  - You are about to drop the `PromoCodeRecords` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `PromoTypes` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Ticket` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TicketConfig` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `event_translations` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `events` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `Item` DROP FOREIGN KEY `Item_promoCodeId_fkey`;

-- DropForeignKey
ALTER TABLE `PromoCodeRecords` DROP FOREIGN KEY `PromoCodeRecords_promoCodeId_fkey`;

-- DropForeignKey
ALTER TABLE `PromoCodeRecords` DROP FOREIGN KEY `PromoCodeRecords_referenceOrderNo_fkey`;

-- DropForeignK<PERSON>
ALTER TABLE `PromoCodeRecords` DROP FOREIGN KEY `PromoCodeRecords_userId_fkey`;

-- DropForeignKey
ALTER TABLE `Ticket` DROP FOREIGN KEY `Ticket_itemId_fkey`;

-- DropForeignKey
ALTER TABLE `Ticket` DROP FOREIGN KEY `Ticket_orderId_fkey`;

-- DropForeignKey
ALTER TABLE `Ticket` DROP FOREIGN KEY `Ticket_productId_fkey`;

-- DropForeignKey
ALTER TABLE `Ticket` DROP FOREIGN KEY `Ticket_userId_fkey`;

-- DropForeignKey
ALTER TABLE `TicketConfig` DROP FOREIGN KEY `TicketConfig_itemId_fkey`;

-- DropForeignKey
ALTER TABLE `TicketConfig` DROP FOREIGN KEY `TicketConfig_productId_fkey`;

-- DropForeignKey
ALTER TABLE `event_translations` DROP FOREIGN KEY `event_translations_event_id_fkey`;

-- DropForeignKey
ALTER TABLE `events` DROP FOREIGN KEY `events_region_fkey`;

-- DropForeignKey
ALTER TABLE `products` DROP FOREIGN KEY `products_event_id_fkey`;

-- DropTable
DROP TABLE `PromoCodeRecords`;

-- DropTable
DROP TABLE `PromoTypes`;

-- DropTable
DROP TABLE `Ticket`;

-- DropTable
DROP TABLE `TicketConfig`;

-- DropTable
DROP TABLE `event_translations`;

-- DropTable
DROP TABLE `events`;
