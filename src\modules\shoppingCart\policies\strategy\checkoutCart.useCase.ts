import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import { CartCheckStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';

import { checkUserCartValidStrategy, checkProductInStock } from '../implementations';

type ParameterType = CartCheckStrategyParameterType;
export const checkoutCartStrategyMap: CartCheckStrategy<ParameterType>[] = [
  checkUserCartValidStrategy,
  checkProductInStock,
];
