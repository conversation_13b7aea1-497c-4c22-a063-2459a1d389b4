import { z } from 'zod';

export const IdDto = z.object({
  id: z.string().transform((id) => parseInt(id)),
});

export const PaginationDto = z.object({
  page: z
    .string()
    .transform((page) => parseInt(page))
    .optional()
    .default('1'),
  size: z
    .string()
    .transform((size) => parseInt(size))
    .optional()
    .default('10'),
});

export const JwtDto = z.object({
  sub: z.number(),
  email: z.string(),
  name: z.string().nullable(),
});

export const GenderDto = z.enum([
  'male',
  'female',
  'transgender_female',
  'transgender_male',
  'non_binary',
  'agender',
  'not_listed',
  'not_to_state',
]);

export const acceptLanguageDto = z.enum(['en', 'zh_cn', 'zh_tw']);
