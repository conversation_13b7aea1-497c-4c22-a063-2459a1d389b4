import { z } from 'zod';
import { languageQuerySchema } from '@/shared/schema/request.schema';

import { PrismaCartItemType, PrismaCartType } from '../enums/shoppingCart.enum';
import { attributeSchema } from '@/shared/schema/productAttribute.schema';

// REGION: getShoppingCart request schema
export const getShoppingCartQuerySchema = languageQuerySchema;
export type GetShoppingCartRequestType = z.infer<typeof getShoppingCartQuerySchema>;

export const getShoppingCartResponseSchema = z.object({
  shoppingCartId: z.number(),
  shoppingCartType: z.enum([
    PrismaCartType.productOnly,
    PrismaCartType.ticketOnly,
    PrismaCartType.hybrid,
  ]),
  region: z.string().length(2).optional(),
  regionName: z.string().optional(),
  currency: z.string().optional(),
  summary: z.object({
    cartTotalQuantity: z.number(),
    cartTotalAmount: z.number(),
  }),
  shoppingCartItems: z.array(
    z.object({
      shippingCartItemId: z.number(),
      shippingCartItemType: z.enum([PrismaCartItemType.product, PrismaCartItemType.ticket]),
      is_selected: z.boolean(),
      productId: z.number(),
      skuId: z.number(),
      productName: z.string(),
      thumbnail: z.string(), // FIXME: z.string().url()
      event: z.object({
        id: z.number(),
        name: z.string(),
      }),
      price: z.object({
        salePrice: z.number(),
        originalPrice: z.number(),
      }),
      skuAttribute: attributeSchema.single,
      quantity: z.number(),
    }),
  ),
  events: z.array(
    z.object({
      eventId: z.number(),
      eventName: z.string(),
    }),
  ),
});

// REGION: getShoppingCartCount request schema
export const getShoppingCartCountResponseSchema = z.object({ count: z.number() });

// REGION: addShoppingCartItem request schema
export const addShoppingCartItemBodySchema = z.object({
  cartItemType: z.enum([PrismaCartItemType.product, PrismaCartItemType.ticket]),
  productId: z.number(),
  skuId: z.number(),
  quantity: z.number(),
});
export type AddShoppingCartItemRequestType = z.infer<typeof addShoppingCartItemBodySchema>;

// REGION: changeShoppingCartItemQuantity
export const changeShoppingCartItemQuantityBodySchema = z.object({
  cartItemType: z.enum([PrismaCartItemType.product, PrismaCartItemType.ticket]),
  productId: z.number(),
  skuId: z.number(),
  quantity: z.number(),
});
export type ChangeShoppingCartItemQuantityRequestType = z.infer<
  typeof addShoppingCartItemBodySchema
>;

// REGION: changeShoppingCartItemQuantity
export const deleteShoppingCartItemBodySchema = z.object({
  cartItemType: z.enum([PrismaCartItemType.product, PrismaCartItemType.ticket]),
  productId: z.number(),
  skuId: z.number(),
});
export type DeleteShoppingCartItemRequestType = z.infer<typeof deleteShoppingCartItemBodySchema>;

// REGION: selectShoppingCartItem
export const selectShoppingCartItemBodySchema = z.object({
  cartItemType: z.enum([PrismaCartItemType.product, PrismaCartItemType.ticket]),
  products: z.array(
    z.object({
      productId: z.number(),
      skuId: z.number(),
      isSelected: z.boolean(),
    }),
  ),
});
export type SelectShoppingCartItemRequestType = z.infer<typeof selectShoppingCartItemBodySchema>;

// REGION: generateShoppingCartCheckoutToken
export const checkoutCartBodySchema = z.object({
  cartType: z.enum([PrismaCartType.hybrid, PrismaCartType.productOnly, PrismaCartType.ticketOnly]),
  cartId: z.number(),
});
export type CheckoutCartBodySchemaRequestType = z.infer<typeof checkoutCartBodySchema>;

// REGION: checkout
export const checkoutCartResponseSchema = z.object({
  orderId: z.number(),
});

// REGION: checkoutOnlyOneShoppingCart
export const checkoutOnlyOneCartBodySchema = z.object({
  cartType: z.enum([PrismaCartType.hybrid, PrismaCartType.productOnly, PrismaCartType.ticketOnly]),
  productId: z.number(),
  skuId: z.number(),
  quantity: z.number(),
});
export type CheckoutOnlyOneCartBodySchemaRequestType = z.infer<
  typeof checkoutOnlyOneCartBodySchema
>;
