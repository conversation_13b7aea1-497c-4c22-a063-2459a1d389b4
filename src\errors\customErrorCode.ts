export type ErrorCodeTableType = { code: string; message: string; details?: any };

export const errorCodeTable = {
  badRequest: { code: 'BAD_REQUEST', message: 'bad request' },
  internalServerError: { code: 'INTERNAL_SERVER_ERROR', message: 'internal server error' },

  unauthorized: { code: 'UNAUTHORIZED', message: 'unauthorized' },
  forbidden: { code: 'FORBIDDEN', message: 'forbidden' },
  notFound: { code: 'NOT_FOUND', message: 'not found' },
  methodNotAllowed: { code: 'METHOD_NOT_ALLOWED', message: 'method not allowed' },
  conflict: { code: 'CONFLICT', message: 'conflict' },

  // REGION: zod error
  requestParameterInvalidate: {
    code: 'REQUEST_PARAMETER_INVALIDATE',
    message: 'Request parameter is invalidate',
  },

  // REGION: aws.s3 error
  fileS3BucketNameIsNotExists: { code: 'FILE_S3_1001', message: 'S3 bucket name is not exists.' },
  fileS3UploadFileIsNotValidate: { code: 'FILE_S3_1002', message: 'Upload file is not validate.' },
  fileS3UploadFail: { code: 'FILE_S3_1003', message: 'Upload file fail.' },

  // REGION: custom error - product
  productSKUInvalidate: { code: 'PRODUCT_2001', message: 'invalid product & sku' },
  addProductInvalidRegion: { code: 'PRODUCT_2002', message: 'add product invalid region' },
  addProductInvalidEvent: { code: 'PRODUCT_2003', message: 'add product invalid event' },
  addProductInvalidAttribute: { code: 'PRODUCT_2004', message: 'add product invalid attribute' },
  addProductInvalidPickupDate: { code: 'PRODUCT_2005', message: 'add product invalid pickup date' },
  addProductInvalidSaleDate: { code: 'PRODUCT_2006', message: 'add product invalid sale date' },
  addProductDuplicateSkuCode: { code: 'PRODUCT_2007', message: 'add product duplicate sku code' },
  addProductPrice: { code: 'PRODUCT_2008', message: 'add product price invalid' },
  updateProductStatusInvalidProductId: {
    code: 'PRODUCT_2009',
    message: 'update product status invalid product id',
  },

  // REGION: custom error - cart
  cartNotExist: { code: 'CART_7001', message: 'shopping cart not exits or expired' },
  cartAddItemProductNoListed: { code: 'CART_7002', message: 'add product not listed' },
  cartAddItemOverflowQty: {
    code: 'CART_7003',
    message: 'add item qty overflow.',
    details: { min: 0, max: 100 },
  },
  cartAddItemSkuNotValidate: { code: 'CART_7004', message: 'sku is invalidate' },
  cartAddItemInvalidRegion: { code: 'CART_7005', message: 'add product is not match cart region' },
  cartAddItemUnderStocking: { code: 'CART_7005', message: 'add product is out of stock' },
  cartItemNotExist: { code: 'CART_7006', message: 'shopping cart item not exits' },
  cartCheckoutCartInvalid: { code: 'CART_7007', message: 'check out cart invalid' },
  cartCheckoutProductOutOfStock: {
    code: 'CART_7008',
    message: 'check out cart product out of stock',
  },
  cartCheckoutNoCartItems: { code: 'CART_7009', message: 'check out cart, no cart item' },

  // REGION: custom error - idempotencyKey
  idempotencyKeyMissing: { code: 'IDEMPOTENCY_KEY_8001', message: 'missing checkout token' },
  idempotencyKeyInvalid: {
    code: 'IDEMPOTENCY_KEY_8002',
    message: 'invalid or expire checkout token',
  },
  idempotencyKeyIsUsed: { code: 'IDEMPOTENCY_KEY_8003', message: 'checkout token already used' },
  idempotencyKeyNotExists: {
    code: 'IDEMPOTENCY_KEY_8004',
    message: 'idempotency token is not exists',
  },
  
  // REGION: Order
  getOrderOrderIdNotExists: { code: 'ORDER_1001', message: 'order id not exists.' },
  updateOrderShippingOrderIdNotExists: { code: 'ORDER_1002', message: 'order id not exists.' },
  updateOrderShippingOrderIsComplete: { code: 'ORDER_1003', message: 'order is complete.' },
  updateOrderShippingShippingDelivered: { code: 'ORDER_1004', message: 'shipping delivered.' },

  // REGION: LabPay
  sendLabPayPreCreateOrderNoExists: {
    code: 'LABPAY_4001',
    message: 'lab pay pre-create order no exists',
  },
  sendLabPayPreCreateOrderInvalidEnv: {
    code: 'LABPAY_4002',
    message: 'lab pay pre-create order invalid env',
  },
  sendLabPayPreCreteOrderResponseError: {
    code: 'LABPAY_4003',
    message: 'lab pay pre-create order response error',
  },
  notificationLabPayInvalidBody: {
    code: 'LABPAY_4101',
    message: 'lab pay notification invalid body',
  },
  notificationLabPaySignatureError: {
    code: 'LABPAY_4102',
    message: 'lab pay signature error',
  },
};
