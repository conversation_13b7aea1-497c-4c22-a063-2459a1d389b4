import { $Enums } from '@prisma/client';

export const PrismaProductStatus = $Enums.product_status;
export const PrismaProductMediaType = $Enums.product_media_media_type;
export const PrismaProductInventoryHoldStatus = $Enums.hold_status;

export type ProductStatusType =
  | typeof PrismaProductStatus.draft
  | typeof PrismaProductStatus.listed
  | typeof PrismaProductStatus.unListed;

export type ProductInventoryHoldStats =
  | typeof PrismaProductInventoryHoldStatus.hold
  | typeof PrismaProductInventoryHoldStatus.deducted
  | typeof PrismaProductInventoryHoldStatus.released;

export type ProductMediaType =
  | typeof PrismaProductMediaType.image
  | typeof PrismaProductMediaType.video;
