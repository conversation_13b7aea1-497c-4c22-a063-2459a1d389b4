import AWS from 'aws-sdk';
import { logger } from './logger';

const {
  S3_BUCKET_NAME,
  S3_PUBLIC,
  S3_BUCKET_PRIVATE
} = process.env;

const s3 = new AWS.S3();

export const EVENT_MEDIA = 'event_media'

export enum S3_KEY {
  TICKET = 'ticket',
  EVENT = 'event'
}

async function moveS3Object(bucket: string, srcKey: string, destKey: string) {
  // Copy the object to the new location
  // it should throw an error if fails
  await copyS3Object(bucket, srcKey, destKey);

  // failure to delete original object should not block the room creation
  // we can clean /tmp folder using a cron job
  try {
    await s3
      .deleteObject({
        Bucket: bucket,
        Key: srcKey,
      })
      .promise();
  } catch (error) {
    logger.error('Error delete S3 object:', error);
  }
}

async function copyS3Object(bucket: string, srcKey: string, destKey: string) {
  await s3
    .copyObject({
      Bucket: bucket,
      CopySource: `${bucket}/${srcKey}`,
      Key: destKey,
    })
    .promise();
}

export function parseS3Url(url: string) {
  // Remove the protocol part and split the URL
  const urlParts = url.replace('https://', '').split('/');
  const bucket = urlParts[1];
  const key = urlParts.slice(2).join('/');

  return { bucket, key };
}

function parseAndReplaceS3Url(url: string, objectId: number | string) {
  const { bucket, key } = parseS3Url(url);

  const newKey = key.replace('tmp', String(objectId));
  const newUrl = url.replace('tmp', String(objectId));

  return { bucket, key, newKey, newUrl };
}

export function deleteObjectByUrl(url: string) {
  const { bucket, key } = parseS3Url(url);

  return s3
    .deleteObject({
      Bucket: bucket,
      Key: key,
    })
    .promise();
}

export async function moveTmpObject(s3Url: string, objectId: number | string) {
  const { bucket, key, newKey, newUrl } = parseAndReplaceS3Url(s3Url, objectId);
  await moveS3Object(bucket, key, newKey);

  return newUrl;
}

export async function copyTmpObject(s3Url: string, objectId: number | string) {
  const { bucket, key, newKey, newUrl } = parseAndReplaceS3Url(s3Url, objectId);
  await copyS3Object(bucket, key, newKey);

  return newUrl;
}

export async function deleteTmpObjects(s3Urls: string[]) {
  const promises = s3Urls
    .filter((s3Url) => s3Url?.includes('/tmp/'))
    .map((s3Url) => deleteObjectByUrl(s3Url));
  try {
    await Promise.all(promises);
  } catch (error) {
    logger.error(error);
  }
}

export const uploadS3File = async (
  key: string,
  fileName: string,
  body: Buffer | string,
  size: number,
  isPrivate?: boolean
) => {
  const s3Key = `${key}/${fileName}`
  const uploadParams = {
    Bucket: isPrivate? S3_BUCKET_PRIVATE : S3_BUCKET_NAME,
    Key: s3Key,
    Body: body,
    ContentLength: size,
  } as any;

  await s3.putObject(uploadParams).promise();

  if (isPrivate) {
    return s3Key
  }
  return (
    `${S3_PUBLIC}/${key}/${encodeURIComponent(fileName)}`
  );
};

export const removeS3File = async (file: string) => {
  const deleteParams = {
    Bucket: S3_BUCKET_NAME,
    Key: file,
  } as any;
  await s3.deleteObject(deleteParams).promise();
};

export const copyObjectFromExternalS3 = async (src: string, key: string) => {
  await s3
    .copyObject({
      Bucket: S3_BUCKET_NAME as string,
      CopySource: src,
      Key: key,
    })
    .promise();

  return `https://s3.ap-southeast-1.amazonaws.com/portal-hkgt.fun-verse.io/${key}`;
};
