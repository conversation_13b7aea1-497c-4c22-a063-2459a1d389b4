import {
  ProductCheckStrategyParameterType,
  UpdateProductStatusCheckParameter,
} from '@modules/product/schema/strategy.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkProductExistStrategy: Strategy = async (product: ParameterType) => {
  const { id } = product as UpdateProductStatusCheckParameter;

  const count = await db.products.count({ where: { product_id: id } });
  if (count <= 0) {
    throw new CustomErrorException(errorCodeTable.updateProductStatusInvalidProductId);
  }
};
