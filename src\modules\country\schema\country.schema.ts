import { z } from 'zod';
import { languageQuerySchema } from '@/shared/schema/request.schema';

// REGION: getAllCountry request schema
export const getAllCountryQuerySchema = languageQuerySchema;
export type GetAllCountryRequestType = z.infer<typeof getAllCountryQuerySchema>;
export const getAllCountryResponseSchema = z.record(
  z.string(),
  z.object({
    countryCode: z.string(),
    name: z.string(),
  }),
);

// REGION: getCountryByCode request schema
export const getCountryByCodeParamSchema = z.object({ code: z.string() });
export const getCountryByCodeQuerySchema = languageQuerySchema;
export type GetCountryByCodeRequestType = {
  params: z.infer<typeof getCountryByCodeParamSchema>;
  query: z.infer<typeof getCountryByCodeQuerySchema>;
};
export const getCountryByCodeResponseSchema = z.object({
  countryCode: z.string(),
  name: z.string(),
});
