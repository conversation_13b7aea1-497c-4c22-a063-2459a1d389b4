import dayjs, { ManipulateType } from 'dayjs';
import { getTimezonesForCountry, Country, getCountry, Timezone } from 'countries-and-timezones';

/**
 * Generate now unix timestamp. using second
 * @returns current unix timestamp
 */
export const generateTimeNow: () => number = () => {
  return dayjs().tz('UTC').unix();
};

/**
 * Get after input day unix
 * @param day add day
 * @returns unix timestamp
 */
export const getUnixAfterDays: (day: number) => number = (day) => {
  return dayjs().tz('UTC').add(day, 'day').set('hour', 0).set('minute', 0).set('second', 0).unix();
};

/**
 * Get expire unix timestamp
 * @param value
 * @param mode
 * @returns
 */
export const generateExpireAt: (value: number, mode: ManipulateType) => number = (value, mode) => {
  return dayjs().tz('UTC').add(value, mode).unix();
};

export const generateTimeNowWithFormat: (format: string) => string = (format) => {
  const tzHK = convertCountryToTimezone();
  return dayjs().tz(tzHK[0].name).format(format);
};

/**
 * Convert region/country(string) to timezone
 * Default: HK, or arg[1]
 *
 * @param region The country ISO 3166-1 code. default HK
 */
export const convertCountryToTimezone: (region?: string) => Timezone[] = (region = 'HK') => {
  const country: Country = getCountry(region) ?? getCountry('HK');
  const productTimeZone = getTimezonesForCountry(country?.id);

  return productTimeZone;
};

/**
 * Test today within date range.
 * @param startDate unix time (UTC)
 * @param endDate unix time (UTC)
 * @param timezone timezone (by countries-and-timezones)
 * @returns
 */
export const todayWithinDateRange: (
  timezone: Timezone,
  startDate: number,
  endDate: number,
) => boolean = (timezone, startDate, endDate) => {
  return withinDateRange(generateTimeNow(), startDate, endDate, timezone);
};

/**
 * Test input date within date range.
 * @param inputDate unix time (UTC)
 * @param startDate unix time (UTC)
 * @param endDate unix time (UTC)
 * @param timezone timezone (by countries-and-timezones)
 * @returns
 */
export const withinDateRange: (
  inputDate: number,
  startDate: number,
  endDate: number,
  timezone: Timezone,
) => boolean = (inputDate, startDate, endDate, timezone) => {
  const d = dayjs.unix(inputDate).tz(timezone.name);
  const sd = dayjs.unix(startDate).tz(timezone.name);
  const ed = dayjs.unix(endDate).tz(timezone.name);

  return d.isBetween(sd, ed);
};

/**
 * Random n-digital string, include Upper/lower/number
 * @returns Random n-digital string.
 */
export const generateRandomString: (n: number) => string = (n) => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return [...Array(3).keys()].map(() => charset[Math.floor(Math.random() * 62)]).join('');
};

export const parseDateWithFormat = (date: string, format: string = 'YYYYMMDDHHmmss') => {
  const parsedDate = dayjs(date, format);
  return parsedDate.unix();
};

export const languageCodeSorter = (a: any, b: any) =>
  [a.language_code, b.language_code].includes('EN') ? -1 : a.localeCompare(b);
