import axios from 'axios';
import { json2xml, xml2json } from 'xml-js';
import db from '@prismaClient';

import { CustomErrorException, errorCodeTable } from '@/errors';
import { Language, LanguageCode } from '@/shared/enums';
import { OrderPaymentBodySchemaType } from '@modules/order/schema/order.schema';

import {
  convertToLocale,
  generateLabPayPreCreateOrderBody,
  generateQueryLabPayOrder,
  verifyNotificationSignature,
} from './utils/payment.util';
import { LabPayCreatePreOrderBodyType, LabPayNotificationBodyType } from './schema/payment.schema';
import { logger } from '@/utils/logger';

export const sendLabPayPreCreateOrder = async (
  order: OrderPaymentBodySchemaType,
  language: Language = LanguageCode.EN,
): Promise<string> => {
  // find order info by order id
  const findOrder = await db.orders.findUnique({
    select: { total_amount: true, order_no: true, currency: true },
    where: { order_id: order.orderId },
  });

  if (!findOrder) {
    throw new CustomErrorException(errorCodeTable.sendLabPayPreCreateOrderNoExists);
  }

  // Transform total amount to preCreateOrder's total fee
  const { total_amount: totalAmount, order_no: orderNo } = findOrder;
  const totalFee = totalAmount.mul(100).toNumber();

  const preCreateOrderBody = generateLabPayPreCreateOrderBody(order.paymentPlatform, {
    orderId: order.orderId,
    orderNo,
    locale: convertToLocale(language),
    paymentMethod: order.paymentMethod,
    paymentPlatform: order.paymentPlatform,
    paymentType: order.paymentType,
    feeType: findOrder.currency,
    totalFee,
  });

  return await postPreCreateOrderToLabPay(preCreateOrderBody);
};

export const postPreCreateOrderToLabPay = async (orderObject: LabPayCreatePreOrderBodyType) => {
  const labPayUrl = process.env.LAB_PAY_API_URL ?? '';

  // create a pre order to Lab Pay
  const requestBody = `<xml>${json2xml(JSON.stringify(orderObject), { compact: true, spaces: 0 })}</xml>`;
  logger.info(`[LabPay preCreateOrder XML request body] >> ${requestBody}`);
  const result = await axios.post(`${labPayUrl}/precreateorder`, requestBody, {
    headers: { 'Content-Type': 'application/xml' },
  });
  logger.info(`[LabPay preCreateOrder XML result] >> ${result.data}`);

  // convert xml response to json
  const resultXMLToJson = xml2json(result.data, { compact: true, spaces: 4 });
  const { xml: response } = JSON.parse(resultXMLToJson);

  // get response code and url from xml response
  const responseCode = response?.['response_code']?.['_text'];
  if (responseCode !== '0000') {
    throw new CustomErrorException(errorCodeTable.sendLabPayPreCreteOrderResponseError);
  }
  if (['data', 'url'].some((field) => Object.keys(response).includes(field))) {
    return response?.['data']?.['_cdata'] || response?.['url']?.['_cdata'];
  }
  throw new CustomErrorException(
    errorCodeTable.sendLabPayPreCreteOrderResponseError,
    `'data', 'url' are not exists in pre-create order response`,
  );
};

export const postQueryOrderToLabPay = async (orderId: number) => {
  const labPayUrl = process.env.LAB_PAY_API_URL ?? '';

  const findOrder = await db.orders.findUnique({
    select: { order_no: true, payment_platform: true },
    where: { order_id: orderId },
  });

  if (!findOrder?.order_no || !findOrder?.payment_platform) {
    throw new CustomErrorException(errorCodeTable.sendLabPayPreCreateOrderNoExists);
  }

  const orderObject = generateQueryLabPayOrder(
    findOrder.order_no,
    findOrder.payment_platform ?? '',
  );

  const postXMLBody = `<xml>${json2xml(JSON.stringify(orderObject), { compact: true, spaces: 0 })}</xml>`;
  logger.info(`[LabPay queryOrder XML request body] >> ${postXMLBody}`);
  const result = await axios.post(`${labPayUrl}/queryorder`, postXMLBody, {
    headers: { 'Content-Type': 'application/xml' },
  });
  logger.info(`[LabPay queryOrder XML result] >> ${JSON.stringify(result.data)}`);
  return result.data;
};

export const verifyLabPayPayment = (sign: string, order: LabPayNotificationBodyType) => {
  return verifyNotificationSignature(sign, order);
};
