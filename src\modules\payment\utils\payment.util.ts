import { randomBytes } from 'node:crypto';
import { MD5, SHA256 } from 'crypto-js';
import { logger } from '@utils/logger';

import { PaymentPlatform, PaymentType } from '../Enum/payment.enum';
import {
  AliPayPreCheckoutType,
  CardPreCheckout,
  LabPayCreatePreOrderBodyType,
  LabPayNotificationBodyType,
  PreCreateOrderBody,
  WechatPayPreCheckoutType,
} from '../schema/payment.schema';
import { Language, LanguageCode } from '@/shared/enums';
import { CustomErrorException, errorCodeTable } from '@/errors';

type GeneratePreCreateOrderBody = (
  order: PreCreateOrderBody,
  withBilling?: boolean,
) => LabPayCreatePreOrderBodyType;

const {
  ENCRYPT_METHOD,
  AUTH_CODE = '151234567890123456',
  LAB_PAY_MERCHANT_APP_ID,
  LAB_PAY_MERCHANT_ID,
  LAB_PAY_KEY,
  NOTIFY_URL,
  RETURN_URL,
} = process.env;

type PAYMENT_PLATFORM_TYPE = 'WeChatPay' | 'AliPay' | 'Airwallex';

export const PAYMENT_PLATFORM: { [x: string]: number } = {
  WeChatPay: 1,
  AliPay: 2,
  Airwallex: 3,
};

export const PAYMENT_TYPE: { [x: string]: number } = {
  QRCODE: 1,
  MOBILEWEB: 2,
  WEB: 3,
  REDIRECT: 4,
};

export const CURRENCY: { [x: string]: number } = {
  HKD: 1,
  USD: 2,
};

export const TXN_STATE: { [x: string]: number } = {
  SUCCESS: 1,
};

export const convertToLocale = (language: Language) => {
  if (language === LanguageCode.EN) return 'en';
  if (language === LanguageCode.TC) return 'zh_tw';
  if (language === LanguageCode.TS) return 'zh_cn';
  return language.toLowerCase();
};
export const convertToFrontEndLocale = (language: Language) => {
  if (language === LanguageCode.EN) return 'en';
  if (language === LanguageCode.TC) return 'zh';
  if (language === LanguageCode.TS) return 'zh';
  return language.toLowerCase();
};

export const generatePaymentType = (platform: PaymentPlatform): PaymentType => {
  if (platform === PaymentPlatform.weChatPay || platform === PaymentPlatform.alipay)
    return PaymentType.qrCode;
  if (platform === PaymentPlatform.airwallex) return PaymentType.redirect;

  return PaymentType.redirect;
};

export const generateNonce = (length: number = 32) => {
  return randomBytes(length).toString('base64');
};

export const generateOrderNo = (userId: number, prefix: string = 'BLK') => {
  return `${prefix}${userId}${Math.round(new Date().valueOf() / 1000)}`;
};

export const generateDeliveryNote = (userId: number, prefix: string = 'BLK-DN') => {
  const roundedTimestamp = Math.round(new Date().valueOf() / 1000);
  return `${prefix}-${userId}-${roundedTimestamp}`;
};

// LAB PAY
export const generateLabPayPreCreateOrderBody = (
  type: PaymentPlatform,
  order: PreCreateOrderBody,
): LabPayCreatePreOrderBodyType => {
  return {
    [PaymentPlatform.weChatPay]: generateWechatPayPreCheckout(order),
    [PaymentPlatform.alipay]: generateAliPayPreCheckout(order),
    [PaymentPlatform.airwallex]: generateCardPreCheckout(order),
  }[type];
};

const generateWechatPayPreCheckout: GeneratePreCreateOrderBody = (order: any) => {
  if (!LAB_PAY_MERCHANT_APP_ID || !LAB_PAY_MERCHANT_ID || !order.paymentType) {
    throw new CustomErrorException(errorCodeTable.sendLabPayPreCreateOrderInvalidEnv);
  }

  const BODY = order.body || 'client-order';
  const nonce = generateNonce(24);
  const notifyUrl = `${NOTIFY_URL}/api/public/v1/payment/notification`;

  const preOrderObj = {
    body: BODY,
    expiry_period: 5,
    fee_type: order.feeType,
    lab_merchant_app_id: LAB_PAY_MERCHANT_APP_ID,
    lab_merchant_id: LAB_PAY_MERCHANT_ID,
    merchant_order_no: order.orderNo,
    nonce_str: nonce,
    notify_url: notifyUrl,
    payment_platform: order.paymentPlatform,
    payment_platform_wallet_region: 'HK',
    payment_type: order.paymentType,
    total_fee: order.totalFee,
  };
  const sign = createRequestQuerySignature(preOrderObj);
  return { ...preOrderObj, sign } as WechatPayPreCheckoutType;
};

const generateAliPayPreCheckout: GeneratePreCreateOrderBody = (order) => {
  if (!LAB_PAY_MERCHANT_APP_ID || !LAB_PAY_MERCHANT_ID || !order.paymentType) {
    throw new CustomErrorException(errorCodeTable.sendLabPayPreCreateOrderInvalidEnv);
  }

  const BODY = order.body || 'client-order';
  const nonce = generateNonce(24);
  const notifyUrl = `${NOTIFY_URL}/api/public/v1/payment/notification`;

  const preOrderObj = {
    body: BODY,
    expiry_period: 5,
    fee_type: order.feeType,
    lab_merchant_app_id: LAB_PAY_MERCHANT_APP_ID,
    lab_merchant_id: LAB_PAY_MERCHANT_ID,
    merchant_order_no: order.orderNo,
    nonce_str: nonce,
    notify_url: notifyUrl,
    payment_platform: order.paymentPlatform,
    payment_platform_wallet_region: 'HK',
    payment_type: order.paymentType,
    total_fee: order.totalFee,
  };
  const sign = createRequestQuerySignature(preOrderObj);
  return { ...preOrderObj, sign } as AliPayPreCheckoutType;
};

const generateCardPreCheckout: GeneratePreCreateOrderBody = (order, withBilling = false) => {
  if (!LAB_PAY_MERCHANT_APP_ID || !LAB_PAY_MERCHANT_ID || !order.paymentType) {
    throw new CustomErrorException(errorCodeTable.sendLabPayPreCreateOrderInvalidEnv);
  }

  const BODY = order.body || 'client-order';
  const nonce = generateNonce(24);
  const notifyUrl = `${NOTIFY_URL}/api/public/v1/payment/notification`;
  const returnUrl = `${RETURN_URL}/order/${order.orderId}/callback`;

  const preOrderObj = {
    body: BODY,
    fee_type: order.feeType,
    is_with_billing: withBilling,
    lab_merchant_app_id: LAB_PAY_MERCHANT_APP_ID,
    lab_merchant_id: LAB_PAY_MERCHANT_ID,
    locale: order.locale,
    merchant_order_no: order.orderNo,
    nonce_str: nonce,
    notify_url: notifyUrl,
    payment_platform: PaymentPlatform.airwallex,
    payment_type: order.paymentType,
    return_url: returnUrl,
    total_fee: order.totalFee,
  };
  const sign = createRequestQuerySignature(preOrderObj);
  return {
    ...preOrderObj,
    sign,
    payment_channels: [{ payment_channel: order.paymentMethod }],
  } as CardPreCheckout;
};

export const verifyLabPayPayment = (sign: string, order: any) => {
  const orderStr = [
    ...Object.keys(order)
      .sort()
      .map((key) => {
        if (!order[key]) return undefined;
        return `${key}=${order[key]}`;
      }),
    `key=${LAB_PAY_KEY}`,
  ]
    .filter((item) => item)
    .join('&');
  const expectedStr = MD5(orderStr).toString().toUpperCase();
  if (sign !== expectedStr) {
    logger.info(
      `[lab pay signature error] >> received: ${sign} >> generated: ${expectedStr} >> orderSt: ${orderStr}`,
    );
    throw 4303;
  }
  return true;
};

export const generateQueryLabPayOrder = (orderNo: string, paymentPlatform: string) => {
  const nonce = generateNonce(24);
  const queryObj = {
    lab_merchant_app_id: LAB_PAY_MERCHANT_APP_ID,
    lab_merchant_id: LAB_PAY_MERCHANT_ID,
    nonce_str: nonce,
    merchant_order_no: orderNo,
    payment_platform: paymentPlatform,
  };
  const sign = createRequestQuerySignature(queryObj);
  return { ...queryObj, sign };
};

const convertToKeyPair = <T extends object>(data: T): string => {
  const dataObject = Object.entries(data).map(([key, value]) => ({ key, value }));
  const sortedData = dataObject
    .filter(({ key, value }) => !!key || !!value)
    .sort((a, b) => a.key.localeCompare(b.key));
  sortedData.push({ key: 'key', value: LAB_PAY_KEY ?? '' });
  return sortedData.map(({ key, value }) => `${key}=${value}`).join('&');
};

export const createRequestQuerySignature = (
  data: Record<string, string | boolean | number | null | undefined>,
) => {
  const keyPair = convertToKeyPair(data);
  return MD5(keyPair).toString().toUpperCase();
};

export const verifyNotificationSignature = (sign: string, order: LabPayNotificationBodyType) => {
  const expectedStr = createRequestQuerySignature(order);

  // FIXME: ByPass
  logger.info(
    `[LabPay verifyNotificationSignature] >> received: ${sign} >> generated: ${expectedStr}`,
    JSON.stringify(order),
  );
  // if (sign !== expectedStr) {
  //   logger.info(
  //     `[Signature error from LabPay] >> received: ${sign} >> generated: ${expectedStr}`,
  //     JSON.stringify(order),
  //   );
  //   throw new CustomErrorException(errorCodeTable.notificationLabPaySignatureError);
  // }
  return true;
};
