import { CreateProductRequestType } from './product.schema';
import { ProductStatusType } from '../enums/product.enum';

export type ProductCheckStrategyParameterType =
  | CreateProductRequestType
  | UpdateProductStatusCheckParameter
  | BatchUpdatePProductStatusCheckParameter;

export type UpdateProductStatusCheckParameter = {
  id: number;
  status: ProductStatusType;
};

export type BatchUpdatePProductStatusCheckParameter = UpdateProductStatusCheckParameter[];
