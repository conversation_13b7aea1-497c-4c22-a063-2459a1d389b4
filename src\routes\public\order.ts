import { Router } from 'express';
import * as orderController from '@modules/order/order.controller';
import { tokenAuthenticate } from '@/middleware';

const router = Router();

router.get('/:id', tokenAuthenticate, orderController.getOrderById);
router.post('/payment', tokenAuthenticate, orderController.orderPayment);
router.post('/payment/idempotency-key', tokenAuthenticate, orderController.getOrderPaymentIdempotencyKey)
router.post('/shipping', tokenAuthenticate, orderController.updateOrderShipping);

export default router;
