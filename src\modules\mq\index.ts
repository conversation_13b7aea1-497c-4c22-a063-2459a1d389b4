import { Queue } from 'bullmq';
import { TicketMQ } from './ticket';
import { OrderMQ } from './order';
import { redis } from './redis';
import { NotificationMQ } from './notification';
import { NOTIFICATION_QUEUE_TOPIC } from './constant';

let ServiceQueue: any;

class MQ {
    public ticketQueue: any;
    public orderQueue: any;
    public notificationQueue: any;

    constructor() {
        this.initQueue()
        this.initWorker()
        this.testQueue()
    }

    testQueue() {
        this.ticketQueue.add('ticket', { status: 'ticket queue ready' })
        this.orderQueue.add('order', { status: 'order queue ready' })
        this.notificationQueue.add('notification', { status: 'notification queue ready' })
    }

    initQueue() {
        this.ticketQueue = new Queue('ticket', { connection: redis })
        this.orderQueue = new Queue('order', { connection: redis })
        this.notificationQueue = new Queue('notification', { connection: redis })
    }

    initWorker() {
        new OrderMQ()
        new TicketMQ()
        new NotificationMQ()
    }
}

const InitMQ = () => {
    ServiceQueue = new MQ()
}

export {
    ServiceQueue,
    InitMQ
}