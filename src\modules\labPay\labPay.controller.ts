import { Request, Response } from 'express';
import { handleAsync } from '@middleware';
import { logger } from '@utils/logger';
import { errorHandler } from '@errors';
// import labPayService from '@modules/labPay/labPay.service';

export const payment = handleAsync(async (req: Request, res: Response) => {
  try {
    // const { xml } = req.body;
    // const orderInfo: { [x: string]: any } = {};
    // Object.keys(xml).forEach((key) => {
    //   orderInfo[key] = xml[key][0];
    // });
    // logger.info(`[received webhook from PAY Lab] >> ${JSON.stringify(orderInfo)}`);
    // const { sign, ...rest } = orderInfo;
    // await labPayService.verify(sign, rest);
    // let paymentMethod = 'unknown';
    // if (orderInfo.trade_info) {
    //   try {
    //     paymentMethod = JSON.parse(orderInfo.trade_info).payment_card_name;
    //   } catch (error) {
    //     errorHandler(4305, 'LabPay /payment');
    //   }
    // }
    // await labPayService.updateOrder(
    //   orderInfo.merchant_order_no,
    //   orderInfo.payment_platform,
    //   paymentMethod,
    // );
    return res.status(200).send();
  } catch (error) {
    // errorHandler(error, 'LabPay /payment');
    return res.status(500).send();
  }
});

// labPayRouter.post('/payment', handleAsync(async (req: Request, res: Response) => {
//     try {
//         const {xml} = req.body
//         const orderInfo: { [x: string]: any } = {}
//         Object.keys(xml).forEach((key) => {
//             orderInfo[key] = xml[key][0]
//         })
//         logger.info(`[received webhook from PAY Lab] >> ${JSON.stringify(orderInfo)}`)
//         const {
//             sign,
//             ...rest
//         } = orderInfo
//         await labPayService.verify(sign, rest)
//         let paymentMethod = 'unknown'
//         if (orderInfo.trade_info) {
//             try {
//                 paymentMethod = JSON.parse(orderInfo.trade_info).payment_card_name
//             } catch(error) {
//                 errorHandler(4305, 'LabPay /payment')
//             }
//         }
//         await labPayService.updateOrder(
//             orderInfo.merchant_order_no,
//             orderInfo.payment_platform,
//             paymentMethod
//         )
//         return res.status(200).send();
//     } catch(error) {
//         errorHandler(error, 'LabPay /payment')
//         return res.status(500).send()
//     }
// }));

// export default labPayRouter;
