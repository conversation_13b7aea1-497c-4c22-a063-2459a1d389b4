datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "linux-musl-openssl-3.0.x", "linux-musl"]
  previewFeatures = ["relationJoins", "prismaSchemaFolder"]
}

model Item {
  id           Int           @id @default(autoincrement())
  promoCodeId  Int
  code         String
  title        String
  description  String
  visible      Boolean
  quantity     Int
  createdAt    Int
  updatedAt    Int
  UserItem     UserItem[]

  @@index([promoCodeId], map: "Item_promoCodeId_fkey")
}

model Transaction {
  id          Int     @id @default(autoincrement())
  refNo       Int
  orderNo     String
  url         String
  urlType     String
  status      Int
  orderId     Int
  total       Decimal @db.Decimal(9, 2)
  currency    Int
  createdDate Int
  updatedDate Int?
}

model User {
  id                   Int                @id @default(autoincrement())
  email                String             @unique
  password             String
  name                 String?
  verificationCode     Int?
  resetPasswordToken   String?
  resetPasswordExpires DateTime?
  isActive             Boolean            @default(false)
  gender               User_gender?
  phoneNumber          String?
  dateOfBirth          DateTime?
  countryCode          String?            @db.VarChar(2)
  address              String?
  photoUrl             String?
  receivePromotions    Boolean            @default(false)
  identity             String             @default("personal")
  role                 User_role
  supportGoogle        Boolean?
  supportFacebook      Boolean?
  user_ref_id          String             @db.VarChar(36) @unique
  token                String             @db.Text
  preference_language  String?            @db.VarChar(5) @default("en")
  shopping_cart        shopping_cart?
  Address              Address[]
  UserItem             UserItem[]
  orders               orders[]
  ticket               ticket[] @relation("user_ticket")

  @@index([email], map: "email_idx")
  @@index([name], map: "name_idx")
  @@index([user_ref_id], map: "user_ref_id_idx")
  @@index([token(length: 500)], map: "User_token_IDX")

}

model UserItem {
  id        Int     @id @default(autoincrement())
  userId    Int
  itemId    Int
  title     String
  quantity  Int
  visible   Boolean
  createdAt Int
  updatedAt Int
  Item      Item    @relation(fields: [itemId], references: [id])
  User      User    @relation(fields: [userId], references: [id])

  @@index([itemId], map: "UserItem_itemId_fkey")
  @@index([userId], map: "UserItem_userId_fkey")
}

model Address {
  id                Int     @id @default(autoincrement())
  userId            Int
  addressType       Int     @default(1)
  recipientName     String?
  recipientphone    String?
  countryCode       String?
  country           String?
  province          String?
  city              String?
  district          String?
  street            String?
  addressLine1      String?
  addressLine2      String?
  email             String?
  postalCode        String?
  isDefaultBilling  Boolean
  isDefaultDelivery Boolean
  createdAt         Int
  updatedAt         Int
  user              User    @relation(fields: [userId], references: [id])

  @@index([userId], map: "Address_userId_fkey")
}


/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model product_inventory {
  inventory_id       Int         @id @default(autoincrement())
  product_id         Int
  sku_id             Int         @unique
  total_quantity     Int         @default(0)
  available_quantity Int         @default(0)
  reserved_quantity  Int         @default(0)
  returned_quantity  Int         @default(0)
  version            Int         @default(0)
  created_at         Int
  updated_at         Int
  products           products    @relation(fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction, map: "product_inventory_ibfk_1")
  product_sku        product_sku @relation(fields: [sku_id], references: [sku_id], onDelete: NoAction, onUpdate: NoAction, map: "product_inventory_ibfk_2")

  @@unique([product_id, sku_id, version], map: "uk_product_sku_version")
  @@index([product_id], map: "idx_product_id")
  @@index([sku_id], map: "idx_sku_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model product_inventory_hold {
  product_inventory_hold_id Int         @id @default(autoincrement())
  order_id                  Int
  product_id                Int
  sku_id                    Int
  hold_quantity             Int
  hold_status               hold_status
  created_at                Int
  updated_at                Int
  orders                    orders      @relation(fields: [order_id], references: [order_id], onDelete: NoAction, onUpdate: NoAction, map: "product_inventory_hold_ibfk_1")
  products                  products    @relation(fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction, map: "product_inventory_hold_ibfk_2")
  product_sku               product_sku @relation(fields: [sku_id], references: [sku_id], onDelete: NoAction, onUpdate: NoAction, map: "product_inventory_hold_ibfk_3")

  @@index([order_id, product_id, sku_id], map: "idx_product_sku")
  @@index([product_id], map: "product_id")
  @@index([sku_id], map: "sku_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model product_media {
  product_media_id Int                      @id @default(autoincrement())
  product_id       Int
  sku_id           Int
  media_type       product_media_media_type
  is_main          Boolean?                 @default(false)
  url              String?                  @db.VarChar(255)
  priority         Int?                     @default(1)
  is_active        Boolean?                 @default(true)
  created_at       Int
  updated_at       Int
  products         products                 @relation(fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction, map: "product_media_ibfk_1")
  product_sku      product_sku              @relation(fields: [sku_id], references: [sku_id], onDelete: NoAction, onUpdate: NoAction, map: "product_media_ibfk_2")

  @@index([product_id], map: "idx_product_id")
  @@index([sku_id], map: "idx_sku_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model product_sku {
  sku_id                 Int                      @id @default(autoincrement())
  product_id             Int
  is_default             Boolean                  
  sku_code               String                   @unique(map: "uk_sku_code") @db.VarChar(100)
  original_price         Decimal                  @db.Decimal(12, 2)
  sale_price             Decimal                  @db.Decimal(12, 2)
  cost_price             Decimal?                 @db.Decimal(12, 2)
  tax                    Decimal?                 @db.Decimal(12, 2)
  weight                 Decimal?                 @db.Decimal(10, 2)
  volume                 Decimal?                 @db.Decimal(10, 2)
  length                 Decimal?                 @db.Decimal(10, 2)
  width                  Decimal?                 @db.Decimal(10, 2)
  height                 Decimal?                 @db.Decimal(10, 2)
  created_at             Int
  updated_at             Int
  order_items            order_items[]
  product_inventory      product_inventory?       
  product_inventory_hold product_inventory_hold[]
  product_media          product_media[]
  products               products                 @relation("AllSKU", fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction, map: "product_sku_ibfk_1")
  defaultSKUProduct      products[]               @relation("DefaultSKU")
  shopping_cart_item     shopping_cart_item[]
  product_sku_attributes product_sku_attributes[]

  @@index([product_id], map: "idx_product_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model product_translations {
  product_translation_id Int                                 @id @default(autoincrement())
  product_id             Int
  language_code          String                              @db.VarChar(5)
  name                   String                              @db.VarChar(255)
  description            String?                             @db.VarChar(500)
  introduction           String?                             @db.LongText
  tag                    Json?
  pickup_venue           String?                             @db.Text
  created_at             Int
  updated_at             Int
  products               products                            @relation(fields: [product_id], references: [product_id])

  @@unique([product_id, language_code], map: "uk_product_language")
  @@index([language_code], map: "idx_language_code")
  @@index([product_id], map: "idx_product_id")
}

model product_attributes {
  product_attribute_id              Int         @id @default(autoincrement())
  product_id                        Int
  product_attribute_category_id     Int
  product_attribute_value_id        Int 
  products                          products    @relation(fields: [product_id], references: [product_id])
  product_attribute_categories      product_attribute_categories  @relation(fields: [product_attribute_category_id], references: [product_attribute_category_id])
  product_attribute_values          product_attribute_values      @relation(fields: [product_attribute_value_id], references: [product_attribute_value_id])

  product_sku_attributes       product_sku_attributes[]

  @@index([product_id], map: "idx_product_id")
}

model product_sku_attributes {
  product_sku_attribute_id       Int                 @id @default(autoincrement())
  sku_id                         Int
  product_attribute_id           Int
  product_skus                   product_sku         @relation(fields: [sku_id], references: [sku_id])
  product_attributes             product_attributes  @relation(fields: [product_attribute_id], references: [product_attribute_id])
  
  @@index([sku_id], map: "idx_sku_id")
  @@index([product_attribute_id], map: "idx_product_attribute_id")
}

model product_attribute_values {
  product_attribute_value_id           Int     @id @default(autoincrement())
  product_attribute_category_id        Int
  note                         String?  @db.Text
  product_attributes               product_attributes[]
  product_attribute_translation    product_attribute_translation[]
  product_attribute_categories         product_attribute_categories      @relation(fields: [product_attribute_category_id], references: [product_attribute_category_id])

  @@index([product_attribute_category_id], map: "idx_product_attribute_category_id")
}

model product_attribute_categories {
  product_attribute_category_id              Int     @id @default(autoincrement())
  category_code                      String  @db.VarChar(20)  
  product_attributes               product_attributes[]
  product_attribute_translation    product_attribute_translation[]
  product_attribute_values                   product_attribute_values[]

  @@index([category_code], map: "idx_category_code")
}

model product_attribute_translation {
  attributes_translation_id       Int                                 @id @default(autoincrement())
  product_attribute_category_id           Int?
  product_attribute_value_id              Int?
  language_code                           String                              @db.VarChar(5)
  name                            String                              @db.VarChar(100)

  product_attribute_categories            product_attribute_categories?               @relation(fields: [product_attribute_category_id], references: [product_attribute_category_id])
  product_attribute_values                product_attribute_values?                   @relation(fields: [product_attribute_value_id], references: [product_attribute_value_id])

  @@index([product_attribute_category_id, language_code], map: "idx_category_language")
  @@index([product_attribute_value_id, language_code], map: "idx_value_language")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model products {
  product_id             Int                      @id @default(autoincrement())
  event_id               Int
  region                 String                   @db.VarChar(3)
  is_active              Boolean?                 @default(true)
  default_sku_id         Int?                      
  status                 product_status           @default(draft)
  type                   product_type             @default(main)
  currency               String                   @default("HKD") @db.VarChar(3)
  category               Int?                     @default(1) @db.TinyInt
  pickup_start_date      Int?
  pickup_end_date        Int?
  shipping_methods       Json?
  sale_start_date        Int?
  sale_end_date          Int?
  remarks                String?                  @db.Text
  created_at             Int
  updated_at             Int
  order_items            order_items[]
  product_inventory      product_inventory[]
  product_inventory_hold product_inventory_hold[]
  product_media          product_media[]
  product_sku            product_sku[]            @relation("AllSKU")
  product_translations   product_translations[]
  shopping_cart_item     shopping_cart_item[]
  product_attributes     product_attributes[]
  country                country                  @relation(fields: [region], references: [country_code])  
  defaultSKU             product_sku?             @relation("DefaultSKU", fields: [default_sku_id], references: [sku_id])
  product_to_ticket      ticket_to_gift[]         @relation("gift_to_ticket")
  product_redeem_record  redeem_gift_record[]     @relation("redeem_gift")
  event                  event                    @relation("event_products", fields: [event_id], references: [id])

  @@index([category], map: "idx_category")
  @@index([event_id], map: "idx_event_id")
  @@index([region], map: "idx_region")
  @@index([status], map: "idx_status")
  @@index([type], map: "idx_type")
  @@index([default_sku_id], map: "idx_default_sku_id")
}

model country {
  country_id            Int          @id @default(autoincrement())
  country_code          String       @unique(map: "uk_country_code") @db.VarChar(3)
  is_active             Boolean      @default(true)
  products              products[]
  countryTranslations   country_translations[]

  @@index([country_code], map: "idx_country_code")
}

model country_translations {
  country_translations_id    Int                                 @id @default(autoincrement())
  country_code               String                              @db.VarChar(3)
  language_code              String                              @db.VarChar(5)
  name                       String                              @db.VarChar(255)
  created_at                 Int
  updated_at                 Int
  country                    country                             @relation(fields: [country_code], references: [country_code])

  @@unique([country_code, language_code], map: "uk_country_language")
  @@index([country_code, language_code], map: "idx_language_code")
}

model idempotency_keys {
  idempotency_key       String                        @id @db.VarChar(128)
  user_id               Int                
  status                idempotency_token_status
  http_method           String                        @db.VarChar(10)
  route_path            String                        @db.VarChar(255)
  request_hash          String                        @db.VarChar(64)
  response_code         Int?                          @db.SmallInt
  response_body         Json?
  created_at            Int
  updated_at            Int
  expires_at            Int

  @@index([user_id], map: "idx_user_id")
  @@index([http_method, route_path, request_hash], map: "idx_route")
  @@index([expires_at], map: "idx_expires")
}

model global_config {
  config_id       Int             @id @default(autoincrement())
  config_group    String          @db.VarChar(50)
  config_key      String          @db.VarChar(50)
  config_value    String          @db.VarChar(255)
  is_active       Boolean
  created_at      Int
  updated_at      Int

  @@unique([config_group, config_key], map: "uk_config_group_key")
  @@index([config_group, config_key], map: "idx_config_group_key")
}

enum User_gender {
  male
  female
  transgender_female
  transgender_male
  non_binary
  agender
  not_listed
  not_to_state
}

enum User_role {
  creator
  visitor
}

enum translations_language_code {
  EN
  TC
  TS
}

enum product_media_media_type {
  image
  video
}

enum product_status {
  listed
  unListed
  draft
}

enum product_type {
  main
  merchant
}

enum hold_status {
  hold
  deducted
  released
}

enum idempotency_token_status {
  unused 
  used
  expired
  failed
}