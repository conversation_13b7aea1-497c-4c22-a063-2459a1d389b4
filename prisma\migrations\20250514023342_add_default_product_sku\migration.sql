/*
  Warnings:

  - Added the required column `is_default` to the `product_sku` table without a default value. This is not possible if the table is not empty.
  - Added the required column `default_sku_id` to the `products` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `product_sku` ADD COLUMN `is_default` TINYINT NOT NULL;

-- AlterTable
ALTER TABLE `products` ADD COLUMN `default_sku_id` INTEGER NOT NULL;

-- CreateIndex
CREATE INDEX `idx_default_sku_id` ON `products`(`default_sku_id`);

-- AddForeignKey
ALTER TABLE `products` ADD CONSTRAINT `products_default_sku_id_fkey` FOREIGN KEY (`default_sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
