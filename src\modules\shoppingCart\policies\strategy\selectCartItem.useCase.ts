import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import {
  CartCheckParameter,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';

import {
  checkCartExistStrategy,
  checkProductListedStrategy,
  skuValidateStrategy,
} from '../implementations';
import { checkCartItemExistStrategy } from '../implementations/checkCartItemExists.strategy';

type ParameterType = CartCheckParameter & CartItemStrategyParameterType;
export const selectCartItemStrategyMap: CartCheckStrategy<ParameterType>[] = [
  checkCartExistStrategy,
  checkCartItemExistStrategy,
  skuValidateStrategy,
  checkProductListedStrategy,
];
