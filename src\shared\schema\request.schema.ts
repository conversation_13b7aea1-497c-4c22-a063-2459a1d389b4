import { z } from 'zod';

import { Language, SortOrder } from '@shared/enums';
import { getAllCountries } from 'countries-and-timezones';

type AllRequestSchema = { body?: z.ZodTypeAny; query?: z.ZodTypeAny; params?: z.ZodTypeAny };
export const requestSchema = {
  body: <T extends z.ZodTypeAny>(schema: T) => z.object({ body: schema }),
  query: <T extends z.ZodTypeAny>(schema: T) => z.object({ query: schema }),
  params: <T extends z.ZodTypeAny>(schema: T) => z.object({ params: schema }),
  all: <T extends AllRequestSchema>(schema: T) =>
    z.object({
      body: schema?.body ?? z.any(),
      query: schema?.query ?? z.any(),
      params: schema?.params ?? z.any(),
    }),
};

const languageRefine = (language: string): boolean => {
  const validList = [...Object.keys(getAllCountries()), 'EN', 'TC', 'TS'];
  return validList.includes(language);
};

export const languageCodeSchema = z.string().transform((val) => val.toUpperCase());

export const languageQuerySchema = z.object({
  language: languageCodeSchema.transform((val) => val as Language),
});

export const paginationQuerySchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  size: z.coerce.number().int().positive().max(9999).default(12),
  sort: z.string().optional(),
  sortOrder: z.enum([SortOrder.ASC, SortOrder.DESC]).optional(),
});
