import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import {
  CartCheckParameter,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';

import {
  checkCartExistStrategy,
  checkRegionStrategy,
  skuValidateStrategy,
} from '../implementations';
import { checkCartItemExistStrategy } from '../implementations/checkCartItemExists.strategy';

type ParameterType = CartCheckParameter & CartItemStrategyParameterType;
export const deleteCartItemStrategyMap: CartCheckStrategy<ParameterType>[] = [
  checkCartExistStrategy,
  checkCartItemExistStrategy,
  skuValidateStrategy,
  checkRegionStrategy,
];
