# Docker and Kubernetes Environment Variables Setup

## Overview

This document explains how environment variables are handled in the Docker container and Kubernetes deployment for the Incutix API. The application is designed to receive environment variables from Kubernetes secrets and config maps rather than bundling them in the Docker image.

## Docker Configuration

### Environment Variables in Dockerfile

The Dockerfile defines the following environment variables that will be overridden by Kubernetes:

```dockerfile
ENV NODE_ENV=production
ENV DATABASE_URL=""
ENV DATABASE_READ_URL=""
ENV AWS_ACCESS_KEY_ID=""
ENV AWS_SECRET_ACCESS_KEY=""
ENV AWS_REGION=""
ENV JWT_SECRET=""
ENV LAB_PAY_MERCHANT_APP_ID=""
ENV LAB_PAY_MERCHANT_ID=""
ENV LAB_PAY_KEY=""
```

### Security Features

1. **No .env files in container**: The `.dockerignore` file prevents environment files from being copied into the Docker image
2. **Non-root user**: The container runs as a non-root user `appuser` for security
3. **Health checks**: Built-in health check endpoint at `/health`
4. **Startup script**: Handles database migrations and seeding based on environment

### Startup Process

The container startup script (`/app/start.sh`) performs the following:

1. **Database Migrations**: Runs `prisma migrate deploy` if `DATABASE_URL` is provided
2. **Database Seeding**: Runs seeding only in development/UAT environments
3. **Application Start**: Starts the Node.js application with nodemon

## Kubernetes Configuration

### Required Environment Variables

The following environment variables must be configured in Rancher Kubernetes:

#### Database Configuration
- `DATABASE_URL`: Primary database connection string
- `DATABASE_READ_URL`: Read replica database connection string

#### AWS Configuration
- `AWS_ACCESS_KEY_ID`: AWS access key for S3 and other services
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key
- `AWS_REGION`: AWS region (e.g., ap-southeast-1)

#### Authentication
- `JWT_SECRET`: Secret key for JWT token signing

#### Payment Gateway (Lab Pay)
- `LAB_PAY_MERCHANT_APP_ID`: Lab Pay merchant application ID
- `LAB_PAY_MERCHANT_ID`: Lab Pay merchant ID
- `LAB_PAY_KEY`: Lab Pay API key

#### Application Configuration
- `NODE_ENV`: Environment (production, uat, development)
- `FRONT_END_URL`: Frontend application URL
- `S3_PUBLIC`: Public S3 URL
- `S3_BUCKET_NAME`: S3 bucket for public files
- `S3_BUCKET_PRIVATE`: S3 bucket for private files
- `NOTIFY_URL`: Callback URL for payment notifications
- `RETURN_URL`: Return URL after payment
- `LAB_PAY_API_URL`: Lab Pay API endpoint
- `SENDER_EMAIL`: Email sender address
- `SUPPORT_EMAIL`: Support team email addresses
- `ERROR_EMAIL`: Error notification email addresses

### Rancher Dashboard Setup

#### 1. Create Secrets

Create the following secrets in Rancher Dashboard:

**Database Secret** (`incutix-db-secret`):
- `database-url`: Base64 encoded database URL
- `database-read-url`: Base64 encoded read database URL

**AWS Secret** (`incutix-aws-secret`):
- `access-key-id`: Base64 encoded AWS access key
- `secret-access-key`: Base64 encoded AWS secret key

**JWT Secret** (`incutix-jwt-secret`):
- `jwt-secret`: Base64 encoded JWT secret

**Lab Pay Secret** (`incutix-labpay-secret`):
- `merchant-app-id`: Base64 encoded merchant app ID
- `merchant-id`: Base64 encoded merchant ID
- `lab-pay-key`: Base64 encoded Lab Pay key

#### 2. Create Deployment

Create a deployment with the following configuration:

**Basic Settings**:
- Name: `incutix-nodejs-api`
- Namespace: `incutix-private` (UAT) or `incutix-public` (Pre-prod/Prod)
- Replicas: 2 (or as needed)

**Container Settings**:
- Container Name: `incutix-nodejs-api`
- Image: Your ECR repository URL
- Ports: 5000, 8080

**Environment Variables**:
- Reference the secrets created above for sensitive data
- Set non-sensitive config as direct values

**Health Checks**:
- Liveness Probe: HTTP GET `/health` on port 5000
- Readiness Probe: HTTP GET `/health` on port 5000

**Resource Limits**:
- Memory Request: 256Mi, Limit: 512Mi
- CPU Request: 250m, Limit: 500m

#### 3. Create Service

Create a service to expose the deployment:

**Service Settings**:
- Name: `incutix-nodejs-api-service`
- Type: ClusterIP (or LoadBalancer if external access needed)
- Ports: 5000 → 5000, 8080 → 8080
- Selector: `app=incutix-nodejs-api`

## Environment-Specific Configuration

### UAT Environment
- Namespace: `incutix-private`
- NODE_ENV: `uat`
- Database seeding: Enabled
- URLs: Development/UAT endpoints

### Pre-Production Environment
- Namespace: `incutix-public`
- NODE_ENV: `production`
- Database seeding: Disabled
- URLs: Pre-production endpoints

### Production Environment
- Namespace: `incutix-public`
- NODE_ENV: `production`
- Database seeding: Disabled
- URLs: Production endpoints

## Health Monitoring

### Health Check Endpoints

- `/health`: Comprehensive health check with status, timestamp, uptime
- `/ping`: Simple ping endpoint

### Monitoring in Kubernetes

The deployment includes:
- Liveness probes to restart unhealthy containers
- Readiness probes to control traffic routing
- Resource monitoring and limits

## Troubleshooting

### Common Issues

1. **Container fails to start**:
   - Check if all required environment variables are set
   - Verify database connectivity
   - Check container logs in Rancher

2. **Database migration fails**:
   - Ensure DATABASE_URL is correctly set
   - Check database permissions
   - Verify network connectivity to database

3. **Health check failures**:
   - Verify the application is listening on port 5000
   - Check if `/health` endpoint is accessible
   - Review application logs for errors

### Debugging Commands

```bash
# Check pod status
kubectl get pods -n incutix-private

# View pod logs
kubectl logs <pod-name> -n incutix-private

# Describe pod for events
kubectl describe pod <pod-name> -n incutix-private

# Test health endpoint
kubectl port-forward <pod-name> 5000:5000 -n incutix-private
curl http://localhost:5000/health
```

## Security Best Practices

1. **Use Kubernetes Secrets**: Never store sensitive data in environment variables directly
2. **Least Privilege**: Run containers as non-root users
3. **Network Policies**: Implement network policies to restrict pod-to-pod communication
4. **Resource Limits**: Set appropriate CPU and memory limits
5. **Image Scanning**: Regularly scan Docker images for vulnerabilities
6. **Secret Rotation**: Regularly rotate secrets and API keys

## Next Steps

1. Create the required secrets in Rancher Dashboard
2. Deploy the application using the provided configuration
3. Monitor the deployment and verify all environment variables are correctly set
4. Test the application functionality in each environment
5. Set up monitoring and alerting for the deployment
