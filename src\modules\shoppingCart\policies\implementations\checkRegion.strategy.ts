import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import {
  CartCheckStrategyParameterType,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import { generateTimeNow } from '@utils/common';

type ParameterType = CartCheckStrategyParameterType & CartItemStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkRegionStrategy: Strategy = async (parameter: ParameterType) => {
  const { userId, productId } = parameter;

  // Get cart
  const countShoppingCart = await db.shopping_cart.findUnique({
    select: { cart_id: true },
    where: { user_id: userId, expires_at: { gte: generateTimeNow() } },
  });
  const { cart_id: cartId } = countShoppingCart ?? {};

  // Add product region
  const addProductInfo = await db.products.findUnique({
    select: { region: true },
    where: { product_id: productId },
  });
  const { region: addProductRegion } = addProductInfo ?? {};

  // Search any not match add product region in cart items.
  const shoppingCartRegion = await db.shopping_cart_item.count({
    where: {
      cart_id: cartId,
      shoppingCart: {
        expires_at: { gte: generateTimeNow() },
      },
      products: { NOT: { region: addProductRegion } },
    },
  });

  if (shoppingCartRegion > 0) {
    throw new CustomErrorException(errorCodeTable.cartAddItemInvalidRegion);
  }
};
