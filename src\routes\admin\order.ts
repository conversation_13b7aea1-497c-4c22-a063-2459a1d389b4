import { Router } from 'express';
import * as orderController from '@modules/order/order.controller';

const router = Router();

router.get('/', orderController.getOrderList);
router.get('/:id', orderController.getSalesOrderById);
router.put('/order-status', orderController.updateOrderStatus);
router.put('/shipping-status', orderController.updateOrderShippingStatus);
router.put('/shipping-status/batch', orderController.batchUpdateOrderShippingStatus);
router.post('/remark', orderController.updateOrderAdminNote);

export default router;
