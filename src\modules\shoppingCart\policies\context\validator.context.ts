import {
  CartC<PERSON>ckParameter,
  CartCheckStrategyParameterType,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';
import { StrategyMap, UseCase } from '@modules/shoppingCart/enums/useCase.enum';

import {
  addToCartStrategyMap,
  changeCartItemQuantityStrategyMap,
  deleteCartItemStrategyMap,
  selectCartItemStrategyMap,
} from '../strategy';
import { clearCartStrategyMap } from '../strategy/clearCart.useCase';
import { checkoutCartStrategyMap } from '../strategy/checkoutCart.useCase';

type ParameterType = CartCheckParameter & CartItemStrategyParameterType;
export class ValidatorContext {
  private static cartValidateStrategy: StrategyMap<ParameterType> = {
    [UseCase.addProductToCart]: addToCartStrategyMap,
    [UseCase.changeCartItem]: changeCartItemQuantityStrategyMap,
    [UseCase.deleteCartItem]: deleteCartItemStrategyMap,
    [UseCase.selectCartItem]: selectCartItemStrategyMap,
    [UseCase.emptyCart]: clearCartStrategyMap,
    [UseCase.checkout]: checkoutCartStrategyMap,
  };

  public static execute = async (useCase: UseCase, parameter: CartCheckStrategyParameterType) => {
    // run selectCartItemStrategy
    if ('products' in parameter) {
      const { userId, products } = parameter;
      for (const strategy of ValidatorContext.cartValidateStrategy[useCase]) {
        for (const { cartItemType, productId, skuId } of products) {
          await strategy({ userId, cartItemType, productId, skuId });
        }
      }
      // run rest strategy
    } else {
      for (const strategy of ValidatorContext.cartValidateStrategy[useCase]) {
        await strategy(parameter as ParameterType);
      }
    }
  };
}
