import { PrismaClient } from '@prisma/client';
import { readReplicas } from '@prisma/extension-read-replicas';
import { logger } from '@/utils/logger';

const db = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'stdout',
      level: 'error',
    },
    {
      emit: 'stdout',
      level: 'info',
    },
    {
      emit: 'stdout',
      level: 'warn',
    },
  ],
});

db.$extends(
  readReplicas({
    url: process.env.DATABASE_READ_URL || '',
  }),
);

db.$on('query', (e) => {
  logger.debug(`Query:    ${e.query}`);
  logger.debug(`Params:   ${e.params}`);
  logger.debug(`Duration: ${e.duration} ms`);
});

export default db;
