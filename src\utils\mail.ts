import AWS from 'aws-sdk';
import nodemailer from 'nodemailer';
import { logger } from './logger';

const ses = new AWS.SES();

const transporter = nodemailer.createTransport({
  SES: ses,
});

export const sendEmail = async (to: string, subject: string, message: string) => {
  const params = {
    Source: process.env.SENDER_EMAIL || '<EMAIL>', // Verified email in SES
    Destination: {
      ToAddresses: [to],
    },
    Message: {
      Body: {
        Text: { Data: message },
      },
      Subject: { Data: subject },
    },
  };
  return ses.sendEmail(params).promise();
};

export const sendEmailWithAttachment = async (
  to: string,
  subject: string,
  message: string,
  attachments: { name: string; content: string }[],
  sender?: string,
) => {
  try {
    return await transporter.sendMail({
      from: sender || process.env.SENDER_EMAIL || '<EMAIL>',
      to,
      subject,
      text: message,
      html: message,
      attachments: attachments.map((attachment) => {
        return {
          filename: attachment.name,
          content: attachment.content,
        };
      }),
    });
  } catch (err) {
    logger.error('[Send Email Error] >> ' + err);
    return undefined;
  }
};

export const sendHTMLMail = async (
  to: string,
  subject: string,
  message: string,
  sender?: string,
) => {
    try {
    return await transporter.sendMail({
      from: sender || process.env.SENDER_EMAIL || '<EMAIL>',
      to,
      subject,
      html: message,
    });
  } catch (err) {
    logger.error('[Send Email Error] >> ' + err);
    return undefined;
  }
}

export default ses;
