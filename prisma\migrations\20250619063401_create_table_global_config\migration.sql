-- CreateTable
CREATE TABLE `global_config` (
    `config_id` INTEGER NOT NULL AUTO_INCREMENT,
    `config_group` VARCHAR(50) NOT NULL,
    `config_key` VARCHAR(50) NOT NULL,
    `config_value` JSON NOT NULL,
    `is_active` BOOLEAN NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_config_group_key`(`config_group`, `config_key`),
    PRIMARY KEY (`config_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE UNIQUE INDEX `uk_config_group_key` ON `global_config`(`config_group`, `config_key`);

ALTER TABLE `global_config` MODIFY `config_value` VARCHAR(255) NOT NULL;
