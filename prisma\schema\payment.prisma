model payment_method {
  payment_id              Int                @id @default(autoincrement())
  payment_code            String             @db.VarChar(50)
  payment_name            String             @db.VarChar(100)
  payment_region          String?            @db.VarChar(100)
  payment_platform        String?            @db.VarChar(50)
  payment_type            payment_type?      
  status                  payment_status
  description             String?            @db.Text
  config                  Json?
  createdAt               Int
  updatedAt               Int

  @@unique([payment_code, payment_region], map: "uk_code_region")
  @@index([payment_code], map: "idx_payment_code")
  @@index([payment_region], map: "idx_payment_region")
  @@index([payment_platform], map: "idx_payment_platform")
}

enum payment_status {
  enabled
  disabled
}

enum payment_type {
  QRCODE
  MOBILEWEB
  WEB
  REDIRECT
}