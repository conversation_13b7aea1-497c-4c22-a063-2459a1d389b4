-- AlterTable
ALTER TABLE `ticket_section` ADD COLUMN `used` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `ticket_type` ADD COLUMN `status` ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE';

-- CreateTable
CREATE TABLE `ticket_inventory` (
    `total` INTEGER NOT NULL,
    `available` INTEGER NOT NULL,
    `on_hold` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_section_id` INTEGER NOT NULL,

    PRIMARY KEY (`ticket_section_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_adjustment` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `amount` INTEGER NOT NULL,
    `before` INTEGER NOT NULL,
    `after` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_section_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_distribution` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `ticket_section_id` INTEGER NOT NULL,
    `ticket_type_id` INTEGER NOT NULL,

    UNIQUE INDEX `ticket_distribution_ticket_type_id_ticket_section_id_key`(`ticket_type_id`, `ticket_section_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ticket_distribution_adjustment` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `amount` INTEGER NOT NULL,
    `before` INTEGER NOT NULL,
    `after` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_distribution_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ticket_inventory` ADD CONSTRAINT `ticket_inventory_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_adjustment` ADD CONSTRAINT `ticket_adjustment_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_distribution` ADD CONSTRAINT `ticket_distribution_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_distribution` ADD CONSTRAINT `ticket_distribution_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_distribution_adjustment` ADD CONSTRAINT `ticket_distribution_adjustment_ticket_distribution_id_fkey` FOREIGN KEY (`ticket_distribution_id`) REFERENCES `ticket_distribution`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
