import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { generateTimeNow } from '@utils/common';
import {
  CartCheckStrategyParameterType,
  CheckoutCartCheckParameter,
} from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';

type ParameterType = CartCheckStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkUserCartValidStrategy: Strategy = async (parameter) => {
  const { userId, cartId } = parameter as CheckoutCartCheckParameter;

  // Check the cart is exists
  const countShoppingCart = await db.shopping_cart.count({
    where: {
      user_id: userId,
      cart_id: cartId,
      expires_at: { gte: generateTimeNow() },
    },
  });
  if (countShoppingCart === 0) {
    throw new CustomErrorException(errorCodeTable.cartCheckoutCartInvalid, { userId, cartId });
  }

  // Check cart item exists
  const countShoppingCartItems = await db.shopping_cart_item.count({
    where: {
      shoppingCart: {
        user_id: userId,
        cart_id: cartId,
        expires_at: { gte: generateTimeNow() },
      },
    },
  });
  if (countShoppingCartItems === 0) {
    throw new CustomErrorException(errorCodeTable.cartCheckoutNoCartItems, { userId, cartId });
  }
};
