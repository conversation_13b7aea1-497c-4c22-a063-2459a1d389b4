#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Log in to AWS ECR
aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 433299495796.dkr.ecr.ap-southeast-1.amazonaws.com

# Build the Docker image
docker build -t hkgt/api .

# Tag the Docker image
docker tag hkgt/api:latest 433299495796.dkr.ecr.ap-southeast-1.amazonaws.com/hkgt/api:1.1.10

# Push the Docker image to the repository
docker push 433299495796.dkr.ecr.ap-southeast-1.amazonaws.com/hkgt/api:1.1.10

