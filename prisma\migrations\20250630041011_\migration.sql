/*
  Warnings:

  - Added the required column `ticket_date_inventory_id` to the `ticket` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `ticket` ADD COLUMN `ticket_date_inventory_id` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `ticket` ADD CONSTRAINT `ticket_ticket_date_inventory_id_fkey` FOREIGN KEY (`ticket_date_inventory_id`) REFERENCES `ticket_date_inventory`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
