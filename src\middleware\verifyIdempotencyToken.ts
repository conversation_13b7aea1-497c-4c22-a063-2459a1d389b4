import { Request, Response, NextFunction } from 'express';

import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { generateTimeNow } from '@utils/common';
import { PrismaIdempotencyKeyStatus } from '@modules/idempotencyKey/enum/idempotencyKey.enum';

export const verifyIdempotencyToken = async (req: Request, res: Response, next: NextFunction) => {
  const token = req.get('X-Incutix-Idempotency-Key');

  if (!token) {
    next(new CustomErrorException(errorCodeTable.idempotencyKeyMissing));
    return false;
  }

  // 1. Retrieve Token]
  const retrieveToken = await db.idempotency_keys.findUnique({
    where: {
      idempotency_key: token,
      expires_at: { gt: generateTimeNow() },
    },
  });

  if (!retrieveToken) {
    next(new CustomErrorException(errorCodeTable.idempotencyKeyInvalid));
  }

  // 2. Check token status
  if (retrieveToken && retrieveToken.status !== PrismaIdempotencyKeyStatus.unused) {
    next(new CustomErrorException(errorCodeTable.idempotencyKeyIsUsed, retrieveToken));
  }

  // 3. Add
  req.tokenData = retrieveToken;
  next();
};
