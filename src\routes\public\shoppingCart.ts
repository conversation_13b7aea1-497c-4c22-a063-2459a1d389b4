import { Router } from 'express';

import { tokenAuthenticate } from '@/middleware';
import * as shoppingCartController from '@modules/shoppingCart/shoppingCart.controller';

const router = Router();

router.get('/', tokenAuthenticate, shoppingCartController.getShoppingCart);
router.post('/', tokenAuthenticate, shoppingCartController.addShoppingCartItem);
router.put('/', tokenAuthenticate, shoppingCartController.changeShoppingCartItemQuantity);
router.delete('/', tokenAuthenticate, shoppingCartController.deleteShoppingCartItem);
router.get('/count', tokenAuthenticate, shoppingCartController.getShoppingCartCount);
router.post('/select', tokenAuthenticate, shoppingCartController.selectShoppingCartItem);
router.post('/clear', tokenAuthenticate, shoppingCartController.clearShoppingCartItem);
router.post(
  '/checkout/idempotency-key',
  tokenAuthenticate,
  shoppingCartController.getCheckoutIdempotencyKey,
);
router.post('/checkout', tokenAuthenticate, shoppingCartController.checkoutShoppingCart);

router.post(
  '/checkout/only-one/idempotency-key',
  tokenAuthenticate,
  shoppingCartController.getCheckoutOnlyOneIdempotencyKey,
);
router.post(
  '/checkout/only-one',
  tokenAuthenticate,
  shoppingCartController.checkoutOnlyOneShoppingCart,
);

export default router;
