export enum PaymentPlatform {
  weChatPay = 'WeChatPay',
  alipay = 'AliPay',
  airwallex = 'Airwallex',
}

export enum PaymentType {
  qrCode = 'QRCODE',
  mobileWeb = 'MOBILEWEB',
  web = 'WEB',
  redirect = 'REDIRECT',
}

export enum PaymentTransactionState {
  SUCCESS = 'SUCCESS',
  REFUND = 'REFUND',
  RECEIVED = 'RECEIVED',
  NOT_PAY = 'NOTPAY',
  CLOSED = 'CLOSED',
  REVOKED = 'REVOKED',
  USER_PAYING = 'USERPAYING',
  PAY_ERROR = 'PAYERROR',
}
