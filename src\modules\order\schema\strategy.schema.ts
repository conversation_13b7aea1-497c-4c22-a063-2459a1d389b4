import { OrderShippingStatus, OrderStatus } from '../enum/order.enum';

export type OrderCheckStrategyParameterType =
  | (OrderCheckParameter & BatchOrderIdParameter)
  | UpdateOrderStatusParameter
  | UpdateOrderAdminNoteParameter;

export type OrderCheckParameter = {
  orderId?: number;
  shippingStatus: OrderShippingStatus;
};

export type BatchOrderIdParameter = {
  orderIds?: number[];
  shippingStatus: OrderShippingStatus;
};

export type UpdateOrderStatusParameter = {
  orderId: number;
  orderStatus: OrderStatus;
};

export type UpdateOrderAdminNoteParameter = {
  orderId: number;
};
