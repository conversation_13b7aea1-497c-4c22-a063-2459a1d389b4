-- CreateTable
CREATE TABLE `FeaturedEvents` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `eventName` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `eventStartDate` INTEGER NOT NULL,
    `eventEndDate` INTEGER NOT NULL,
    `region` VARCHAR(191) NOT NULL,
    `timeZone` VARCHAR(191) NOT NULL,
    `eventTime` VARCHAR(191) NOT NULL,
    `aboutTime` VARCHAR(191) NOT NULL,
    `thumbnail` VARCHAR(191) NULL,
    `Venue` VARCHAR(191) NULL,
    `category` VARCHAR(191) NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NULL,
    `userId` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Item` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `promoCodeId` INTEGER NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `visible` BOOLEAN NOT NULL,
    `quantity` INTEGER NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,

    INDEX `Item_promoCodeId_fkey`(`promoCodeId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PromoCodeRecords` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `promoCodeId` INTEGER NOT NULL,
    `userId` INTEGER NOT NULL,
    `status` ENUM('applied', 'redeem') NOT NULL,
    `referenceOrderNo` INTEGER NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,

    INDEX `PromoCodeRecords_promoCodeId_fkey`(`promoCodeId`),
    INDEX `PromoCodeRecords_referenceOrderNo_fkey`(`referenceOrderNo`),
    INDEX `PromoCodeRecords_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PromoTypes` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `promoCode` VARCHAR(191) NOT NULL,
    `discountAmount` INTEGER NOT NULL,
    `currentQuantity` INTEGER NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,
    `type` INTEGER NOT NULL,
    `maxRedeem` INTEGER NOT NULL DEFAULT 1,

    INDEX `promoCode_idx`(`promoCode`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Ticket` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `orderId` INTEGER NULL,
    `productId` INTEGER NULL,
    `itemId` INTEGER NULL,
    `src` VARCHAR(191) NULL,
    `status` INTEGER NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,

    INDEX `Ticket_itemId_fkey`(`itemId`),
    INDEX `Ticket_orderId_fkey`(`orderId`),
    INDEX `Ticket_productId_fkey`(`productId`),
    INDEX `Ticket_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `TicketConfig` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `productId` INTEGER NULL,
    `itemId` INTEGER NULL,
    `throughTicketSystem` INTEGER NOT NULL,
    `content` TINYTEXT NULL,
    `eventId` VARCHAR(191) NOT NULL,
    `config` VARCHAR(191) NOT NULL,
    `createdAt` INTEGER NULL,
    `updatedAt` INTEGER NULL,

    UNIQUE INDEX `TicketConfig_itemId_key`(`itemId`),
    INDEX `TicketConfig_productId_fkey`(`productId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Transaction` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `refNo` INTEGER NOT NULL,
    `orderNo` VARCHAR(191) NOT NULL,
    `url` VARCHAR(191) NOT NULL,
    `urlType` VARCHAR(191) NOT NULL,
    `status` INTEGER NOT NULL,
    `orderId` INTEGER NOT NULL,
    `total` DECIMAL(9, 2) NOT NULL,
    `currency` INTEGER NOT NULL,
    `createdDate` INTEGER NOT NULL,
    `updatedDate` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `User` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `email` VARCHAR(191) NOT NULL,
    `password` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `verificationCode` INTEGER NULL,
    `resetPasswordToken` VARCHAR(191) NULL,
    `resetPasswordExpires` DATETIME(3) NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT false,
    `gender` ENUM('male', 'female', 'transgender_female', 'transgender_male', 'non_binary', 'agender', 'not_listed', 'not_to_state') NULL,
    `phoneNumber` VARCHAR(191) NULL,
    `dateOfBirth` DATETIME(3) NULL,
    `countryCode` VARCHAR(2) NULL,
    `address` VARCHAR(191) NULL,
    `photoUrl` VARCHAR(191) NULL,
    `receivePromotions` BOOLEAN NOT NULL DEFAULT false,
    `identity` VARCHAR(191) NOT NULL DEFAULT 'personal',
    `role` ENUM('creator', 'visitor') NOT NULL,
    `supportGoogle` BOOLEAN NULL,
    `supportFacebook` BOOLEAN NULL,

    UNIQUE INDEX `User_email_key`(`email`),
    INDEX `email_idx`(`email`),
    INDEX `name_idx`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `itemId` INTEGER NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `visible` BOOLEAN NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,

    INDEX `UserItem_itemId_fkey`(`itemId`),
    INDEX `UserItem_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Address` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `addressType` INTEGER NOT NULL DEFAULT 1,
    `recipientName` VARCHAR(191) NULL,
    `recipientphone` VARCHAR(191) NULL,
    `countryCode` VARCHAR(191) NULL,
    `country` VARCHAR(191) NULL,
    `province` VARCHAR(191) NULL,
    `city` VARCHAR(191) NULL,
    `district` VARCHAR(191) NULL,
    `street` VARCHAR(191) NULL,
    `addressLine1` VARCHAR(191) NULL,
    `addressLine2` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `postalCode` VARCHAR(191) NULL,
    `isDefaultBilling` BOOLEAN NOT NULL,
    `isDefaultDelivery` BOOLEAN NOT NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,

    INDEX `Address_userId_fkey`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `order_items` (
    `order_item_id` INTEGER NOT NULL AUTO_INCREMENT,
    `order_id` INTEGER NOT NULL,
    `product_id` INTEGER NULL,
    `sku_id` INTEGER NULL,
    `quantity` INTEGER NOT NULL,
    `unit_price` DECIMAL(12, 2) NOT NULL,
    `original_price` DECIMAL(12, 2) NOT NULL,
    `total_price` DECIMAL(12, 2) NOT NULL,
    `discount_amount` DECIMAL(12, 2) NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_order_id`(`order_id`),
    INDEX `idx_product_id`(`product_id`),
    INDEX `idx_sku_id`(`sku_id`),
    UNIQUE INDEX `uk_order`(`order_id`, `product_id`, `sku_id`),
    PRIMARY KEY (`order_item_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `order_shipping` (
    `order_shipping_id` INTEGER NOT NULL AUTO_INCREMENT,
    `order_id` INTEGER NOT NULL,
    `shipping_status` BOOLEAN NOT NULL DEFAULT true,
    `shipping_type` BOOLEAN NOT NULL,
    `tracking_no` VARCHAR(255) NULL,
    `consignee` VARCHAR(255) NULL,
    `country_code` VARCHAR(5) NULL,
    `contract_no` VARCHAR(20) NULL,
    `address` TEXT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    UNIQUE INDEX `uk_order_id`(`order_id`),
    INDEX `idx_shipping_status`(`shipping_status`),
    INDEX `idx_shipping_type`(`shipping_type`),
    PRIMARY KEY (`order_shipping_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `orders` (
    `order_id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `order_no` VARCHAR(20) NOT NULL,
    `region` VARCHAR(3) NOT NULL,
    `status` BOOLEAN NOT NULL DEFAULT true,
    `type` BOOLEAN NOT NULL,
    `total_amount` DECIMAL(12, 2) NOT NULL,
    `sub_total` DECIMAL(12, 2) NOT NULL,
    `shipping_fee` DECIMAL(12, 2) NOT NULL,
    `tax_amount` DECIMAL(12, 2) NOT NULL,
    `discount_amount` DECIMAL(12, 2) NOT NULL,
    `coupon_code` VARCHAR(50) NOT NULL,
    `coupon_discount` DECIMAL(12, 2) NOT NULL,
    `currency` VARCHAR(3) NOT NULL DEFAULT 'HKD',
    `admin_note` TEXT NOT NULL,
    `customer_note` TEXT NOT NULL,
    `cancel_time` INTEGER NOT NULL,
    `complete_time` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    UNIQUE INDEX `uk_order_number`(`order_no`),
    INDEX `idx_order_no`(`order_no`),
    INDEX `idx_region`(`region`),
    INDEX `idx_status`(`status`),
    INDEX `idx_user_id`(`user_id`),
    PRIMARY KEY (`order_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_inventory` (
    `inventory_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_id` INTEGER NOT NULL,
    `sku_id` INTEGER NOT NULL,
    `total_quantity` INTEGER NOT NULL DEFAULT 0,
    `available_quantity` INTEGER NOT NULL DEFAULT 0,
    `reserved_quantity` INTEGER NOT NULL DEFAULT 0,
    `returned_quantity` INTEGER NOT NULL DEFAULT 0,
    `version` INTEGER NOT NULL DEFAULT 0,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_product_id`(`product_id`),
    INDEX `idx_sku_id`(`sku_id`),
    UNIQUE INDEX `uk_product_sku_version`(`product_id`, `sku_id`, `version`),
    PRIMARY KEY (`inventory_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_inventory_hold` (
    `product_inventory_hold_id` INTEGER NOT NULL AUTO_INCREMENT,
    `order_id` INTEGER NOT NULL,
    `product_id` INTEGER NOT NULL,
    `sku_id` INTEGER NOT NULL,
    `hold_quantity` INTEGER NOT NULL,
    `hold_status` TINYINT NOT NULL DEFAULT 1,
    `expire_time` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_product_sku`(`order_id`, `product_id`, `sku_id`),
    INDEX `product_id`(`product_id`),
    INDEX `sku_id`(`sku_id`),
    PRIMARY KEY (`product_inventory_hold_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_media` (
    `product_media_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_id` INTEGER NOT NULL,
    `sku_id` INTEGER NOT NULL,
    `media_type` ENUM('image', 'video') NOT NULL,
    `is_main` BOOLEAN NULL DEFAULT false,
    `url` VARCHAR(255) NULL,
    `priority` INTEGER NULL DEFAULT 1,
    `is_active` BOOLEAN NULL DEFAULT true,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_product_id`(`product_id`),
    INDEX `idx_sku_id`(`sku_id`),
    PRIMARY KEY (`product_media_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_sku` (
    `sku_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_id` INTEGER NOT NULL,
    `sku_code` VARCHAR(100) NOT NULL,
    `original_price` DECIMAL(12, 2) NOT NULL,
    `sale_price` DECIMAL(12, 2) NOT NULL,
    `cost_price` DECIMAL(12, 2) NULL,
    `tax` DECIMAL(12, 2) NULL,
    `attributes` JSON NOT NULL,
    `weight` DECIMAL(10, 2) NULL,
    `volume` DECIMAL(10, 2) NULL,
    `length` DECIMAL(10, 2) NULL,
    `width` DECIMAL(10, 2) NULL,
    `height` DECIMAL(10, 2) NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    UNIQUE INDEX `uk_sku_code`(`sku_code`),
    INDEX `idx_product_id`(`product_id`),
    PRIMARY KEY (`sku_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_translations` (
    `product_translation_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_id` INTEGER NOT NULL,
    `language_code` ENUM('EN', 'TC', 'TS') NULL DEFAULT 'EN',
    `name` VARCHAR(255) NOT NULL,
    `description` VARCHAR(500) NULL,
    `introduction` LONGTEXT NULL,
    `tag` JSON NULL,
    `pickup_location` TEXT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_language_code`(`language_code`),
    INDEX `idx_product_id`(`product_id`),
    UNIQUE INDEX `uk_product_language`(`product_id`, `language_code`),
    PRIMARY KEY (`product_translation_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `products` (
    `product_id` INTEGER NOT NULL AUTO_INCREMENT,
    `collection_id` INTEGER NOT NULL,
    `region` VARCHAR(3) NOT NULL,
    `is_active` BOOLEAN NULL DEFAULT true,
    `status` BOOLEAN NOT NULL DEFAULT false,
    `type` BOOLEAN NOT NULL DEFAULT true,
    `currency` VARCHAR(3) NOT NULL DEFAULT 'HKD',
    `category` TINYINT NULL DEFAULT 1,
    `pickup_start_date` INTEGER NULL,
    `pickup_end_date` INTEGER NULL,
    `shipping_methods` JSON NULL,
    `sale_start_date` INTEGER NULL,
    `sale_end_date` INTEGER NULL,
    `remarks` TEXT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_category`(`category`),
    INDEX `idx_collection_id`(`collection_id`),
    INDEX `idx_region`(`region`),
    INDEX `idx_status`(`status`),
    INDEX `idx_type`(`type`),
    PRIMARY KEY (`product_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `shopping_cart` (
    `cart_id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `cart_type` BOOLEAN NOT NULL,
    `is_checked_out` BOOLEAN NULL DEFAULT false,
    `cart_total` DECIMAL(12, 2) NULL DEFAULT 0.00,
    `discount_total` DECIMAL(12, 2) NULL DEFAULT 0.00,
    `item_count` INTEGER NULL DEFAULT 0,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `expires_at` INTEGER NULL,

    UNIQUE INDEX `uk_user_session`(`user_id`),
    INDEX `idx_expires`(`expires_at`),
    INDEX `idx_user_id`(`user_id`),
    PRIMARY KEY (`cart_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `shopping_cart_item` (
    `shopping_cart_item_id` INTEGER NOT NULL AUTO_INCREMENT,
    `cart_id` INTEGER NOT NULL,
    `cart_item_type` BOOLEAN NOT NULL DEFAULT true,
    `product_id` INTEGER NULL,
    `sku_id` INTEGER NULL,
    `quantity` INTEGER NOT NULL DEFAULT 1,
    `original_price` DECIMAL(12, 2) NOT NULL,
    `sale_price` DECIMAL(12, 2) NOT NULL,
    `total_price` DECIMAL(12, 2) NOT NULL,
    `added_at` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `notes` VARCHAR(200) NULL,

    INDEX `idx_cart_id`(`cart_id`),
    INDEX `idx_cart_item_type`(`cart_item_type`),
    INDEX `idx_product_sku`(`product_id`, `sku_id`),
    INDEX `idx_user_product`(`cart_id`, `product_id`, `sku_id`),
    INDEX `sku_id`(`sku_id`),
    PRIMARY KEY (`shopping_cart_item_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Item` ADD CONSTRAINT `Item_promoCodeId_fkey` FOREIGN KEY (`promoCodeId`) REFERENCES `PromoTypes`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PromoCodeRecords` ADD CONSTRAINT `PromoCodeRecords_promoCodeId_fkey` FOREIGN KEY (`promoCodeId`) REFERENCES `PromoTypes`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PromoCodeRecords` ADD CONSTRAINT `PromoCodeRecords_referenceOrderNo_fkey` FOREIGN KEY (`referenceOrderNo`) REFERENCES `orders`(`order_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `PromoCodeRecords` ADD CONSTRAINT `PromoCodeRecords_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Ticket` ADD CONSTRAINT `Ticket_itemId_fkey` FOREIGN KEY (`itemId`) REFERENCES `Item`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Ticket` ADD CONSTRAINT `Ticket_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `orders`(`order_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `Ticket` ADD CONSTRAINT `Ticket_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `Ticket` ADD CONSTRAINT `Ticket_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `TicketConfig` ADD CONSTRAINT `TicketConfig_itemId_fkey` FOREIGN KEY (`itemId`) REFERENCES `Item`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `TicketConfig` ADD CONSTRAINT `TicketConfig_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `UserItem` ADD CONSTRAINT `UserItem_itemId_fkey` FOREIGN KEY (`itemId`) REFERENCES `Item`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserItem` ADD CONSTRAINT `UserItem_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Address` ADD CONSTRAINT `Address_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders`(`order_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ibfk_3` FOREIGN KEY (`sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `order_shipping` ADD CONSTRAINT `order_shipping_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders`(`order_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `orders` ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_inventory` ADD CONSTRAINT `product_inventory_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_inventory` ADD CONSTRAINT `product_inventory_ibfk_2` FOREIGN KEY (`sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_inventory_hold` ADD CONSTRAINT `product_inventory_hold_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders`(`order_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_inventory_hold` ADD CONSTRAINT `product_inventory_hold_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_inventory_hold` ADD CONSTRAINT `product_inventory_hold_ibfk_3` FOREIGN KEY (`sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_media` ADD CONSTRAINT `product_media_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_media` ADD CONSTRAINT `product_media_ibfk_2` FOREIGN KEY (`sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_sku` ADD CONSTRAINT `product_sku_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `product_translations` ADD CONSTRAINT `product_translations_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `shopping_cart` ADD CONSTRAINT `shopping_cart_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `shopping_cart_item` ADD CONSTRAINT `shopping_cart_item_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE `shopping_cart_item` ADD CONSTRAINT `shopping_cart_item_ibfk_2` FOREIGN KEY (`sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
