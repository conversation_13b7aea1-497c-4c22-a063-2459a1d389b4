import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import {
  CartCheckParameter,
  CartItemStrategyParameterType,
} from '@modules/shoppingCart/schema/strategy.schema';

import {
  boundaryProductQtyStrategy,
  checkCartExistStrategy,
  checkProductListedStrategy,
  checkRegionStrategy,
  checkStockStrategy,
  skuValidateStrategy,
} from '../implementations';
import { checkCartItemExistStrategy } from '../implementations/checkCartItemExists.strategy';

type ParameterType = CartCheckParameter & CartItemStrategyParameterType;
export const changeCartItemQuantityStrategyMap: CartCheckStrategy<ParameterType>[] = [
  checkCartItemExistStrategy,
  checkCartExistStrategy,
  skuValidateStrategy,
  checkProductListedStrategy,
  boundaryProductQtyStrategy,
  checkRegionStrategy,
  checkStockStrategy,
];
