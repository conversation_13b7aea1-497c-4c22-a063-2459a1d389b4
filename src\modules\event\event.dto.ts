import { z } from 'zod';
import { $Enums } from '@prisma/client';
import { PaginationDto } from '@/models/commonDto';

const EventStatusField = z.nativeEnum($Enums.EventStatus)

const EventDetailField = z.object({
    name: z.string(),
    description: z.string().optional(),
    regionCode: z.nativeEnum($Enums.RegionCode),
    timezone: z.nativeEnum($Enums.TimeZone),
    startDate: z.number().optional(),
    startTime: z.number().optional(),
    endDate: z.number().optional(),
    endTime: z.number().optional(),
    duration: z.number().optional(),
    venue: z.string().optional(),
    status: EventStatusField.default($Enums.EventStatus.DRAFT),
    parentId: z.number().optional()
})

export const CreateEventDto = EventDetailField
export type CreateEventDtoType = z.infer<typeof CreateEventDto>;
export const UpdateEventDto = EventDetailField.partial()
export type UpdateEventDtoType = z.infer<typeof UpdateEventDto>;
export const UpdateEventStatusDto = z.object({
    status: EventStatusField
})
export type UpdateEventStatusDtoType = z.infer<typeof UpdateEventStatusDto>;

export const UpdateEventSetting = z.object({
    content: z.string()
})
export type UpdateEventSettingDtoType = z.infer<typeof UpdateEventSetting>;

export const UpdateEventQaaDto = z.array(z.object({
    id: z.number().optional(),
    question: z.string(),
    answer: z.string()
}))
export type UpdateEventQaaDtoType = z.infer<typeof UpdateEventQaaDto>

export const UpdateEventTerms = z.object({
    content: z.string()
})
export type UpdateEventTermsDtoType = z.infer<typeof UpdateEventTerms>

export const ListEvent = z.object({
    ...PaginationDto.shape,
    search: z.string().optional(),
    region: z.array(z.nativeEnum($Enums.RegionCode)).optional(),
    status: z.nativeEnum($Enums.EventStatus).optional()
})
export type ListEventDtoType = z.infer<typeof ListEvent>

export const GetEvent = z.object({
    id: z.preprocess((a) => parseInt(z.string().parse(a)),
            z.number())
})
export type GetEventDtoType = z.infer<typeof GetEvent>