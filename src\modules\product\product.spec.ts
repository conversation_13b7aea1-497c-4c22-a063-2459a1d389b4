import supertest from 'supertest';
import app from '../../server';
import { StatusCodes } from 'http-status-codes';
import { User } from '@prisma/client';
import { initDb, cleanDb } from '../../config/tests';
import db from '@prismaClient';
import { generateUniqueID } from '../../utils/crypto';
import expect from 'expect';
import sinon from 'sinon';
import * as s3 from '../../utils/s3';
import { idToPaddedId } from '../../utils/conversion';

describe('Rooms', () => {
  let user: User;
  let accessToken: string;

  beforeEach(async () => {
    const data = await initDb();
    user = data.user;
    accessToken = data.accessToken;
  });

  afterEach(async () => {
    sinon.restore();
    await cleanDb();
  });

  const fakeS3Domain = 'https://s3.ap-southeast-1.amazonaws.com/';
  const fakeBucket = 'rooms';
  const getS3Url = (prefix: string, fileName: string) => {
    return `${fakeS3Domain}${fakeBucket}/${prefix}/${fileName}`;
  };

  const createDto = (): any => {
    return {
      roomName: generateUniqueID(),
      roomUrl: 'test-url',
      description: 'test description',
      visibleToPublic: true,
      openToAllMembers: true,
    };
  };

  describe('CRUD', () => {
    it('should create a room', async () => {
      await supertest(app)
        .post('/rooms')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(createDto())
        .expect(StatusCodes.CREATED);
    });

    it('should create a room with tags and member groups', async () => {
      const roomDto = createDto();
      const tag = await db.tag.create({
        data: {
          name: 'test tag',
          userId: user.id,
        },
      });

      const memberGroup = await db.memberGroup.create({
        data: {
          name: 'test member group',
          userId: user.id,
        },
      });

      await supertest(app)
        .post('/rooms')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          ...roomDto,
          tags: [
            {
              id: tag.id,
            },
          ],
          memberGroups: [
            {
              id: memberGroup.id,
            },
          ],
        })
        .expect(StatusCodes.CREATED);

      const createdRoom = await db.room.findFirstOrThrow({
        where: {
          roomName: roomDto.roomName,
        },
        include: {
          tags: true,
          memberGroups: true,
        },
      });

      expect(createdRoom.tags[0].id).toEqual(tag.id);
      expect(createdRoom.memberGroups[0].id).toEqual(memberGroup.id);
    });

    it('should create a room with media and move them to room prefix folder on S3', async () => {
      const roomDto = createDto();
      roomDto.logoUrl = getS3Url('tmp', 'logo.png');
      roomDto.thumbnailUrl = getS3Url('tmp', 'thumbnail.png');
      roomDto.bgMusicUrl = getS3Url('tmp', 'bg-music.mp3');

      const newLogoUrl = getS3Url('RM0000x', 'logo.png');
      const newThumbnailUrl = getS3Url('RM0000x', 'thumbnail.png');
      const newBgMusicUrl = getS3Url('RM0000x', 'bg-music.mp3');

      const moveTmpObjectSpy = sinon.stub(s3, 'moveTmpObject');

      moveTmpObjectSpy
        .withArgs(roomDto.logoUrl, sinon.match.any)
        .resolves(newLogoUrl)
        .withArgs(roomDto.thumbnailUrl, sinon.match.any)
        .resolves(newThumbnailUrl)
        .withArgs(roomDto.bgMusicUrl, sinon.match.any)
        .resolves(newBgMusicUrl);

      await supertest(app)
        .post('/rooms')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(roomDto)
        .expect(StatusCodes.CREATED);

      const createdRoom = await db.room.findFirstOrThrow({
        where: {
          roomName: roomDto.roomName,
        },
      });

      expect(createdRoom.logoUrl).toEqual(newLogoUrl);
      expect(createdRoom.thumbnailUrl).toEqual(newThumbnailUrl);
      expect(createdRoom.bgMusicUrl).toEqual(newBgMusicUrl);

      expect(moveTmpObjectSpy.calledWith(roomDto.logoUrl)).toBeTruthy();
      expect(moveTmpObjectSpy.calledWith(roomDto.thumbnailUrl)).toBeTruthy();
      expect(moveTmpObjectSpy.calledWith(roomDto.bgMusicUrl)).toBeTruthy();
    });

    it('should update a room', async () => {
      const roomDto = createDto();
      const room = await db.room.create({
        data: {
          ...roomDto,
          userId: user.id,
        },
      });
      await supertest(app)
        .put(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          ...roomDto,
          roomName: roomDto.name + 'updated name',
        })
        .expect(StatusCodes.OK);

      const updatedRoom = await db.room.findUniqueOrThrow({
        where: {
          id: room.id,
        },
      });

      expect(updatedRoom.roomName).toEqual(roomDto.name + 'updated name');
    });

    it('should update tags and member groups of a room', async () => {
      const roomDto = createDto();
      const tag1 = await db.tag.create({
        data: {
          name: 'tag 1',
          userId: user.id,
        },
      });
      const mg1 = await db.memberGroup.create({
        data: {
          name: 'mg 1',
          userId: user.id,
        },
      });
      const room = await db.room.create({
        data: {
          ...roomDto,
          userId: user.id,
          tags: {
            connect: [
              {
                id: tag1.id,
              },
            ],
          },
          memberGroups: {
            connect: [
              {
                id: mg1.id,
              },
            ],
          },
        },
      });

      const tag2 = await db.tag.create({
        data: {
          name: 'tag 2',
          userId: user.id,
        },
      });

      const mg2 = await db.memberGroup.create({
        data: {
          name: 'mg 2',
          userId: user.id,
        },
      });
      await supertest(app)
        .put(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          ...roomDto,
          tags: [
            {
              id: tag2.id,
            },
          ],
          memberGroups: [
            {
              id: mg2.id,
            },
          ],
        })
        .expect(StatusCodes.OK);

      const updatedRoom = await db.room.findUniqueOrThrow({
        where: {
          id: room.id,
        },
        include: {
          tags: true,
          memberGroups: true,
        },
      });

      expect(updatedRoom.tags.length).toEqual(1);
      expect(updatedRoom.memberGroups.length).toEqual(1);

      expect(updatedRoom.tags[0].id).toEqual(tag2.id);
      expect(updatedRoom.memberGroups[0].id).toEqual(mg2.id);
    });

    it('should move new photo URLs when updating', async () => {
      const roomDto = createDto();
      const room = await db.room.create({
        data: {
          ...roomDto,
          userId: user.id,
        },
      });

      const tmpLogoUrl = getS3Url('tmp', 'logo.png');
      const newLogoUrl = getS3Url('RM0000x', 'logo.png');

      const moveTmpObjectSpy = sinon.stub(s3, 'moveTmpObject');
      moveTmpObjectSpy.withArgs(tmpLogoUrl, sinon.match.any).resolves(newLogoUrl);

      await supertest(app)
        .put(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          ...roomDto,
          logoUrl: tmpLogoUrl,
        })
        .expect(StatusCodes.OK);

      const updatedRoom = await db.room.findUniqueOrThrow({
        where: {
          id: room.id,
        },
      });

      expect(updatedRoom.logoUrl).toEqual(newLogoUrl);
      expect(moveTmpObjectSpy.calledWith(tmpLogoUrl)).toBeTruthy();
    });

    it('should delete S3 photos if they are unset', async () => {
      const roomDto = createDto();
      const room = await db.room.create({
        data: {
          ...roomDto,
          userId: user.id,
          logoUrl: getS3Url('RM0000x', 'logo.png'),
          thumbnailUrl: getS3Url('RM0000x', 'thumbnail.png'),
          bgMusicUrl: getS3Url('RM0000x', 'bg_music.mp3'),
        },
      });

      const deleteS3ObjectSpy = sinon.stub(s3, 'deleteObjectByUrl');

      await supertest(app)
        .put(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          ...roomDto,
          logoUrl: '',
          thumbnailUrl: undefined,
          bgMusicUrl: null,
        })
        .expect(StatusCodes.OK);

      const updatedRoom = await db.room.findUniqueOrThrow({
        where: {
          id: room.id,
        },
      });

      expect(updatedRoom.logoUrl).toBeNull();
      expect(updatedRoom.thumbnailUrl).toBeNull();
      expect(updatedRoom.bgMusicUrl).toBeNull();

      expect(deleteS3ObjectSpy.calledWith(String(room.logoUrl))).toBeTruthy();
      expect(deleteS3ObjectSpy.calledWith(String(room.thumbnailUrl))).toBeTruthy();
      expect(deleteS3ObjectSpy.calledWith(String(room.bgMusicUrl))).toBeTruthy();
    });

    it('should not move photos if they do not change', async () => {
      let roomDto = createDto();
      roomDto = {
        ...roomDto,
        logoUrl: getS3Url('RM0000x', 'logo.png'),
        thumbnailUrl: getS3Url('RM0000x', 'thumbnail.png'),
        bgMusicUrl: getS3Url('RM0000x', 'bg_music.mp3'),
        userId: user.id,
      };
      const room = await db.room.create({
        data: roomDto,
      });

      const moveTmpObjectSpy = sinon.stub(s3, 'moveTmpObject');

      await supertest(app)
        .put(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(roomDto)
        .expect(StatusCodes.OK);

      const updatedRoom = await db.room.findUniqueOrThrow({
        where: {
          id: room.id,
        },
      });

      expect(updatedRoom.logoUrl).toEqual(roomDto.logoUrl);
      expect(updatedRoom.thumbnailUrl).toEqual(roomDto.thumbnailUrl);
      expect(updatedRoom.bgMusicUrl).toEqual(roomDto.bgMusicUrl);
      expect(moveTmpObjectSpy.called).toBeFalsy();
    });

    it('should get a room by id', async () => {
      const roomDto = createDto();
      const tag = await db.tag.create({
        data: {
          name: 'tag',
          userId: user.id,
        },
      });
      const mg = await db.memberGroup.create({
        data: {
          name: 'mg',
          userId: user.id,
        },
      });
      const room = await db.room.create({
        data: {
          ...roomDto,
          userId: user.id,
          tags: {
            connect: [{ id: tag.id }],
          },
          memberGroups: {
            connect: [{ id: mg.id }],
          },
        },
      });

      const response = await supertest(app)
        .get(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(StatusCodes.OK);

      const item = response.body;
      expect(item.id).toEqual(room.id);
      expect(item.roomId).toEqual(idToPaddedId('RM', room.id));
      expect(item.tags).toBeInstanceOf(Array);
      expect(item.tags[0].id).toEqual(tag.id);
      expect(item.tags[0].name).toEqual(tag.name);
      expect(item.memberGroups).toBeInstanceOf(Array);
      expect(item.memberGroups[0].id).toEqual(mg.id);
      expect(item.memberGroups[0].name).toEqual(mg.name);
    });

    it('should delete a room', async () => {
      const roomDto = createDto();
      const room = await db.room.create({
        data: {
          ...roomDto,
          userId: user.id,
        },
      });

      await supertest(app)
        .delete(`/rooms/${room.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(StatusCodes.OK);

      const deletedRoom = await db.room.findUnique({
        where: {
          id: room.id,
        },
      });
      expect(deletedRoom).toBeNull();
    });

    it('should get rooms', async () => {
      const room = await db.room.create({
        data: {
          ...createDto(),
          userId: user.id,
        },
      });

      const response = await supertest(app)
        .get('/rooms')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(StatusCodes.OK);

      const item = response.body.items[0];
      expect(item.id).toEqual(room.id);
      expect(item.roomId).toEqual(idToPaddedId('RM', room.id));
      expect(item.tags).toBeInstanceOf(Array);
      expect(item.memberGroups).toBeInstanceOf(Array);
    });
  });
});
