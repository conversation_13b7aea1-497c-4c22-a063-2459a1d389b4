import { Worker } from 'bullmq';
import { redis } from './redis';
import { NOTIFICATION_QUEUE_TOPIC } from './constant';
import db from '@prismaClient';

export class NotificationMQ {
    private queue: any;
    private worker: any;

    constructor() {
        this.initWorker()
        console.info("[setup] inited order worker")
    }

    initWorker() {
        this.worker = new Worker(
            'notification',
            async job => {
                switch (job.name) {
                    case NOTIFICATION_QUEUE_TOPIC.ORDER_COMPLETED:
                        console.info(`[issuing ticket] >> order id: ${job.data?.ticket}`)
                        await this.orderCompleted(job.data.orderId)
                        break;
                    case NOTIFICATION_QUEUE_TOPIC.TICKET_GENERATED:
                        await this.ticketGenerated(job.data.orderId)
                        break;
                }
            },
            { connection: redis },
        );
    }

    async orderCompleted(orderId: number) {
        const orderRecord = await db.orders.findUnique({
            select: {
                User: {
                    select: {
                        email: true
                    }
                },
                order_items: {
                    select: {
                        products: {
                            select: {
                                product_sku: true,
                                product_translations: true
                            }
                        },
                        ticket_type: true
                    }
                }
            },
            where: {
                order_id: orderId
            }
        })

    }

    async ticketGenerated(orderId: number) {

    }
}