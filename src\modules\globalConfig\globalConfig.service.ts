import db from '@prismaClient';

import { GlobalConfigGroup } from './enum/globalConfig';

interface GetGlobalConfig {
  (configGroup: GlobalConfigGroup, configKey: string): Promise<string | undefined>;
}

export const getShippingGlobalConfig = (configKey: string) => {
  return getGlobalConfig(GlobalConfigGroup.Shipping, configKey);
};

const getGlobalConfig: GetGlobalConfig = async (configGroup, configKey) => {
  const data = await db.global_config.findUnique({
    select: { config_value: true },
    where: {
      config_group_config_key: {
        config_group: configGroup,
        config_key: configKey,
      },
    },
  });

  return data?.config_value;
};
