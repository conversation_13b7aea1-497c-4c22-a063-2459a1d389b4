/*
  Warnings:

  - You are about to drop the column `name` on the `collections` table. All the data in the column will be lost.
  - Added the required column `name_en` to the `collections` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_tc` to the `collections` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name_ts` to the `collections` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `collections` DROP COLUMN `name`,
    ADD COLUMN `name_en` VARCHAR(255) NOT NULL,
    ADD COLUMN `name_tc` VARCHAR(255) NOT NULL,
    ADD COLUMN `name_ts` VARCHAR(255) NOT NULL;
