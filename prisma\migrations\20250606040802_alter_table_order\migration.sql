/*
  Warnings:

  - You are about to drop the column `order_idempotency_key` on the `orders` table. All the data in the column will be lost.
  - You are about to alter the column `type` on the `orders` table. The data in that column could be lost. The data in that column will be cast from `TinyInt` to `Enum(EnumId(6))`.
  - You are about to drop the column `is_checked_out` on the `shopping_cart` table. All the data in the column will be lost.
  - You are about to drop the `shopping_cart_checkout_idempotency_token` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropIndex
DROP INDEX `uk_order_idempotency_key` ON `orders`;

-- DropIndex
DROP INDEX `idx_uid_cart_check` ON `shopping_cart`;

-- AlterTable
ALTER TABLE `orders` DROP COLUMN `order_idempotency_key`,
    MODIFY `order_no` VARCHAR(50) NOT NULL,
    MODIFY `type` ENUM('product', 'ticket') NOT NULL,
    MODIFY `coupon_code` VARCHAR(50) NULL,
    MODIFY `coupon_discount` DECIMAL(12, 2) NULL,
    MODIFY `admin_note` TEXT NULL,
    M<PERSON><PERSON>Y `customer_note` TEXT NULL,
    MODIFY `cancel_at` INTEGER NULL,
    MODIFY `complete_at` INTEGER NULL;

-- AlterTable
ALTER TABLE `shopping_cart` DROP COLUMN `is_checked_out`;

-- DropTable
DROP TABLE `shopping_cart_checkout_idempotency_token`;

-- CreateTable
CREATE TABLE `idempotency_keys` (
    `idempotency_key` VARCHAR(128) NOT NULL,
    `user_id` INTEGER NOT NULL,
    `status` ENUM('unused', 'used', 'expired', 'failed') NOT NULL,
    `http_method` VARCHAR(10) NOT NULL,
    `route_path` VARCHAR(255) NOT NULL,
    `request_hash` VARCHAR(64) NOT NULL,
    `response_code` SMALLINT NULL,
    `response_body` JSON NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `expires_at` INTEGER NOT NULL,

    INDEX `idx_user_id`(`user_id`),
    INDEX `idx_route`(`http_method`, `route_path`, `request_hash`),
    INDEX `idx_expires`(`expires_at`),
    PRIMARY KEY (`idempotency_key`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `idx_uid_cart_check` ON `shopping_cart`(`user_id`, `cart_type`);
