import { handleAsync, validateRequest } from '@middleware';
import { requestSchema } from '@shared/schema/request.schema';
import { sendSuccessResponse } from '@utils/apiResponse';

import * as authService from './auth.service';
import * as authSchema from './schema/auth.schema';

export const registerToken = [
  validateRequest(requestSchema.body(authSchema.tokenRegisterBodySchema)),
  handleAsync(async (req, res) => {
    const { token, userId, userEmail, userName } = req.validatedData
      ?.body as authSchema.TokenRegisterRequestType;

    await authService.registerToken({ token, userId, userEmail, userName });

    return sendSuccessResponse(res, {});
  }),
];
