/*
  Warnings:

  - You are about to drop the column `third_party_name` on the `ticket_type` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE `order_items` DROP FOREIGN KEY `order_items_ticket_section_id_fkey`;

-- DropForeignKey
ALTER TABLE `order_items` DROP FOREIGN KEY `order_items_ticket_type_id_fkey`;

-- AlterTable
ALTER TABLE `order_items` MODIFY `ticket_section_id` INTEGER NULL,
    MODIFY `ticket_type_id` INTEGER NULL;

-- AlterTable
ALTER TABLE `ticket_type` DROP COLUMN `third_party_name`,
    ADD COLUMN `third_party_id` INTEGER NULL;

-- CreateTable
CREATE TABLE `third_party` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `created_at` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ticket_type_id_fkey` FOREIGN KEY (`ticket_type_id`) REFERENCES `ticket_type`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `order_items` ADD CONSTRAINT `order_items_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_type` ADD CONSTRAINT `ticket_type_third_party_id_fkey` FOREIGN KEY (`third_party_id`) REFERENCES `third_party`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
