import { Prisma } from '@prisma/client';
import { BadRequestException } from '@errors';
import { default as prismaErrorCodes } from '@shared/constants/prismaErrorCodes';

type KnownError = Prisma.PrismaClientKnownRequestError;

export const prismaErrorHandler = (error: KnownError | unknown) => {
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    if (error.code === prismaErrorCodes.EMAIL_DUPLICATED_ERROR_CODE) {
      return new BadRequestException({
        prismaCode: error.code,
        meta: error.meta,
      });
    }
    if (error.code === prismaErrorCodes.NOT_FOUND) {
      return new BadRequestException({
        prismaCode: error.code,
        meta: error.toString(),
      });
    }
  }
};
