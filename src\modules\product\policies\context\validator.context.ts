import { StrategyMap, UseCase } from '@modules/product/enums/strategy.enum';
import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import {
  addProductStrategyMap,
  updateProductStatusStrategyMap,
  bachUpdateProductStatusStrategyMap,
} from '../strategy';

export class ValidatorContext {
  public static productValidateStrategy: StrategyMap<ProductCheckStrategyParameterType> = {
    [UseCase.addProduct]: addProductStrategyMap,
    [UseCase.updateProductStatus]: updateProductStatusStrategyMap,
    [UseCase.batchUpdateProductStatus]: bachUpdateProductStatusStrategyMap,
  };

  public static execute = async (useCase: UseCase, product: ProductCheckStrategyParameterType) => {
    if (Array.isArray(product)) {
      const strategies = product.flatMap((param) =>
        ValidatorContext.productValidateStrategy[useCase].map((strategy) => strategy(param)),
      );
      await Promise.all(strategies);
    } else {
      await Promise.all(
        ValidatorContext.productValidateStrategy[useCase].map((strategy) => strategy(product)),
      );
    }
  };
}
