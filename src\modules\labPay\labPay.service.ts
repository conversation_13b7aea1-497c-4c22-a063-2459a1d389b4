// import db from "@prismaClient";
// import { CURRENCY, extractCData, extractData, generateLabPayPreOrder, generateOrderNo, PAYMENT_PLATFORM, PAYMENT_TYPE, queryLabPayOrder, TXN_STATE, verifyLabPayPayment } from "../../utils/payment";
// import axios from "axios";
// import { json2xml, xml2json } from 'xml-js';
// import { logger } from "../../utils/logger";
// import { errorHandler } from "../../errorHandlers/generalErrorHandler";
// import { generateTimeNow } from "../../utils/common";
// import { copyObjectFromExternalS3, uploadS3File } from "../../utils/s3";
// import * as _ from "lodash";
// import { sendEmailWithAttachment } from "../../utils/mail";
// import { wss } from "../../utils/websocket";

// const {
//     TICKET_SYSTEM_API_KEY
// } = process.env

// const verify = async (sign: string, obj: any) => {
//     await verifyLabPayPayment(sign, obj)
// }

// const getTicketFromExternal = async (product: { product: any, quantity: number }, userId: number, orderId: number, tx: any) => {
//     const timenow = generateTimeNow()
//     const {
//         product: productInfo
//     } = product
//     if (!(
//         productInfo &&
//         productInfo.ticketConfig &&
//         productInfo.ticketConfig[0]
//     )) return undefined;
//     const {
//         throughTicketSystem,
//         eventId = null,
//         config
//     } = productInfo.ticketConfig[0]
//     if (throughTicketSystem === 3 && eventId) {
//         const asyncFunc = async () => {
//             const ticketInfo = await tx.ticket.create({
//                 data: {
//                     userId,
//                     productId: productInfo.id,
//                     orderId,
//                     status: 0,
//                     createdAt: timenow,
//                     updatedAt: timenow
//                 }
//             })

//             let data = '';
//             try {
//                 let ticketConfig = { eventId }
//                 if (config) {
//                     let parsedConfig = JSON.parse(config)
//                     ticketConfig = { ...ticketConfig, ...parsedConfig }
//                 }
//                 const result = await axios.post(`https://apis.ticket-generator.com/client/v1/ticket/url`, ticketConfig, {
//                     headers: {
//                         'X-API-KEY': TICKET_SYSTEM_API_KEY
//                     }
//                 })
//                 data = result.data
//                 logger.info("[ticketing system] >> ticket url: "+ data)
//                 // ensure the image can be captured
//                 await new Promise(resolve => setTimeout(resolve, 3000))
//                 const ticketImage = await axios.get(
//                     data, { responseType: 'arraybuffer' }
//                 )
//                 if (ticketImage.status === 304 || ticketImage.status === 200) {
//                     const fileName = `${ticketInfo.id}.jpg`
//                     logger.info(`[ticketing system] >> uploading file to s3`)
//                     const s3Url = await uploadS3File(
//                         `ticket/${userId}`,
//                         fileName,
//                         ticketImage.data,
//                         ticketImage.data.length,
//                         true
//                     )
//                     logger.info(`[ticketing system] >> uploaded file to s3 >> ${s3Url}`)
//                     await tx.ticket.update({
//                         data: {
//                             src: s3Url,
//                             status: 1
//                         },
//                         where: {
//                             id: ticketInfo.id
//                         }
//                     })
//                     return [{
//                         name: fileName,
//                         content: ticketImage.data
//                     }]
//                 }
//             } catch (err) {
//                 logger.error("[ticketing system error] >> " + err)
//             }
//             return undefined
//         }
//         const functions = Array.from(Array(product.quantity).keys()).map((item, idx) => asyncFunc())
//         const tickets = await Promise.all(functions)
//         return tickets
//     }
// }

// const updateOrder = async(orderNo: string, paymentPlatform: string, paymentMethod: string) => {
//     const timenow = generateTimeNow()
//     const order = await db.order.findFirst({
//         select: {
//             id: true,
//             orderNo: true,
//             amount: true,
//             userId: true,
//             paymentPlatform: true,
//             clientName: true,
//             clientEmail: true,
//             Products: {
//                 select: {
//                     id: true,
//                     sku: true,
//                     quantity: true,
//                     product: {
//                         include: {
//                             ticketConfig: true
//                         }
//                     }
//                 }
//             }
//         },
//         where: {
//             orderNo
//         }
//     })
//     if (
//         !order ||
//         order.paymentPlatform !== PAYMENT_PLATFORM[paymentPlatform]
//     ) throw 4304
//     const queryObject = queryLabPayOrder(orderNo, paymentPlatform)
//     const orderInfo = await axios.post(
//         "https://wapi.lab-pay.com/queryorder",
//         `<xml>${json2xml(JSON.stringify(queryObject), { compact: true, spaces: 4})}</xml>`,
//         {
//             headers: {
//                 'Content-Type': 'application/xml'
//             }
//         }
//     )
//     const {
//         txn_state: txnState,
//         trade_info: tradeInfo,
//         response_code: responseCode,
//         payment_wallet: wallet
//     } = orderInfo.data
//     logger.info("[lab pay query order] >> orderInfo data >> " + JSON.stringify(orderInfo.data))
//     if (responseCode !== "0000") {
//         logger.warn(`[lab pay query order] >> response code >> ${responseCode}`)
//         throw 4302
//     }
//     paymentMethod = 'unknown'
//     if (tradeInfo) {
//         try {
//             paymentMethod = JSON.parse(tradeInfo).payment_card_name
//         } catch(error) {
//             errorHandler(4305, 'LabPay /payment')
//         }
//     }
//     if (wallet) {
//         paymentMethod = wallet
//     }
//     const data = {
//         status: TXN_STATE[txnState] || 0,
//         transactionStatus: txnState.toLowerCase(),
//         paymentMethod: paymentMethod.toLowerCase(),
//         updatedAt: timenow
//     }
//     await db.$transaction(async(tx) => {
//         const targetOrder = await tx.order.update({
//             where: {
//                 id: order.id
//             },
//             data
//         })
//         const promoCodeRecords = await tx.promoCodeRecords.findMany({
//             where: {
//                 referenceOrderNo: targetOrder.id
//             }
//         })
//         if (promoCodeRecords.length > 0) {
//         if (TXN_STATE[txnState] !== 1) {
//                 // delete all promocode is not successed
//                 await Promise.all(promoCodeRecords.map((code) => tx.promoCodeRecords.delete({
//                     where: {
//                         id: code.id
//                     }
//                 })))
//             } else {
//                 // update the promocode is successed
//                 await Promise.all(promoCodeRecords.map((code) => tx.promoCodeRecords.update({
//                     data: {
//                         status: 'redeem'
//                     },
//                     where: {
//                         id: code.id
//                     }
//                 })))
//                 await Promise.all(promoCodeRecords.map((code: any) => tx.promoTypes.update({
//                     data: {
//                         currentQuantity: {
//                             decrement: 1
//                         }
//                     },
//                     where: {
//                         id: code.promoCodeId,
//                     }
//                 })))
//             }
//         }
//         // update inventory
//         if (order.Products && order.clientEmail) {
//             await Promise.all(order.Products.map((product) => {
//                 return tx.productWarehouse.updateMany({
//                     data: {
//                         quantity: {
//                             decrement: product.quantity
//                         }
//                     },
//                     where: {
//                         id: product.id,
//                         sku: product.sku
//                     }
//                 })
//             }))

//             const tickets = await Promise.all(order.Products.map((product) => {
//                 return getTicketFromExternal(product, order.userId, order.id, tx)
//             }))
//             if (tickets.filter(item => item).length > 0) {
//                 if (!order.clientEmail) {
//                     logger.error(`[Issue ticket error] >> orderId: ${order.id} >> clientEmail is empty`)
//                 }
//                 await sendEmailWithAttachment(
//                     order.clientEmail,
//                     `Thanks for Your Purchase from Hong Kong's Got Talent!`,
//                     `
//                         <div>
//                             <div style="display: flex; justify-content: space-between; height: 100px;">
//                                 <div style="margin: 5px;">
//                                     <img src="https://s3.ap-southeast-1.amazonaws.com/hkgt.fun-verse.io/left.png" width="100">
//                                 </div>
//                                 <div style="margin: 5px;">
//                                     <img src="https://s3.ap-southeast-1.amazonaws.com/hkgt.fun-verse.io/right.png" width="100">
//                                 </div>
//                             </div>
//                             <p>Hi ${order.clientName},</p>
//                             <p>
//                                 Thank you for your recent purchase from the <i>Hong Kong's Got Talent</i> store! \
//                                 We’re excited that you’ve chosen to support us and can’t wait for you to \
//                                 enjoy your [${order.Products.map((product) => product.product.name)}].
//                             </p>
//                             <p><b>Order Details:</b></p>
//                             <ul>
//                                 <li><b>Order Number: </b> ${order.orderNo}</li>
//                                 <li><b>Items: ${order.Products.map((product) => product.product.name).join(", ")}</b></li>
//                                 <li><b>Total: ${order.amount}</b></li>
//                             </ul>
//                             <br/>
//                             <p>
//                                 You’ll be able to <b>collect your purchase at the venue on the show date.</b> \
//                                 Just bring the <b>QR code</b> that we’ll provide to you.
//                             </p>
//                             <p>
//                                 If you have any questions, feel free to reach out to us at \
//                                 <a href="mailto:<EMAIL>"><EMAIL></a>, and we’ll be happy to help.
//                             </p>
//                             <p>Thanks again for supporting <i>Hong Kong's Got Talent</i>! We can’t wait to see you there!</p>

//                             <p><b>Transportation Information</b></p>
//                             <p>To make your visit as smooth as possible, here are the directions to reach ATV:</>
//                             <ol type="1">
//                                 <li>
//                                     <b>Starting Point:</b>
//                                     <br/>
//                                     From the <b>MTR East Rail Line - Tai Po Market Station</b>, exit via <b>Exit B</b>.
//                                 </li>
//                                 <li>
//                                     <b>Transportation:</b>
//                                     <br/>
//                                     Take the <b>20K minibus</b> from the Tai Po Market minibus terminus.
//                                 </li>
//                                 <li>
//                                     <b>Destination:</b>
//                                     <br/>
//                                     Get off at <b>Fung Yuen Road</b> and walk approximately <b>10 minutes</b> to reach ATV.
//                                 </li>
//                             </ol>
//                             <p>For additional clarity, please refer to the attached photo for detailed directions.</p>
//                             <p>
//                                 If you need further assistance, feel free to contact us at \
//                                 <a href="mailto:<EMAIL>"><EMAIL></a>.
//                             </p>
//                             <br/>
//                             <p>We look forward to welcoming you to ATV!</p>
//                             <p> Best regards,</p>
//                             <p><i>Hong Kong's Got Talent</i> Team</p>
//                         </div>
//                     `,
//                     (tickets).flat(Infinity).filter(data => data) as []
//                 )
//             }
//         }
//         wss.sendOrderMessage(order.orderNo, JSON.stringify({orderNo, success: true}))
//         return true
//     }, {
//         maxWait: 30000,
//         timeout: 60000
//     })
// }

// export default {
//     verify,
//     updateOrder
// }
