import dayjs from 'dayjs';

import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkSaleDateValidStrategy: Strategy = async (parameter: ParameterType) => {
  const { saleStartDate, saleEndDate } = parameter as CreateProductRequestType;

  const isAfter = dayjs.unix(saleEndDate).isAfter(dayjs.unix(saleStartDate));

  if (!isAfter) {
    throw new CustomErrorException(errorCodeTable.addProductInvalidSaleDate);
  }
};
