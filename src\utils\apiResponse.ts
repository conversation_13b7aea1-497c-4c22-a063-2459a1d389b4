import { Response } from 'express';
import { ErrorApiResponse, SuccessApiResponse } from '@shared/types/response.type';
import { ErrorApiResponseSchema, SuccessApiResponseSchema } from '@shared/schema/response.schema';
import { HttpException } from '@/errors';

// Send success response
export const sendSuccessResponse = <T>(res: Response, data: T): Response<SuccessApiResponse<T>> => {
  const response: SuccessApiResponse<T> = {
    success: true,
    data,
  };

  // validate
  const validatedResponse = SuccessApiResponseSchema.parse(response);

  return res.status(200).json(validatedResponse);
};

// Send error response
export const sendErrorResponse = (
  res: Response,
  statusCode: number = 400,
  err: HttpException,
): Response<ErrorApiResponse> => {
  const response: ErrorApiResponse = err.toResponse();

  // validate
  const validatedResponse = ErrorApiResponseSchema.parse(response);

  return res.status(statusCode).json(validatedResponse);
};
