import { Router } from 'express';
import * as eventController from '@modules/event/event.controller';

const router = Router();

// admin
router.post('/', eventController.createEvent);
router.put('/:id', eventController.updateEvent);
router.put('/status/:id', eventController.updateEventStatus);
router.put('/media/:id', eventController.updateEventMedia);
router.put('/setting/:id', eventController.updateEventSetting);
router.put('/qaa/:id', eventController.updateEventQaa);
router.put('/terms/:id', eventController.updateEventTerms);

export default router;
