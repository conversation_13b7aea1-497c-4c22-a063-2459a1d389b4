import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import { CartCheckStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';

import { checkCartExistStrategy } from '../implementations';

type ParameterType = CartCheckStrategyParameterType;
export const clearCartStrategyMap: CartCheckStrategy<ParameterType>[] = [checkCartExistStrategy];
