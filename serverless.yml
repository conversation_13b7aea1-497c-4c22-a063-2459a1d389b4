# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: hieundx
# "service" is the name of this project. This will also be added to your AWS resource names.
service: funverse-api

provider:
  name: aws
  runtime: nodejs20.x
  region: ap-southeast-1

functions:
  api:
    name: api
    handler: dist/handler.handler
    events:
      - http:
          path: '{proxy+}'
          method: any
          cors: true

custom:
  dotenv:
    path: .env.prod-dev

plugins:
  - serverless-dotenv-plugin
  - serverless-plugin-common-excludes
  - serverless-plugin-include-dependencies

package:
  patterns:
    - 'node_modules/.prisma/**'
    - '!node_modules/prisma/libquery_engine-*'
    - '!node_modules/@prisma/engines/**'
    - '!node_modules/.cache/prisma/**'
    - '!node_modules/aws-sdk/**'
