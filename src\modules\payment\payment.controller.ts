import { Request, Response } from 'express';

import { CustomErrorException, errorCodeTable } from '@/errors';
import { handleAsync, validateRequest } from '@/middleware';
import { logger } from '@/utils/logger';
import * as orderService from '@modules/order/order.service';
import { requestSchema } from '@/shared/schema/request.schema';
import { sendSuccessResponse } from '@/utils/apiResponse';

import {
  LabPayNotificationBodySchema,
  retrieveLabPayOrderQuerySchema,
  RetrieveLabPayOrderQueryType,
} from './schema/payment.schema';
import * as paymentService from './payment.service';

export const retrieveLabPayNotification = [
  handleAsync(async (req: Request, res: Response) => {
    const { xml } = req.body;

    const notificationBody = Object.entries<string[]>(xml).reduce<Record<string, string>>(
      (a, [key, value]) => ({ ...a, [key]: value[0] }),
      {},
    );
    logger.info(`[LabPay notification XML body] >> ${JSON.stringify(notificationBody)}`);

    const parseOrderInfo = LabPayNotificationBodySchema.safeParse(notificationBody);
    if (!parseOrderInfo.success) {
      throw new CustomErrorException(
        errorCodeTable.notificationLabPayInvalidBody,
        parseOrderInfo.error,
      );
    }
    const { sign, ...orderInfo } = parseOrderInfo.data;
    await paymentService.verifyLabPayPayment(sign ?? '', orderInfo);
    await orderService.updateOrderByLabPayNotification(orderInfo);

    return res.status(200).send('success');
  }),
];

export const retrieveLabPayOrder = [
  validateRequest(requestSchema.query(retrieveLabPayOrderQuerySchema)),
  handleAsync(async (req, res) => {
    const { orderId, language } = req.validatedData.query as RetrieveLabPayOrderQueryType;

    const result = await paymentService.postQueryOrderToLabPay(orderId);

    return sendSuccessResponse(res, result);
  }),
];
