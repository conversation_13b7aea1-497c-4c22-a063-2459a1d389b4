import { CustomErrorException, errorCodeTable } from '@errors';
import { CartItemStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';

type ParameterType = CartItemStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const boundaryProductQtyStrategy: Strategy = async (parameter: ParameterType) => {
  const quantity = parameter?.quantity ?? 1;

  if (quantity <= 0 || quantity > 100) {
    throw new CustomErrorException(errorCodeTable.cartAddItemOverflowQty);
  }
};
