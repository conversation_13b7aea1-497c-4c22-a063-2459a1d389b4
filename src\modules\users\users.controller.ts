import { Router } from 'express';
import handleAsync from '../../middleware/async';
import usersService, { readFields, writeFields } from './users.service';
import jwtMiddleware from '../../middleware/jwt';
import { userByIdMiddleware } from './users.middleware';
import _ from 'lodash';
import { IdDto, JwtDto } from '../../models/commonDto';
import { GetOneDto, UpdateDto } from './users.dto';

const usersRouter = Router();

// TODO: check with FE and remove
usersRouter.get(
  '/:id',
  [jwtMiddleware],
  handleAsync(async (req, res) => {
    const { id } = req.params;
    const user = GetOneDto.parse(await usersService.findOneBy({ id: Number(id) }));

    res.send(user);
  }),
);

usersRouter.get(
  '/',
  jwtMiddleware,
  handleAsync(async (req, res) => {
    const { sub } = JwtDto.parse(req.user);
    const user = GetOneDto.parse(await usersService.findOneBy({ id: sub }));

    res.send(user);
  }),
);

// TODO: check with FE and remove
usersRouter.put(
  '/:id',
  [jwtMiddleware],
  handleAsync(async (req, res) => {
    const { id } = IdDto.parse(req.params);
    const data = UpdateDto.parse(req.body);

    await usersService.updateById(id, data);
    res.send();
  }),
);

usersRouter.put(
  '/',
  jwtMiddleware,
  handleAsync(async (req, res) => {
    const data = UpdateDto.parse(req.body);
    const { sub } = JwtDto.parse(req.user);

    const user = await usersService.updateById(sub, data);
    res.send(user);
  }),
);

export default usersRouter;
