import db from '../client/index';
import { generateTimeNow } from '@/utils/common';

const imageurl = [
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588901-image_0.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588903-image_1.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588905-image_2.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588907-image_3.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588909-image_4.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588910-image_5.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588912-image_6.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588914-image_7.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588916-image_8.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588918-image_9.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588919-image_10.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588921-image_11.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588923-image_12.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588925-image_13.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588927-image_14.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588929-image_15.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588931-image_16.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588933-image_17.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588934-image_18.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588937-image_19.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588939-image_20.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588941-image_21.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588942-image_22.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588944-image_23.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588946-image_24.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588948-image_25.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588950-image_26.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588951-image_27.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588953-image_28.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588955-image_29.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588957-image_30.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588959-image_31.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588961-image_32.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588963-image_33.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588965-image_34.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588967-image_35.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588969-image_36.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588971-image_37.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588972-image_38.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588974-image_39.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588976-image_40.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588978-image_41.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588980-image_42.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588982-image_43.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588983-image_44.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588985-image_45.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588987-image_46.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588989-image_47.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588991-image_48.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588992-image_49.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588994-image_50.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588996-image_51.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748588998-image_52.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589000-image_53.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589002-image_54.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589003-image_55.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589005-image_56.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589007-image_57.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589009-image_58.png',
  'https://s3.amazonaws.com/dev-web-incutix.fun-verse.io/product/1748589011-image_59.png',
];

export default async () => {
  let index = 0;
  const result: any[] = [];
  const now = generateTimeNow();

  const r1 = await db.product_sku.findMany({
    select: { product_id: true, sku_id: true, sku_code: true },
    orderBy: [{ product_id: 'asc' }, { sku_id: 'asc' }],
  });

  if (r1 && r1.length < 60) {
    await db.product_media.deleteMany({ where: { product_id: { in: [1, 2, 3] } } });
  }

  if (r1 && r1.length < 60) {
    for (const rr of r1) {
      for (const item of [...Array(5).keys()]) {
        result.push({
          product_id: rr.product_id,
          sku_id: rr.sku_id,
          media_type: 'image',
          is_main: item === 0,
          url: imageurl[index],
          priority: item+1,
          is_active: true,
          created_at: now,
          updated_at: now,
        });
        index++;
      }
    }
    await db.product_media.createMany({
      data: result,
    });
  }

  console.log('[Prisma] Seed product insert progress.');
};
