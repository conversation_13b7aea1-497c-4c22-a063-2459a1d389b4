import db from '@prismaClient';
import { Prisma, PrismaClient } from '@prisma/client';

import {
  generateExpireAt,
  generateTimeNow,
  generateTimeNowWithFormat,
  parseDateWithFormat,
} from '@/utils/common';
import { PrismaTransaction } from '@shared/types/prisma.type';
import { Language, LanguageCode, SortOrder } from '@/shared/enums';
import { CustomErrorException, errorCodeTable } from '@/errors';

import * as productService from '@modules/product/product.service';
import * as idempotencyKeyService from '@modules/idempotencyKey/idempotencyKey.service';
import { LabPayNotificationBodyType } from '@modules/payment/schema/payment.schema';
import { PaymentTransactionState } from '@modules/payment/Enum/payment.enum';

import {
  OrderShippingStatus,
  OrderShippingType,
  OrderStatus,
  OrderType,
  PrismaOrderShippingStatus,
  PrismaOrderShippingType,
  PrismaOrderStatus,
  PrismaOrderType,
} from './enum/order.enum';
import { adaptOrderStatus } from './adapters/orderStatus.adapter';
import {
  CreateProductOrderSchemaType,
  GetOrderByIdResponseType,
  OrderPaymentBodySchemaType,
  UpdateOrderShippingBodyType,
} from './schema/order.schema';
import { getShippingGlobalConfig } from '../globalConfig/globalConfig.service';
import { ValidatorContext } from './policies/context/validator.context';
import { UseCase } from './enum/useCase.enum';
import { ServiceQueue } from '../mq';
import { NOTIFICATION_QUEUE_TOPIC } from '../mq/constant';

// PD/TK + random(3) + dateFormat(YYYYMMDDHHmmssSSS) + sequence(4),
const generateNewOrderSN = async (order: CreateProductOrderSchemaType) => {
  const newOrderType = adaptOrderStatus(order.cartType);

  const findOrdersByType = await db.orders.findFirst({
    select: { order_no: true },
    where: {
      type: adaptOrderStatus(order.cartType),
      order_no: { contains: generateTimeNowWithFormat('YYYYMMDD') },
    },
    orderBy: { created_at: 'desc' },
  });
  const { order_no: latestOrderNo } = findOrdersByType ?? {};

  const orderSnTypeTagMapper: Record<OrderType, string> = {
    [PrismaOrderType.product]: 'PD',
    [PrismaOrderType.ticket]: 'TK',
  };
  const newOrderSnTimeStamp = generateTimeNowWithFormat('YYYYMMDDHHmmssSSS');
  const newOrderSnSequence = (Number(latestOrderNo?.slice(-4) ?? 0) + 1)
    .toString()
    .padStart(4, '0');

  return `${orderSnTypeTagMapper[newOrderType]}${newOrderSnTimeStamp}${newOrderSnSequence}`;
};

const calculateOrderSummary = async (orderId: number, prisma: PrismaTransaction = db) => {
  const findOrder = await prisma.orders.findUnique({
    select: {
      total_amount: true,
      sub_total: true,
      shipping_fee: true,
      tax_amount: true,
      discount_amount: true,
      order_shipping: {
        select: { shipping_type: true },
      },
    },
    where: { order_id: orderId },
  });

  if (!findOrder) {
    throw new CustomErrorException(errorCodeTable.getOrderOrderIdNotExists);
  }

  // // NOTE: Calculate shipping fee
  const getShippingFee = async (shippingType?: OrderShippingType): Promise<number> => {
    if (shippingType === PrismaOrderShippingType.express) {
      const expressShippingFee = await getShippingGlobalConfig('express_shipping_fee');
      // Default 30 if the db have not store this value
      return Number(expressShippingFee ?? 0);
    }
    return 0;
  };
  const shippingFee = await getShippingFee(findOrder.order_shipping?.shipping_type);

  // TODO: Calculate coupon fee

  // TODO: Calculate tax fee

  // NOTE: Sum total amount
  const subTotal = Number(findOrder?.sub_total);
  const sumTotalAmount = Number((subTotal + shippingFee).toFixed(2));

  return {
    orderTotalAmount: sumTotalAmount,
    orderSubTotal: subTotal,
    orderShippingFee: shippingFee,
    orderTaxAmount: Number(findOrder?.tax_amount),
    orderDiscountAmount: Number(findOrder?.discount_amount),
  };
};

export const createProductOrder = async (
  prisma: PrismaTransaction,
  order: CreateProductOrderSchemaType,
) => {
  const now = generateTimeNow();
  const expire = generateExpireAt(10, 'minute');
  const newOrderSn = await generateNewOrderSN(order);
  const orderItems = order.orderItems;

  const createOrderResult = await prisma.orders.create({
    select: {
      order_id: true,
      order_items: {
        select: { product_id: true, sku_id: true, quantity: true },
      },
    },
    data: {
      order_no: newOrderSn,
      region: order.region ?? '',
      status: 'inProgress',
      type: 'product',
      total_amount: order.totalAmount,
      sub_total: order.totalAmount,
      shipping_fee: order.shippingFee ?? 0,
      tax_amount: order.taxAmount ?? 0,
      discount_amount: order.discountAmount,
      coupon_code: order.couponCode,
      coupon_discount: order.couponDiscount,
      currency: order.currency,
      admin_note: order.adminNote,
      customer_note: order.customerNote,
      created_at: now,
      updated_at: now,
      expire_at: order.expireAt ?? expire,
      User: { connect: { id: order.userId } },
      order_items: {
        create: orderItems.map((orderItem) => ({
          product_id: orderItem.productId,
          sku_id: orderItem.skuId,
          quantity: orderItem.quantity,
          unit_price: orderItem.unitPrice,
          original_price: orderItem.originalPrice,
          total_price: orderItem.totalPrice,
          discount_amount: orderItem.discountAmount,
          created_at: now,
          updated_at: now,
        })),
      },
    },
  });

  return {
    orderId: createOrderResult.order_id,
    orderItems: createOrderResult.order_items.map(
      ({ product_id: productId, sku_id: skuId, quantity }) => ({
        productId: productId ?? 0,
        skuId: skuId ?? 0,
        quantity,
      }),
    ),
  };
};

export const retrieveOrderById = async (orderId: number, language: Language) => {
  const order = await db.orders.findUnique({
    select: {
      order_id: true,
      order_no: true,
      user_id: true,
      type: true,
      region: true,
      status: true,
      admin_note: true,
      customer_note: true,
      expire_at: true,
      total_amount: true,
      sub_total: true,
      shipping_fee: true,
      tax_amount: true,
      discount_amount: true,
      order_contact_name: true,
      order_contact_email: true,
      order_contact_country_code: true,
      order_contact_tel: true,
      currency: true,
      order_shipping: {
        select: {
          shipping_type: true,
          consignee: true,
          country_code: true,
          contract_no: true,
          address: true,
        },
      },
      order_billing_name: true,
      order_billing_address: true,
      order_billing_country_code: true,
      order_billing_tel: true,
      order_items: {
        select: {
          order_item_id: true,
          product_id: true,
          sku_id: true,
          unit_price: true,
          original_price: true,
          quantity: true,
          total_price: true,
          products: {
            select: {
              product_id: true,
              event_id: true,
              event: {
                select: {
                  name: true,
                  name_en: true,
                },
              },
              product_sku: {
                select: {
                  sku_id: true,
                  original_price: true,
                  sale_price: true,
                  product_media: {
                    select: { url: true },
                    where: {
                      is_main: true,
                      media_type: 'image',
                      is_active: true,
                    },
                  },
                  product_sku_attributes: {
                    select: {
                      product_attributes: {
                        select: {
                          product_attribute_categories: {
                            select: {
                              product_attribute_translation: {
                                select: { name: true, language_code: true },
                                where: { language_code: { in: [language, 'EN'] } },
                              },
                            },
                          },
                          product_attribute_values: {
                            select: {
                              product_attribute_translation: {
                                select: { name: true, language_code: true },
                                where: { language_code: { in: [language, 'EN'] } },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              product_translations: {
                select: { name: true, language_code: true },
                where: { language_code: { in: [language, 'EN'] } },
              },
            },
          },
        },
      },
    },
    // TODO: expired order
    where: { order_id: orderId },
  });

  if (!order) {
    throw new CustomErrorException(errorCodeTable.getOrderOrderIdNotExists);
  }

  const orderItems = order?.order_items;
  const orderTotalQuantity = orderItems?.reduce((a, v) => a + v.quantity, 0);
  const orderEvents = orderItems?.map(({ products }) => {
    return {
      event_id: products?.event_id,
      eventName: ([LanguageCode.TC, LanguageCode.TS] as string[]).includes(language)
        ? products?.event.name
        : products?.event.name_en,
    };
  });
  const orderItemProducts = orderItems?.map((orderItem) => {
    const products = orderItem.products;
    const productSku = products?.product_sku.find((x) => x.sku_id === orderItem.sku_id);
    const productSKUAttribute = productSku?.product_sku_attributes ?? [];
    const skuAttribute = productService.skuAttributesMapper(productSKUAttribute);

    return {
      orderItemId: orderItem.order_item_id,
      // orderItemType: ,
      productId: orderItem.product_id,
      skuId: orderItem.sku_id,
      productName: products?.product_translations?.[0]?.name,
      thumbnail: productSku?.product_media?.[0]?.url,
      skuAttribute,
      price: {
        unitPrice: orderItem.unit_price.toNumber(),
        originalPrice: orderItem.original_price.toNumber(),
        totalPrice: orderItem.total_price.toNumber(),
      },
      quantity: orderItem.quantity,
    };
  });
  const orderContact = {
    name: order?.order_contact_name ?? '',
    email: order?.order_contact_email ?? '',
    countryCode: order?.order_contact_country_code ?? '',
    tel: order?.order_contact_tel ?? '',
  };
  const orderShipping = {
    shippingType: order?.order_shipping?.shipping_type,
    name: order?.order_shipping?.consignee ?? '',
    countryCode: order?.order_shipping?.country_code ?? '',
    tel: order?.order_shipping?.contract_no ?? '',
    address: order?.order_shipping?.address ?? '',
  };
  const orderBilling = {
    name: order?.order_billing_name ?? '',
    countryCode: order?.order_billing_country_code ?? '',
    tel: order?.order_billing_tel ?? '',
    address: order?.order_billing_address ?? '',
  };

  // NOTE: Get payment method
  const findPaymentMethods = await db.payment_method.findMany({
    select: { payment_code: true, payment_platform: true, payment_type: true },
    where: { payment_region: order?.region, status: 'enabled' },
  });

  const paymentMethods = findPaymentMethods.map(
    ({
      payment_code: paymentCode,
      payment_platform: paymentPlatform,
      payment_type: paymentType,
    }) => ({
      paymentCode,
      paymentPlatform: paymentPlatform ?? '',
      paymentType,
    }),
  );

  // NOTE:
  const orderSummary = await calculateOrderSummary(order?.order_id);

  return {
    orderId: order?.order_id,
    userId: order?.user_id,
    orderType: order?.type,
    region: order?.region,
    currency: order?.currency,
    orderStatus: order?.status,
    orderAdminNote: order?.admin_note ?? '',
    orderUserNote: order?.customer_note ?? '',
    orderExpireAt: order?.expire_at,
    orderSummary: {
      orderTotalQuantity: orderTotalQuantity,
      ...orderSummary,
    },
    paymentMethod: paymentMethods,
    events: orderEvents,
    orderItems: orderItemProducts,
    orderContact: orderContact,
    orderShipping: orderShipping,
    orderBilling: orderBilling,
  };
};

export const getOrderPaymentIdempotencyKey = async (
  userId: number,
  order: OrderPaymentBodySchemaType,
) => {
  const orderPaymentRoute = {
    httpMethod: 'POST',
    routePath: '/api/public/v1/order/payment',
    requestHash: JSON.stringify({ userId: userId, orderId: order.orderId }),
  };

  return idempotencyKeyService.getIdempotencyKey(userId, orderPaymentRoute);
};

export const updateProductOrder = async (userId: number, order: OrderPaymentBodySchemaType) => {
  const now = generateTimeNow();

  // TODO: Combine as validator
  const findOrder = await db.orders.findUnique({
    select: { status: true, order_shipping: { select: { shipping_status: true } } },
    where: { order_id: order.orderId, user_id: userId },
  });

  if (!findOrder) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingOrderIdNotExists);
  }
  if (findOrder?.status !== PrismaOrderStatus.inProgress) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingOrderIsComplete);
  }
  if (findOrder?.order_shipping?.shipping_status === PrismaOrderShippingStatus.delivered) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingShippingDelivered);
  }

  await db.$transaction(async (tx) => {
    await tx.orders.update({
      data: {
        order_contact_name: order.orderContact.name,
        order_contact_email: order.orderContact.email,
        order_contact_country_code: order.orderContact.countryCode,
        order_contact_tel: order.orderContact.tel,
        order_billing_name: order.orderBilling.name,
        order_billing_country_code: order.orderBilling.countryCode,
        order_billing_tel: order.orderBilling.tel,
        order_billing_address: order.orderBilling.address,
        payment_method: order.paymentMethod,
        payment_platform: order.paymentPlatform,
        updated_at: now,
      },
      where: { order_id: order.orderId, user_id: userId },
    });

    await updateOrderShipping(userId, order, tx);
  });
};

export const updateOrderShipping = async (
  userId: number,
  order: UpdateOrderShippingBodyType,
  prisma?: PrismaTransaction,
) => {
  const now = generateTimeNow();

  // TODO: Combine as validator
  const findOrder = await db.orders.findUnique({
    select: { status: true, order_shipping: { select: { shipping_status: true } } },
    where: { order_id: order.orderId, user_id: userId },
  });

  if (!findOrder) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingOrderIdNotExists);
  }
  if (findOrder?.status !== PrismaOrderStatus.inProgress) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingOrderIsComplete);
  }
  if (findOrder?.order_shipping?.shipping_status === PrismaOrderShippingStatus.delivered) {
    throw new CustomErrorException(errorCodeTable.updateOrderShippingShippingDelivered);
  }

  const runTransaction = async (tx: PrismaTransaction | PrismaClient) => {
    // order shipping update/create
    const body = {
      shipping_type: order.shippingType,
      consignee: order?.orderShipping?.name ?? null,
      country_code: order?.orderShipping?.countryCode ?? null,
      contract_no: order?.orderShipping?.tel ?? null,
      address: order?.orderShipping?.address ?? null,
      updated_at: now,
    };

    await tx.order_shipping.upsert({
      where: { order_id: order.orderId },
      update: body,
      create: {
        order_id: order.orderId,
        shipping_status: PrismaOrderShippingStatus.pending,
        created_at: now,
        ...body,
      },
    });

    // Calculate order summary
    const orderSummary = await calculateOrderSummary(order.orderId, tx);
    return tx.orders.update({
      data: {
        total_amount: orderSummary.orderTotalAmount,
        sub_total: orderSummary.orderSubTotal,
        shipping_fee: orderSummary.orderShippingFee,
        tax_amount: orderSummary.orderTaxAmount,
        discount_amount: orderSummary.orderDiscountAmount,
      },
      where: { order_id: order.orderId },
    });
  };

  if (!prisma) {
    await db.$transaction(runTransaction);
  } else {
    await runTransaction(prisma);
  }
};

export const updateOrderByLabPayNotification = async (data: LabPayNotificationBodyType) => {
  const now = generateTimeNow();

  const orderNo = data.merchant_order_no;
  const paymentCreateTime = parseDateWithFormat(data.time_create);
  const paymentEndTime = data.time_end ? parseDateWithFormat(data.time_end) : undefined;
  const paymentOrderAmount = Number(data.total_fee);
  const paymentTransactionState = data.txn_state;
  const paymentTransactionRefNo = data.txn_ref_no;
  const paymentReturnUrl = data.data;
  const paymentType = data.payment_type;
  const paymentTradeInfo = data.trade_info;

  if (paymentTransactionState === PaymentTransactionState.SUCCESS) {
    const findOrder = await db.orders.findUnique({
      select: {
        order_id: true,
        order_items: {
          select: { product_id: true, sku_id: true, quantity: true },
        },
      },
      where: { order_no: orderNo },
    });

    await db.$transaction(async (tx) => {
      for (const orderItem of findOrder?.order_items ?? []) {
        await productService.updateProductInventoryAfterOrderComplete(tx, {
          orderId: findOrder?.order_id ?? 0,
          productId: orderItem?.product_id ?? 0,
          skuId: orderItem?.sku_id ?? 0,
          quantity: orderItem.quantity,
        });
      }

      await tx.orders.update({
        data: {
          status: PrismaOrderStatus.complete,
          labpay_create_time: paymentCreateTime,
          labpay_end_time: paymentEndTime,
          labpay_order_amount: paymentOrderAmount,
          labpay_txn_state: paymentTransactionState,
          labpay_txn_ref_no: paymentTransactionRefNo,
          labpay_return_url: paymentReturnUrl,
          labpay_type: paymentType,
          labpay_trade_info: paymentTradeInfo,
          updated_at: now,
          complete_at: now,
        },
        where: { order_no: orderNo },
      });

      if (findOrder?.order_id) {
        ServiceQueue.orderQueue.add(NOTIFICATION_QUEUE_TOPIC.ORDER_COMPLETED, { orderId: findOrder.order_id })
      }
    });
  }
};

export const retrieveOrderList: (param: {
  page: number;
  size: number;
  language: Language;
  sort?: string;
  sortOrder?: SortOrder;
  orderNo?: string;
}) => Promise<{ result: any[]; count: number }> = async (param) => {
  const orderBy: Prisma.ordersOrderByWithRelationInput[] = [
    ...(param.sort ? [{ [param.sort]: SortOrder }] : []),
    { order_id: 'asc' },
  ];
  const where: Prisma.ordersWhereInput = param.orderNo
    ? { order_no: { contains: param.orderNo } }
    : {};

  const findOrders = await db.orders.findMany({
    skip: Math.max(0, param.page - 1) * param.size,
    take: Number(param.size),
    orderBy,
    where,
    select: {
      order_id: true,
      status: true,
      order_no: true,
      User: {
        select: { email: true, name: true },
      },
      total_amount: true,
      currency: true,
      order_shipping: {
        select: { shipping_type: true, shipping_status: true },
      },
      created_at: true,
    },
  });

  const result = findOrders.map((findOrder) => {
    return {
      orderId: findOrder.order_id,
      orderNo: findOrder.order_no,
      orderStatus: findOrder.status,
      userEmail: findOrder.User.email,
      userName: findOrder.User.name ?? '',
      orderAmount: findOrder.total_amount.toNumber(),
      currency: findOrder.currency,
      shippingType: findOrder.order_shipping?.shipping_type,
      shippingStatus: findOrder.order_shipping?.shipping_status,
      create_at: findOrder.created_at,
    };
  });

  const count = result.length;

  return { result, count: count };
};

type OrderExtraInfo = {
  orderNo: string;
  txnRefNo: string;
  paymentMethod: string;
  paymentPlatform: string;
  shippingStatus: OrderShippingStatus;
  createAt: number;
};

export const handleSaleOrderInfo = (
  orderInfo: GetOrderByIdResponseType,
  extraInfo: OrderExtraInfo,
) => {
  // NOTE: omit(paymentMethod)
  const { paymentMethod, orderShipping, ...restOrderInfo } = orderInfo;
  const { shippingStatus, ...restOrderExtraInfo } = extraInfo;

  return {
    orderShipping: {
      ...orderShipping,
      shippingStatus,
    },
    ...restOrderInfo,
    ...restOrderExtraInfo,
  };
};

export const retrieveOrderExtraInfoById = async (orderId: number): Promise<OrderExtraInfo> => {
  const data = await db.orders.findUnique({
    select: {
      order_no: true,
      labpay_txn_ref_no: true,
      payment_method: true,
      payment_platform: true,
      created_at: true,
      order_shipping: {
        select: { shipping_status: true },
      },
    },
    where: { order_id: orderId },
  });

  if (!data) {
    throw new CustomErrorException(errorCodeTable.getOrderOrderIdNotExists);
  }

  return {
    orderNo: data.order_no,
    txnRefNo: data.labpay_txn_ref_no ?? '',
    paymentMethod: data.payment_method ?? '',
    paymentPlatform: data.payment_platform ?? '',
    shippingStatus: data.order_shipping?.shipping_status ?? PrismaOrderShippingStatus.pending,
    createAt: data.created_at,
  };
};

export const updateOrderShippingStatus = async (
  orderId: number,
  shippingStatus: OrderShippingStatus,
) => {
  await ValidatorContext.execute(UseCase.updateOrderShippingStatus, {
    orderId: orderId,
    shippingStatus: shippingStatus,
  });

  await db.$transaction(async (tx) => {
    await tx.order_shipping.update({
      data: { shipping_status: shippingStatus },
      where: { order_id: orderId },
    });
  });
};

export const batchUpdateOrderShippingStatus = async (
  orderIds: number[],
  shippingStatus: OrderShippingStatus,
) => {
  await ValidatorContext.execute(UseCase.batchUpdateOrderShippingStatus, {
    orderIds: orderIds,
    shippingStatus: shippingStatus,
  });

  await db.$transaction(async (tx) => {
    await tx.order_shipping.updateMany({
      data: { shipping_status: shippingStatus },
      where: { order_id: { in: orderIds } },
    });
  });
};

export const updateOrderStatus = async (orderId: number, orderStatus: OrderStatus) => {
  await ValidatorContext.execute(UseCase.updateOrderShippingStatus, {
    orderId: orderId,
    orderStatus,
  });

  await db.$transaction(async (tx) => {
    await tx.orders.update({
      data: { status: orderStatus },
      where: { order_id: orderId },
    });
  });
};

export const updateOrderAdminNote = async (orderId: number, remark: string) => {
  await ValidatorContext.execute(UseCase.updateOrderAdminNote, {
    orderId: orderId,
  });

  await db.$transaction(async (tx) => {
    await tx.orders.update({
      data: { admin_note: remark },
      where: { order_id: orderId },
    });
  });
};
