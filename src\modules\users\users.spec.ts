import supertest from 'supertest';
import app from '../../server';
import db from '../db';
import { Tag, User } from '@prisma/client';
import expect from 'expect';
import { StatusCodes } from 'http-status-codes';
import { initDb, cleanDb } from '../../config/tests';

describe('Users', () => {
  let user: User;
  let accessToken: string;

  beforeEach(async () => {
    const data = await initDb();
    user = data.user;
    accessToken = data.accessToken;
  });

  afterEach(async () => {
    await cleanDb();
  });

  it('should get user by id', async () => {
    const response = await supertest(app)
      .get('/users/' + user.id)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(StatusCodes.OK);

    expect(response.body.email).toEqual(user.email);
  });

  it('should get user', async () => {
    const response = await supertest(app)
      .get('/users')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(StatusCodes.OK);

    expect(response.body.email).toEqual(user.email);
  });

  it('should update user', async () => {
    await supertest(app)
      .put('/users')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        ...user,
        name: 'updated name',
      })
      .expect(StatusCodes.OK);

    const updatedUser = await db.user.findUnique({ where: { id: user.id } });
    expect(updatedUser?.name).toEqual('updated name');
  });
});
