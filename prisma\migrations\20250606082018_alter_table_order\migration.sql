/*
  Warnings:

  - You are about to alter the column `shipping_status` on the `order_shipping` table. The data in that column could be lost. The data in that column will be cast from `TinyInt` to `Enum(EnumId(3))`.
  - You are about to alter the column `shipping_type` on the `order_shipping` table. The data in that column could be lost. The data in that column will be cast from `TinyInt` to `Enum(EnumId(4))`.

*/
-- AlterTable
ALTER TABLE `order_shipping` MODIFY `shipping_status` ENUM('pending', 'delivered') NOT NULL,
    MODIFY `shipping_type` ENUM('storePickup', 'express') NOT NULL;

-- CreateIndex
CREATE INDEX `idx_order_id` ON `order_shipping`(`order_id`);



