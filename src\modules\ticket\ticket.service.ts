import db from '@prismaClient';
import { ExportTicketDtoType, ListTicketDtoType, TicketSectionType, TicketSettingType, TicketTypeType, TicketVariationType, UpdateTicketSettingType, UpdateTicketTypeType, UpdateTicketVariationType } from './ticket.dto';
import { generateTimeNow } from '@/utils/common';
import { $Enums, TicketType } from '@prisma/client';
import { generateJWT, generateUniqueID } from '@/utils/crypto';
import { ServiceQueue } from '../mq';
import { TICKET_QUEUE_TOPIC } from '../mq/constant';
import QRcode from 'qrcode';
import { S3_KEY, uploadS3File } from '@/utils/s3';
import AdmZip from "adm-zip";
import dayjs from 'dayjs';
import _ from 'lodash';

export enum SELLING_TYPE {
    INCUTIX=1,
    THIRD_PARTY=2
}

export const createTicketSetting = async (data: TicketSettingType) => {
    const timenow = generateTimeNow()
    const result = await db.ticket_setting.create({
        data: {
            event_id: data.eventId,
            sale_start_datetime: data.saleStartDatetime,
            sale_end_datetime: data.saleEndDatetime,
            currency: data.currency,
            sale_time_thershold: data.saleTimeThershold,
            created_at: timenow,
            updated_at: timenow,
        }
    })
    return result.id
}

export const updateTicketSetting = async (ticketSettingId: number, data: UpdateTicketSettingType) => {
    const timenow = generateTimeNow()
    const result = await db.ticket_setting.update({
        data: {
            sale_start_datetime: data.saleStartDatetime,
            sale_end_datetime: data.saleEndDatetime,
            currency: data.currency,
            sale_time_thershold: data.saleTimeThershold,
            updated_at: timenow
        },
        where: {
            id: ticketSettingId
        }
    })
    return result
}

export const createTicketSection = async (data: TicketSectionType) => {
    const {
        ticketSettingId,
        sections
    } = data
    const timenow = generateTimeNow()

    const allSpecificDates = sections.map((section) => section.date).filter(item => item)

    const ticketSetting  = await db.ticket_setting.findUnique({
        select: {
            event_info: {
                select: {
                    start_date: true,
                    end_date: true
                }
            }
        },
        where: {
            id: ticketSettingId
        }
    })

    const {
        start_date: startDate,
        end_date: endDate
    } = (ticketSetting?.event_info || {})

    await db.$transaction(async(tx) => {
        await Promise.all(sections.map(async (section) => {
            const {
                day,
                date,
                startTime,
                endTime,
                quantity,
                id,
                seq,
                deleteSection
            } = section

            let sectionId = id

            if (deleteSection) {
                await tx.ticket_section.delete({
                    where: {
                        id
                    }
                })
            } else if (id) {
                await tx.ticket_section.update({
                    data: {
                        day,
                        date,
                        start_time: startTime,
                        end_time: endTime,
                        seq,
                        quantity,
                        updated_at: timenow,
                    },
                    where: {
                        id
                    }
                })
            } else {
                const created = await tx.ticket_section.create({
                    data: {
                        ticket_setting_id: ticketSettingId,
                        day,
                        date,
                        start_time: startTime,
                        end_time: endTime,
                        seq,
                        quantity,
                        created_at: timenow,
                        updated_at: timenow,
                    }
                })
                sectionId = created.id
            }

            let allDays: number[] = []

            if (day) {
                const eventStart = dayjs.unix(Number(startDate)).startOf('day')
                const eventEnd = dayjs.unix(Number(endDate)).startOf('day')
                const dayDiff = eventEnd.diff(eventStart, 'day')
                allDays = Array(dayDiff).fill(0).map((_, idx) => {
                    const newDay = eventStart.add(idx, 'day')
                    console.log("included >> ", allSpecificDates.includes(newDay.unix()))
                    if (allSpecificDates.includes(newDay.unix())) return 0

                    if (newDay.day() + 1 === day) {
                        return newDay.unix()
                    }
                    return 0
                }).filter((item => item !== 0))
            }
            
            if (date) {
                allDays.push(date)
            }

            const existedTicketDateInventory = await tx.ticket_date_inventory.findMany({
                select: {
                    id: true
                },
                where: {
                    ticket_section_id: sectionId,
                    timestamp: {
                        notIn: allDays
                    }
                }
            })

            if (existedTicketDateInventory?.length > 0) {
                await tx.ticket_date_inventory.deleteMany({
                    where: {
                        id: {
                            in: existedTicketDateInventory.map((inventory) => inventory.id)
                        }
                    }
                })
            }

            await Promise.all(_.uniq(allDays).map((timestamp) => {
                return tx.ticket_date_inventory.upsert({
                    create: {
                        ticket_section_id: sectionId as number,
                        total: quantity,
                        timestamp,
                        available: quantity,
                        on_hold: 0,
                        created_at: timenow,
                        updated_at: timenow
                    },
                    update: {
                        total: quantity,
                        updated_at: timenow
                    },
                    where: {
                        timestamp_ticket_section_id: {
                            ticket_section_id: sectionId as number,
                            timestamp
                        }
                    }
                })
            }))
        }))
    })
}

// ticket variation
export const createTicketVariation = async (data: TicketVariationType) => {
    const timenow = generateTimeNow()
    const {
        value = [],
        timeSlot = []
    } = data

    let variationId = -1;
    await db.$transaction(async (tx) => {
        const variation = await tx.ticket_variation.create({
            data: {
                name: data.name,
                type: $Enums.VariationType.NORMAL,
                ticket_setting_id: data.ticketSettingId,
                created_at: timenow,
                updated_at: timenow
            }
        })

        variationId = variation.id

        await tx.ticket_variation_option.createMany({
            data: [
                ...value.map((val) => {
                    return {
                        ticket_variation_id: variationId,
                        value: val,
                        created_at: timenow,
                        updated_at: timenow,
                    }
               }),
               ...timeSlot.map((time) => {
                    return {
                        ticket_variation_id: variationId,
                        timeSlot: {
                            from: time.from,
                            to: time.to
                        },
                        created_at: timenow,
                        updated_at: timenow,
                    }
               })
            ]
        })
    })
    return variationId
}

export const updateTicketVariation = async (variationId: number, data: UpdateTicketVariationType) => {
    const timenow = generateTimeNow()
    await db.$transaction(async (tx) => {
        const {
            name,
            variations = [],
        } = data
        
        if (name) await tx.ticket_variation.update({ data: { name }, where: { id: variationId  } })

        const updateObject = variations.map((item) => {
            const {
                id,
                timeSlot = {},
                value = undefined,
                deleteVariation
            } = item
            let data;
            if (deleteVariation) data = { id, deleteVariation }
            else if (!id) data = { data: { ticket_variation_id: variationId, ...timeSlot, value, created_at: timenow, updated_at: timenow } }
            else data = { id, data: { ...timeSlot, value, updated_at: timenow } }
            return data
        }).filter(item => item) as { id: number, data?: any, deleteVariation?: boolean }[]
        console.log("updateObject >> ", updateObject)
        await Promise.all(updateObject.map((obj) => {
            if (obj.deleteVariation) return tx.ticket_variation_option.delete({ where: { id: obj.id } })
            if (!obj.id) return tx.ticket_variation_option.create({ data: { ...obj.data  } })
            return tx.ticket_variation_option.update({ data: obj.data, where: { id: obj.id } })
        }))
    })
}

// ticket type
export const createTicketType = async (data: TicketTypeType) => {
    const timenow = generateTimeNow()
    let typeId;
    await db.$transaction(async(tx) => {
        const {
            ticketSections,
            ticketVariation,
            ticketGift,
            ...ticketDetail
        } = data

        let third_party_id;
        if (
            ticketDetail?.sellingLocation === SELLING_TYPE.THIRD_PARTY &&
            ticketDetail.thirdPartyName
        ) {
            const found = await tx.third_party.findFirst({
                where: {
                    name: ticketDetail.thirdPartyName
                }
            })
            third_party_id = found?.id
            if (!found) {
                const {
                    id
                } = await tx.third_party.create({
                    data: {
                        name: ticketDetail.thirdPartyName,
                        created_at: timenow
                    }
                })
                third_party_id= id
            }
        }
        const { id: ticketTypeId } = await tx.ticket_type.create({
            data: {
                ticket_setting_id: ticketDetail.ticketSettingId,
                selling_type: ticketDetail.sellingLocation,
                third_party_id,
                multiply: ticketDetail.multiply || 1,
                name: ticketDetail.name,
                description: ticketDetail.description,
                is_all_day: ticketDetail.isAllDay,
                pre_sale_start_time: ticketDetail.preSaleStartTime,
                pre_sale_end_time: ticketDetail.preSaleEndTime,
                price: ticketDetail.price,
                price_unit: ticketDetail.priceUnit,
                allow_multiple_entry: ticketDetail.allowMultipleEntry,
                have_gift: ticketDetail.haveGift,
                created_at: timenow,
                updated_at: timenow,
            }
        })

        typeId = ticketTypeId

        const allSectionIds = await tx.ticket_section.findMany({
            select: {
                id: true
            },
            where: {
                OR: [{
                    day: {
                        in: ticketSections
                    }
                }, {
                    date: {
                        in: ticketSections
                    }
                }]
            }
        })

        
        await tx.ticket_type_to_section.createMany({
            data: allSectionIds.map((item) => {
                return {
                    ticket_type_id: ticketTypeId,
                    ticket_section_id: item.id
                }
            })
        })

        await tx.ticket_type_to_variation.createMany({
            data: ticketVariation.map((variationId) => {
                return {
                    ticket_type_id: ticketTypeId,
                    ticket_variation_id: variationId
                }
            })
        })

        await tx.ticket_to_gift.createMany({
            data: ticketGift.map((productId) => {
                return {
                    ticket_type_id: ticketTypeId,
                    product_id: productId
                }
            })
        })
    })

    return typeId
}

export const updateTicketType = async (id: number, data: UpdateTicketTypeType) => {
    const timenow = generateTimeNow()
    await db.$transaction(async(tx) => {
        const {
            ticketSections,
            ticketVariation,
            ticketGift,
            ...ticketDetail
        } = data

        let third_party_id;
        if (
            ticketDetail?.sellingLocation === SELLING_TYPE.THIRD_PARTY &&
            ticketDetail.thirdPartyName
        ) {
            const found = await tx.third_party.findFirst({
                where: {
                    name: ticketDetail.thirdPartyName
                }
            })
            third_party_id = found?.id
            if (!found) {
                const {
                    id
                } = await tx.third_party.create({
                    data: {
                        name: ticketDetail.thirdPartyName,
                        created_at: timenow
                    }
                })
                third_party_id= id
            }
        }

        await tx.ticket_type.update({
            data: {
                multiply: ticketDetail.multiply || 1,
                third_party_id,
                name: ticketDetail.name,
                description: ticketDetail.description,
                is_all_day: ticketDetail.isAllDay,
                pre_sale_start_time: ticketDetail.preSaleStartTime,
                pre_sale_end_time: ticketDetail.preSaleEndTime,
                price: ticketDetail.price,
                price_unit: ticketDetail.priceUnit,
                allow_multiple_entry: ticketDetail.allowMultipleEntry,
                have_gift: ticketDetail.haveGift,
                updated_at: timenow
            },
            where: {
                id
            }
        })

        if (ticketSections) {
            const allSectionIds = await tx.ticket_section.findMany({
                select: {
                    id: true
                },
                where: {
                    OR: [{
                        day: {
                            in: ticketSections
                        }
                    }, {
                        date: {
                            in: ticketSections
                        }
                    }]
                }
            })

            const ids = allSectionIds.map((item) => item.id)

            await tx.ticket_type_to_section.deleteMany({
                where: {
                    ticket_type_id: id,
                    ticket_section_id: {
                        notIn: ids
                    }
                }
            })

            await Promise.all(ids.map((sectionId) => {
                return tx.ticket_type_to_section.upsert({
                    create: {
                        ticket_type_id: id,
                        ticket_section_id: sectionId
                    },
                    update: {
                        ticket_type_id: id,
                        ticket_section_id: sectionId
                    },
                    where: {
                        ticket_type_id_ticket_section_id: {
                            ticket_type_id: id,
                            ticket_section_id: sectionId
                        }
                    }
                })
            }))
        }
        
        if (ticketVariation) {
            await tx.ticket_type_to_variation.deleteMany({
                where: {
                    ticket_type_id: id,
                    ticket_variation_id: {
                        notIn: ticketVariation
                    }
                }
            })
            await Promise.all(ticketVariation.map((variationId) => {
                return tx.ticket_type_to_variation.upsert({
                    create: {
                        ticket_type_id: id,
                        ticket_variation_id: variationId
                    },
                    update: {
                        ticket_type_id: id,
                        ticket_variation_id: variationId
                    },
                    where: {
                        ticket_type_id_ticket_variation_id: {
                            ticket_type_id: id,
                            ticket_variation_id: variationId
                        }
                    }
                })
            }))
        }

        if (ticketGift) {
            await tx.ticket_to_gift.deleteMany({
                where: {
                    ticket_type_id: id,
                    product_id: {
                        notIn: ticketGift
                    }
                }
            })
            await Promise.all(ticketGift.map((productId) => {
                return tx.ticket_to_gift.upsert({
                    create: {
                        ticket_type_id: id,
                        product_id: productId
                    },
                    update: {
                        ticket_type_id: id,
                        product_id: productId
                    },
                    where: {
                        ticket_type_id_product_id: {
                            ticket_type_id: id,
                            product_id: productId
                        }
                    }
                })
            }))
        }
    })
}

export const listUserTicket = async (userId: number, query: ListTicketDtoType) => {
    return await db.ticket.findMany({
        select: {
            ticket_type: {
                select: {
                    ticket_setting: {
                        select: {
                            event_info: {
                                select: {
                                    name: true,
                                    description: true,
                                    start_date: true,
                                    start_time: true,
                                    end_date: true,
                                    end_time: true,
                                    event_media: true
                                }
                            },
                            ticket_type: {
                                select: {
                                    name: true,
                                    description: true,
                                    is_all_day: true,
                                    price: true,
                                    price_unit: true,
                                    allow_multiple_entry: true,
                                    have_gift: true
                                }
                            }
                        }
                    },
                }
            },
            ticket_section: {
                select: {
                    day: true,
                    date: true,
                    start_time: true,
                    end_time: true,
                }
            }
        },
        where: {
            user_id: userId
        }
    })
}

export const getTicket = async (userId: number, ticketId: number) => {
    return await db.ticket.findFirst({
        select: {
            ticket_type: {
                select: {
                    ticket_to_gift: {
                        select: {
                            products: {
                                select: {
                                    product_translations: {
                                        select: {
                                            name: true,
                                            description: true
                                        }
                                    }
                                }
                            }
                        }
                    },
                    is_all_day: true,
                    allow_multiple_entry: true,
                }
            },
            ticket_section: {
                select: {
                    day: true,
                    date: true,
                    start_time: true,
                    end_time: true,
                }
            },
            entry_record: true,
            redeem_record: {
                select: {
                    products: {
                        select: {
                            product_translations: {
                                select: {
                                    name: true,
                                    description: true,
                                }
                            },
                        }
                    }
                }
            }
        },
        where: {
            user_id: userId,
            id: ticketId
        }
    })
}

export const exportTicket = async ({branch}: ExportTicketDtoType) => {
    const timenow = generateTimeNow();
    const file = new AdmZip()

    await db.$transaction(async(tx) => {
        await Promise.all(branch.map(async (item) => {
            const {
                quantity,
                ticketSectionId,
                ticketDateInventoryId,
                ticketTypeId
            } = item

            const inventoryData = await tx.ticket_date_inventory.findUnique({
                select: {
                    total: true
                },
                where: {
                    id: ticketDateInventoryId
                }
            })

            const total = inventoryData?.total || 0

            const foundDistribution = await tx.ticket_distribution.findUnique({
                where: {
                    ticket_type_id_ticket_date_inventory_id: {
                        ticket_type_id: ticketTypeId,
                        ticket_date_inventory_id: ticketDateInventoryId
                    }
                }
            })

            let ticketDistributionId = foundDistribution?.id

            if (!ticketDistributionId) {
                const {id} = await tx.ticket_distribution.create({
                    data: {
                        ticket_section_id: ticketSectionId,
                        ticket_type_id: ticketTypeId,
                        ticket_date_inventory_id: ticketDateInventoryId
                    }
                })
                ticketDistributionId = id
            }

            await tx.ticket_distribution_adjustment.create({
                data: {
                    amount: quantity,
                    before: total,
                    after: total - quantity,
                    created_at: timenow,
                    updated_at: timenow,
                    ticket_distribution_id: ticketDistributionId
                }
            })

            await tx.ticket_date_inventory.update({
                data: {
                    available: total - quantity
                },
                where: {
                    id: ticketDateInventoryId
                }
            })

            await Promise.all(Array(quantity).fill(0).map(async (_, idx) => {
                const qrcode = generateUniqueID()
                const {
                    id
                } = await tx.ticket.create({
                    data: {
                        qrcode,
                        created_at: timenow,
                        updated_at: timenow,
                        status: $Enums.TicketType.ISSUED,
                        ticket_section_id: ticketSectionId,
                        ticket_date_inventory_id: ticketDateInventoryId,
                        ticket_type_id: ticketTypeId,
                        branch_id: ticketDistributionId
                    }
                })

                const qrcodeJWT = generateJWT(qrcode)
                const bufferStr = await QRcode.toBuffer(qrcodeJWT)
                const fileName = `${id}_${timenow}_qrcode.png`

                file.addFile(fileName, bufferStr)

                const key = await uploadS3File(
                    S3_KEY.TICKET,
                    fileName,
                    bufferStr,
                    Buffer.byteLength(bufferStr),
                    true
                )

                await tx.ticket.update({
                    data: {
                        url: key,
                        updated_at: timenow,
                        status: $Enums.TicketType.ISSUED
                    },
                    where: {
                        id
                    }
                })
            }))

        }))
    })

    return {
        name: generateUniqueID(),
        file: file.toBuffer()
    };

}

export const issueTicket = async (orderId: number, userId: number) => {
  const timenow = generateTimeNow();

  const order = await db.orders.findUnique({
    select: {
        order_items: true
    },
    where: {
        order_id: orderId,
        user_id: userId,
    }
  })

  const expectedTicket = order?.order_items.filter(item => item.ticket_type_id)
  // skip issue ticket process
  if (!order || !expectedTicket || expectedTicket.length === 0)
    return;

  const ticketQuantity: { [ticketTypeId: string]: { [ticketSectionId: string]: number } } = {}
  const ticketOrderItem: { [ticketTypeId: string]: { [ticketSectionId: string]: number } } = {}
  const alreadyGeneratedTicket: { [ticketTypeId: string]: { [ticketSectionId: string]: number[] } } = {}
  const ticketRequiredToGenerate: { [ticketTypeId: string]: { [ticketSectionId: string]: number } } = {}

  expectedTicket.forEach((ticket) => {
    const {
        order_item_id: orderItemId,
        ticket_type_id: ticketTypeId,
        ticket_section_id: ticketSectionId,
        quantity
    } = ticket
    if (!ticketSectionId || !ticketTypeId) return;
    if (ticketSectionId && ticketTypeId && ticketQuantity[ticketSectionId]) {
        ticketQuantity[ticketSectionId][ticketTypeId] = quantity
        ticketOrderItem[ticketSectionId][ticketTypeId] = orderItemId
    } else {
        ticketQuantity[ticketSectionId] = {
            [ticketTypeId]: quantity
        }
        ticketOrderItem[ticketSectionId] = {
            [ticketTypeId]: orderItemId
        }
    }
  })

  const userTicketIssued = await db.ticket.findMany({
    where: {
        order_item_id: {
            in: expectedTicket.map((ticket) => ticket.order_item_id)
        }
    }
  })

  userTicketIssued.forEach((ticket) => {
    const {
        ticket_type_id,
        ticket_section_id,
        id
    } = ticket
    
    if (alreadyGeneratedTicket[ticket_type_id]) {
        alreadyGeneratedTicket[ticket_type_id][ticket_section_id].push(id)
    } else {
        alreadyGeneratedTicket[ticket_type_id] = {
            [ticket_section_id]: [id]
        }
    }
  })

  Object.keys(ticketQuantity).forEach((ticketTypeId) => {
    if (alreadyGeneratedTicket[ticketTypeId]) {
        // some tickets issued
        // check if any ticket missed
        Object.keys(ticketQuantity[ticketTypeId]).forEach((ticketSectionId) => {
            if (alreadyGeneratedTicket[ticketTypeId][ticketSectionId]) {
                if (
                    alreadyGeneratedTicket[ticketTypeId][ticketSectionId].length
                    != ticketQuantity[ticketTypeId][ticketSectionId]
                ) {
                    if (ticketRequiredToGenerate[ticketTypeId]) {
                        ticketRequiredToGenerate[ticketTypeId][ticketSectionId] = 
                            ticketQuantity[ticketTypeId][ticketSectionId]
                            - alreadyGeneratedTicket[ticketTypeId][ticketSectionId].length
                    } else {
                        ticketRequiredToGenerate[ticketTypeId] = {
                            [ticketSectionId]: ticketQuantity[ticketTypeId][ticketSectionId]
                        }
                    }
                }
            } else {
                if (ticketRequiredToGenerate[ticketTypeId]) {
                    ticketRequiredToGenerate[ticketTypeId][ticketSectionId] = 
                        ticketQuantity[ticketTypeId][ticketSectionId]
                } else {
                    ticketRequiredToGenerate[ticketTypeId] = {
                        [ticketSectionId]: ticketQuantity[ticketTypeId][ticketSectionId]
                    }
                }
            }
        })
    } else {
        // no ticket issued
        // generate all tickets
        ticketRequiredToGenerate[ticketTypeId] = ticketQuantity[ticketTypeId]
    }
  })

  try {
    await db.$transaction(async (tx) => {
        console.info("[insert issuing ticket] >> start")
        const insertObject = Object.keys(ticketRequiredToGenerate).map((typeKey) => {
            return Object.keys(ticketRequiredToGenerate[typeKey]).map((sectionKey) => {
                return [...Array(ticketRequiredToGenerate[typeKey][sectionKey])].map(() => {
                    return {
                            qrcode: generateUniqueID(),
                            created_at: timenow,
                            updated_at: timenow,
                            status: $Enums.TicketType.ISSUING,
                            user_id: userId,
                            ticket_section_id: Number(sectionKey),
                            ticket_type_id: Number(typeKey),
                            order_item_id: ticketOrderItem[typeKey][sectionKey]
                    }})
            })
        })

        // const ticketIds = await Promise.all(insertObject.flat(2).map((item) => db.ticket.create({
        //     data: item
        // })))

        // const { count } = await db.ticket.createMany({
        //     data: insertObject.flat(2)
        // })
        
        // console.info(`expected issue: ${insertObject.flat(2).length} >> total issued: ${count}`)
        // console.info("[insert issuing ticket] >> end")

        // ServiceQueue.ticketQueue.add(TICKET_QUEUE_TOPIC.ISSUE, { ticket: ticketIds })
    })
  } catch (error) {
    // TODO:: send email to notify relevant parties
    console.error("[insert ticket error] >> ", error)
  }
}

export const validateTicket = async (qrcode: string) => {
    return await db.ticket.findUnique({
        select: {
            status: true,
            updated_at: true,
            user_info: {
                select: {
                    name: true,
                    email: true
                }
            },
            ticket_type: {
                select: {
                    allow_multiple_entry: true,
                    have_gift: true,
                    is_all_day: true,
                    ticket_to_gift: {
                        select: {
                            products: true
                        }
                    },
                    ticket_setting: {
                        select: {
                            event_info: {
                                select: {
                                    name: true,
                                    description: true,
                                    venue: true,
                                }
                            }
                        }
                    }
                }
            },
            ticket_section: {
                select: {
                    day: true,
                    date: true,
                    start_time: true,
                    end_time: true
                }
            },
            entry_record: {
                select: {
                    created_at: true,
                    updated_at: true
                }
            },
            redeem_record: {
                select: {
                    created_at: true,
                    updated_at: true,
                    products: true
                }
            }
        },
        where: {
            qrcode
        }
    })
}