import { Request, Response } from 'express';
import { handleAsync, validateRequest, verifyIdempotencyToken } from '@middleware';
import { requestSchema } from '@shared/schema/request.schema';
import { UserType } from '@shared/types/user.type';
import { sendSuccessResponse } from '@utils/apiResponse';

import * as shoppingCartService from './shoppingCart.service';
import * as shoppingCartSchema from './schema/shoppingCart.schema';

export const getShoppingCart = [
  validateRequest(requestSchema.query(shoppingCartSchema.getShoppingCartQuerySchema)),
  handleAsync(async (req, res) => {
    const { user } = req;
    const { language } = req.validatedData?.query as shoppingCartSchema.GetShoppingCartRequestType;

    const result = await shoppingCartService.retrieveShoppingCartByUserId(user?.userId, language);

    const data = shoppingCartSchema.getShoppingCartResponseSchema.parse(result);

    return sendSuccessResponse(res, data);
  }),
];

export const getShoppingCartCount = [
  handleAsync(async (req, res) => {
    const { user } = req;

    const result = await shoppingCartService.retrieveShoppingCartCountByUserId(user?.userId);

    const data = shoppingCartSchema.getShoppingCartCountResponseSchema.parse(result);

    return sendSuccessResponse(res, data);
  }),
];

export const addShoppingCartItem = [
  validateRequest(requestSchema.body(shoppingCartSchema.addShoppingCartItemBodySchema)),
  handleAsync(async (req, res) => {
    const { user } = req;
    const addItem = req.validatedData?.body as shoppingCartSchema.AddShoppingCartItemRequestType;

    await shoppingCartService.addShoppingCartItem(user?.userId, addItem);

    return sendSuccessResponse(res, {});
  }),
];

export const changeShoppingCartItemQuantity = [
  validateRequest(requestSchema.body(shoppingCartSchema.changeShoppingCartItemQuantityBodySchema)),
  handleAsync(async (req, res) => {
    const { user } = req;
    const changeItem = req.validatedData
      ?.body as shoppingCartSchema.ChangeShoppingCartItemQuantityRequestType;

    await shoppingCartService.changeShoppingCartItemQuantity(user?.userId, changeItem);

    return sendSuccessResponse(res, {});
  }),
];

export const deleteShoppingCartItem = [
  validateRequest(requestSchema.body(shoppingCartSchema.deleteShoppingCartItemBodySchema)),
  handleAsync(async (req, res) => {
    const { user } = req;
    const deleteItem = req.validatedData
      ?.body as shoppingCartSchema.DeleteShoppingCartItemRequestType;

    await shoppingCartService.deleteShoppingCartItem(user?.userId, deleteItem);

    return sendSuccessResponse(res, {});
  }),
];

export const selectShoppingCartItem = [
  validateRequest(requestSchema.body(shoppingCartSchema.selectShoppingCartItemBodySchema)),
  handleAsync(async (req, res) => {
    const { user } = req;
    const selectCartItem = req.validatedData
      ?.body as shoppingCartSchema.SelectShoppingCartItemRequestType;

    await shoppingCartService.selectShoppingCartItem(user?.userId, selectCartItem);

    return sendSuccessResponse(res, {});
  }),
];

export const clearShoppingCartItem = [
  handleAsync(async (req, res) => {
    const { user } = req;

    await shoppingCartService.clearShoppingCartItem(user?.userId);

    return sendSuccessResponse(res, {});
  }),
];

export const getCheckoutIdempotencyKey = [
  validateRequest(requestSchema.body(shoppingCartSchema.checkoutCartBodySchema)),
  handleAsync(async (req: Request, res: Response) => {
    const { userId } = req.user as UserType;
    const { cartId, cartType } = req.validatedData
      ?.body as shoppingCartSchema.CheckoutCartBodySchemaRequestType;

    const token = await shoppingCartService.getCheckoutIdempotencyKey(userId, cartId, cartType);

    return sendSuccessResponse(res, { token });
  }),
];

export const checkoutShoppingCart = [
  verifyIdempotencyToken,
  validateRequest(requestSchema.body(shoppingCartSchema.checkoutCartBodySchema)),
  handleAsync(async (req: Request, res: Response) => {
    const { userId } = req.user as UserType;
    const { cartId, cartType } = req.validatedData
      ?.body as shoppingCartSchema.CheckoutCartBodySchemaRequestType;
    const { idempotency_key: token } = req.tokenData;

    const createOrderId = await shoppingCartService.checkoutShoppingCart(
      userId,
      cartId,
      cartType,
      token,
    );
    const responseBody = { orderId: createOrderId };

    const data = shoppingCartSchema.checkoutCartResponseSchema.parse(responseBody);

    return sendSuccessResponse(res, data);
  }),
];

export const getCheckoutOnlyOneIdempotencyKey = [
  validateRequest(requestSchema.body(shoppingCartSchema.checkoutOnlyOneCartBodySchema)),
  handleAsync(async (req: Request, res: Response) => {
    const { userId } = req.user as UserType;
    const { cartType, productId, skuId, quantity } = req.validatedData
      ?.body as shoppingCartSchema.CheckoutOnlyOneCartBodySchemaRequestType;

    const token = await shoppingCartService.getCheckoutOnlyOneIdempotencyKey(
      cartType,
      userId,
      productId,
      skuId,
      quantity,
    );

    return sendSuccessResponse(res, { token });
  }),
];

export const checkoutOnlyOneShoppingCart = [
  verifyIdempotencyToken,
  validateRequest(requestSchema.body(shoppingCartSchema.checkoutOnlyOneCartBodySchema)),
  handleAsync(async (req: Request, res: Response) => {
    const { userId } = req.user as UserType;
    const { cartType, productId, skuId, quantity } = req.validatedData
      ?.body as shoppingCartSchema.CheckoutOnlyOneCartBodySchemaRequestType;
    const { idempotency_key: token } = req.tokenData;

    const createOrderId = await shoppingCartService.checkoutShoppingCartOnlyOneProduct(
      cartType,
      userId,
      productId,
      skuId,
      quantity,
      token,
    );
    const responseBody = { orderId: createOrderId };

    const data = shoppingCartSchema.checkoutCartResponseSchema.parse(responseBody);

    return sendSuccessResponse(res, data);
  }),
];
