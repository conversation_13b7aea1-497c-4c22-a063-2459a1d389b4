export type CartCheckStrategyParameterType =
  | CartCheckParameter
  | (CartCheckParameter & CartItemStrategyParameterType)
  | (CartCheckParameter & {
      products: CartItemStrategyParameterType[];
    })
  | CheckoutCartCheckParameter;

export type CartCheckParameter = {
  userId: number;
  cartItemType: 'product' | 'ticket';
};

export type CartItemStrategyParameterType = {
  cartItemType: 'product' | 'ticket';
  productId: number;
  skuId: number;
  quantity?: number;
};

export type CheckoutCartCheckParameter = {
  userId: number;
  cartId: number;
};
