import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';

import { ZodRequestValidator } from '@shared/types/request.type';
import { ParameterInValidateException } from '@/errors';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      validatedData?: any;
      tokenData?: any;
      ServiceQueue: any;
    }
  }
}

export const validateRequest =
  <T extends ZodRequestValidator<any>>(schema: T) =>
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const data = {
        body: req.body,
        query: req.query,
        params: req.params,
      };
      const validatedData = await schema.parseAsync(data);
      req.validatedData = validatedData;

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        next(new ParameterInValidateException(error));
      }
      next(error);
    }
  };
