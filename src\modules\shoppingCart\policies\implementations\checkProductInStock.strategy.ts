import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import {
  CartCheckStrategyParameterType,
  CheckoutCartCheckParameter,
} from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';
import { PrismaCartItemType } from '@modules/shoppingCart/enums/shoppingCart.enum';

type ParameterType = CartCheckStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkProductInStock: Strategy = async (parameter) => {
  const { cartId } = parameter as CheckoutCartCheckParameter;

  const cartItems = await db.shopping_cart_item.findMany({
    select: { product_id: true, sku_id: true, quantity: true },
    where: { cart_id: cartId, cart_item_type: PrismaCartItemType.product, is_selected: true },
  });

  const queries = cartItems.reduce<Promise<any>[]>((a, v) => {
    if (v.product_id && v.sku_id) {
      const query = db.product_inventory.findFirst({
        select: { product_id: true, sku_id: true },
        where: {
          AND: [{ product_id: v.product_id }, { sku_id: v.sku_id }],
          available_quantity: { lt: v.quantity },
        },
      });
      return [...a, query];
    }
    return a;
  }, []);
  const result = await Promise.all(queries);

  if (result.some((x) => x !== null)) {
    throw new CustomErrorException(
      errorCodeTable.cartCheckoutProductOutOfStock,
      result.filter((x) => x !== null),
    );
  }
};
