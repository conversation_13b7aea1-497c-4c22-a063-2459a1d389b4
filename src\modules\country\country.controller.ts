import { handleAsync, validateRequest } from '@middleware';
import { requestSchema } from '@shared/schema/request.schema';
import { sendSuccessResponse } from '@utils/apiResponse';

import * as countryService from './country.service';
import * as countrySchema from './schema/country.schema';

export const getAllCountry = [
  validateRequest(requestSchema.query(countrySchema.getAllCountryQuerySchema)),
  handleAsync(async (req, res) => {
    const { language } = req.validatedData?.query as countrySchema.GetAllCountryRequestType;
    const result = await countryService.retrieveAllCountry(language);

    const data = countrySchema.getAllCountryResponseSchema.parse(result);

    return sendSuccessResponse(res, data);
  }),
];

export const getCountryByCode = [
  validateRequest(
    requestSchema.all({
      params: countrySchema.getCountryByCodeParamSchema,
      query: countrySchema.getCountryByCodeQuerySchema,
    }),
  ),
  handleAsync(async (req, res) => {
    const {
      params: { code },
      query: { language },
    } = req.validatedData as countrySchema.GetCountryByCodeRequestType;
    const result = await countryService.retrieveCountryByCode(code, language);

    const data = countrySchema.getCountryByCodeResponseSchema.parse(result);

    return sendSuccessResponse(res, data);
  }),
];
