-- CreateTable
CREATE TABLE `payment_method` (
    `payment_id` INTEGER NOT NULL AUTO_INCREMENT,
    `payment_code` VARCHAR(50) NOT NULL,
    `payment_name` VARCHAR(100) NOT NULL,
    `payment_region` VARCHAR(100) NULL,
    `payment_platform` VARCHAR(50) NULL,
    `payment_type` ENUM('QRCODE', 'MO<PERSON><PERSON>WEB', 'WEB', 'REDIRECT') NULL,
    `status` ENUM('enabled', 'disabled') NOT NULL,
    `description` TEXT NULL,
    `config` JSON NULL,
    `createdAt` INTEGER NOT NULL,
    `updatedAt` INTEGER NOT NULL,

    INDEX `idx_payment_code`(`payment_code`),
    INDEX `idx_payment_region`(`payment_region`),
    INDEX `idx_payment_platform`(`payment_platform`),
    UNIQUE INDEX `uk_code_region`(`payment_code`, `payment_region`),
    PRIMARY KEY (`payment_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
