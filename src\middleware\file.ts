import AWS from 'aws-sdk';
import { Request } from 'express';
import dayjs from 'dayjs';
import multer, { FileFilterCallback } from 'multer';
import { CustomErrorException, errorCodeTable } from '@errors';

const memoryStorage = multer.memoryStorage();

const fileTypes = ['image/jpeg', 'image/png', 'image/jpg'];

const imageFileFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  cb(null, fileTypes.includes(file.mimetype));
};

// NOTE: Add file category if any new modules.
export enum UPLOAD_IMAGE_FILE_CATEGORY {
  PRODUCT = 'product',
  EVENT = 'event',
}

export const uploader = multer({
  storage: memoryStorage,
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: imageFileFilter,
});

export const uploadImageFile = async (
  category: string,
  file?: Express.Multer.File,
): Promise<string | undefined> => {
  if (!process.env.S3_BUCKET_NAME) {
    throw new CustomErrorException(errorCodeTable.fileS3BucketNameIsNotExists);
  }
  if (!file) {
    throw new CustomErrorException(errorCodeTable.fileS3UploadFileIsNotValidate);
  }

  const s3 = new AWS.S3();
  const params = {
    Bucket: process.env.S3_BUCKET_NAME ?? '',
    Key: `${category}/${dayjs().unix().valueOf()}-${file?.originalname}`,
    Body: file?.buffer,
    ContentType: file?.mimetype,
  };

  try {
    const data = await s3.upload(params).promise();
    return data.Location;
  } catch (err) {
    throw new CustomErrorException(errorCodeTable.fileS3UploadFail, err);
  }
};
