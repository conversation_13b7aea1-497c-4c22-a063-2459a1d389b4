import { StatusCodes } from 'http-status-codes';

import { ErrorApiResponse } from '@shared/types/response.type';

import { errorCodeTable, ErrorCodeTableType } from './customErrorCode';

class HttpException extends Error {
  constructor(
    public errorCode: string,
    message: string,
    public statusCode: number = 400,
    public details?: any,
  ) {
    super(message);
    Object.setPrototypeOf(this, HttpException.prototype);
  }

  toResponse(): ErrorApiResponse {
    return {
      success: false,
      error: {
        code: this.errorCode,
        message: this.message,
        details: this.details,
      },
    };
  }
}

class BadRequestException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.badRequest;
    super(error.code, error.message, StatusCodes.BAD_REQUEST, details ?? error.details);
  }
}

class UnauthorizedException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.unauthorized;
    super(error.code, error.message, StatusCodes.UNAUTHORIZED, details ?? error.details);
  }
}

class ForbiddenException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.forbidden;
    super(error.code, error.message, StatusCodes.FORBIDDEN, details ?? error.details);
  }
}

class NotFoundException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.notFound;
    super(error.code, error.message, StatusCodes.NOT_FOUND, details ?? error.details);
  }
}

class MethodNotAllowedException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.methodNotAllowed;
    super(error.code, error.message, StatusCodes.METHOD_NOT_ALLOWED, details ?? error.details);
  }
}

class ConflictException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.conflict;
    super(error.code, error.message, StatusCodes.CONFLICT, details ?? error.details);
  }
}

class InternalServerErrorException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.internalServerError;
    super(error.code, error.message, StatusCodes.INTERNAL_SERVER_ERROR, details ?? error.details);
  }
}

class ParameterInValidateException extends HttpException {
  constructor(details?: any) {
    const error: ErrorCodeTableType = errorCodeTable.requestParameterInvalidate;
    super(error.code, error.message, StatusCodes.INTERNAL_SERVER_ERROR, details ?? error.details);
  }
}

class CustomErrorException extends HttpException {
  constructor({ code, message, details: defaultDetails }: ErrorCodeTableType, details?: any) {
    super(`${code}`, message, StatusCodes.OK, details ?? defaultDetails);
  }
}

export {
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  NotFoundException,
  MethodNotAllowedException,
  ConflictException,
  InternalServerErrorException,
  ParameterInValidateException,
  CustomErrorException,
};
export default HttpException;
