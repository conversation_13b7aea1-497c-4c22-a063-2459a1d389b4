import { Queue, Worker } from 'bullmq';
import { redis } from './redis';
import { NOTIFICATION_QUEUE_TOPIC, TICKET_QUEUE_TOPIC } from './constant';
import db from '@prismaClient';
import { generateJWT } from '@/utils/crypto';
import QRcode from 'qrcode';
import { S3_KEY, uploadS3File } from '@/utils/s3';
import { generateTimeNow } from '@/utils/common';
import { $Enums } from '@prisma/client';
import { ServiceQueue } from '.';

export class TicketMQ {
    private queue: any;
    private worker: any;

    constructor() {
        this.initWorker()
        console.info("[setup] inited ticket worker")
    }

    initWorker() {
        this.worker = new Worker(
            'ticket',
            async job => {
                switch (job.name) {
                    case TICKET_QUEUE_TOPIC.ISSUE:
                        console.info(`[issuing ticket] >> order id: ${job.data?.orderId}`)
                        await this.generateTicket(job.data.orderId, job.data.ticket)
                        break;
                    case TICKET_QUEUE_TOPIC.REFUND:
                        // TODO: refund the order
                        break;
                    case TICKET_QUEUE_TOPIC.VOID:
                        // TODO: void the ticket
                        break;
                }
            },
            { connection: redis },
        );
    }

    async generateTicket(orderId: number, tickets: number[]) {
        const ticketToBeGenerated = await db.ticket.findMany({
            where: {
                id: {
                    in: tickets
                }
            }
        })

        const timenow = generateTimeNow()
        let allGenerated = true;

        const updateObject = await Promise.all(ticketToBeGenerated.map(async (item) => {
            const {
                id,
                qrcode
            } = item
            const qrcodeJWT = generateJWT(qrcode)
            const bufferStr = await QRcode.toBuffer(qrcodeJWT)
            let updatedInfo;
            try {
                const key = await uploadS3File(
                    S3_KEY.TICKET,
                    `${id}_qrcode.png`,
                    bufferStr,
                    Buffer.byteLength(bufferStr),
                    true
                )

                updatedInfo = await db.ticket.update({
                    data: {
                        url: key,
                        updated_at: timenow,
                        status: $Enums.TicketType.ISSUED
                    },
                    where: {
                        id
                    }
                })
            } catch(error) {
                allGenerated = false;
                console.error(`[issue ticket error] >> id: ${id} >> error: `, error)
            }
            return updatedInfo
        }))
        if (!allGenerated) {
            // TODO: send email to relevant party
        } else {
            ServiceQueue.notificationQueue.add(NOTIFICATION_QUEUE_TOPIC.TICKET_GENERATED, { orderId })
        }
        console.info(
            `[issued ticket] >> id: ${updateObject.map((item) => item?.id).filter(item => item).toString()} \
            expected: ${updateObject.length} >> \
            generated: ${updateObject.map((item) => item?.id).filter(item => item).length}       
            `
        )
    }
}