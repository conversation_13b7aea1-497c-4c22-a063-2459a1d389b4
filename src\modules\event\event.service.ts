import db from '@prismaClient';
import { CreateEventDtoType, GetEventDtoType, ListEventDtoType, UpdateEventDtoType, UpdateEventQaaDtoType, UpdateEventStatusDtoType, UpdateEventTermsDtoType } from './event.dto';
import { generateTimeNow } from '@/utils/common';
import { $Enums, EventStatus } from '@prisma/client';
import { S3_KEY, uploadS3File } from '@/utils/s3';
import dayjs from 'dayjs';
import { uploadImageFile } from '@/middleware';

export const createEvent = async (event: CreateEventDtoType) => {
    await db.event.create({
        data: {
            name: event.name,
            description: event.description,
            region_code: event.regionCode,
            timezone: event.timezone,
            start_date: event.startDate,
            end_date: event.endDate,
            start_time: event.startTime,
            end_time: event.endTime,
            duration: event.duration,
            venue: event.venue,
            status: $Enums.EventStatus.DRAFT,
            parent_id: event.parentId,
            created_at: generateTimeNow(),
            updated_at: generateTimeNow(),
        }
    })
};

export const updateEvent = async (id: number, event: UpdateEventDtoType) => {
    await db.event.update({
        data: {
            ...event,
            updated_at: generateTimeNow(),
        },
        where: {
            id
        }
    })
};

export const updateEventStatus = async (id: number, data: UpdateEventStatusDtoType) => {
    await db.event.update({
        data: {
            status: data.status,
            updated_at: generateTimeNow(),
        },
        where: {
            id
        }
    })
};

export const updateEventMedias = async (id: number, {removeIds}: { removeIds?: number[] }, files: any[]) => {
    // TODO:: upload media to s3 
    const timenow = dayjs().unix()
    await db.$transaction(async(tx) => {
        if (removeIds?.length && removeIds.length > 0) {
            await tx.event_media.deleteMany({
                where: {
                    id: {
                        in: removeIds.map((id) => Number(id))
                    }
                }
            })
        }
        const urls = await Promise.all(files.map((uploadedFile, idx) => {
            return uploadImageFile(
                S3_KEY.EVENT,
                uploadedFile
            )
        }))
        await Promise.all(urls.filter(item => item !== undefined).map((url, idx) => {
            return tx.event_media.create({
                data: {
                    event_id: id,
                    src: url,
                    seq: idx,
                    created_at: timenow,
                    updated_at: timenow,
                }
            })
        }))
    })
};

export const updateEventSetting = async (id: number, content: string) => {
    const timenow = generateTimeNow()
    await db.event_setting.upsert({
        create: {
            event_id: id,
            content,
            created_at: timenow,
            updated_at: timenow,
        },
        update: {
            content,
            updated_at: timenow,
        },
        where: {
            event_id: id
        }
    })
}

// TODO: send to s3
export const updateEventQaa = async (id: number, data: UpdateEventQaaDtoType) => {
    const timenow = generateTimeNow()
    const allExistIds = data.filter(item => item.id).map((question) => question.id)
    const newQuestions = data.filter((question) => !question.id)
    const updateQuestions = data.filter((question) => question.id)

    await db.$transaction(async(tx) => {
        if (allExistIds.length > 0) {
            // clear all removed questions
            await tx.event_qaa.deleteMany({
                where: {
                    id: {
                        notIn: allExistIds as number[]
                    }
                }
            })
        }
        if (newQuestions.length > 0) {
            await Promise.all(newQuestions.map((question) => tx.event_qaa.create({
                data: {
                    event_id: id,
                    question: question.question,
                    answer: question.answer,
                    created_at: timenow,
                    updated_at: timenow,
                }
            })))
        }
        if (updateQuestions.length > 0) {
            await Promise.all(updateQuestions.map((question) => tx.event_qaa.update({
                data: {
                    ...question,
                    updated_at: timenow
                },
                where: {
                    id: question.id
                }
            })))
        }
    })

}

// TODO: send to s3
export const updateEventTerms = async (id: number, data: UpdateEventTermsDtoType) => {
    const timenow = generateTimeNow()
    await db.event_terms.upsert({
        create: {
            event_id: id,
            content: data.content,
            created_at: timenow,
            updated_at: timenow,
        },
        update: {
            content: data.content,
            updated_at: timenow,
        },
        where: {
            event_id: id
        }
    })
}

export const listEvent = async (query: ListEventDtoType) => {
    const {
        search,
        region,
        status,
        page,
        size
    } = query
    return await db.event.findMany({
        select: {
            id: true,
            name: true,
            description: true,
            start_date: true,
            start_time: true,
            end_date: true,
            end_time: true,
            region_code: true,
            venue: true,
            created_at: true,
            updated_at: true,
            ticket_setting: {
                include: {
                    ticket_section: {
                        select: {
                            quantity: true,
                            day: true,
                            date: true,
                            start_time: true,
                            end_time: true,
                        }
                    },
                    ticket_type: true,
                }
            },
            event_media: {
                select: {
                    src: true
                }
            }
        },
        where: {
            ...(search? {
                name: {
                    contains: search,
                }
            }: {}),
            ...(region? {
                region_code: {
                    in: region
                }
            }: {}),
            ...(status? {status}: { status: EventStatus.ACTIVE }),
        },
        take: size,
        skip: (page - 1) * size
    })
}

export const getEvent = async (params: GetEventDtoType) => {
    const {
        id: eventId
    } = params

    return await db.event.findUnique({
        select: {
            id: true,
            name: true,
            region_code: true,
            timezone: true,
            start_date: true,
            end_date: true,
            start_time: true,
            end_time: true,
            duration: true,
            venue: true,
            event_media: true,
            event_qaa: true,
            event_terms: true,
            event_setting: true,
            status: true,
            updated_at: true,
            parent_event: true,
            ticket_setting: {
                select: {
                    id: true,
                    currency: true,
                    sale_time_thershold: true,
                    sale_start_datetime: true,
                    sale_end_datetime: true,
                    ticket_section: {
                        select: {
                            day: true,
                            date: true,
                            id: true,
                            start_time: true,
                            end_time: true,
                            ticket_date_inventory: true,
                            seq: true,
                            quantity: true,
                            ticket_distribution: {
                                select: {
                                    ticket_date_inventory: {
                                        select: {
                                            id: true,
                                            timestamp: true,
                                        }
                                    },
                                    ticket_type: {
                                        select: {
                                            third_party: true
                                        }
                                    },
                                    ticket_distribution_adjustment: true
                                }
                            }
                        }
                    },
                    ticket_variation: {
                        select: {
                            id: true,
                            name: true,
                            ticket_variation_option: true
                        }
                    },
                    ticket_type: {
                        select: {
                            id: true,
                            name: true,
                            price: true,
                            price_unit: true,
                            selling_type: true,
                            multiply: true,
                            third_party: true,
                            pre_sale_start_time: true,
                            pre_sale_end_time: true,
                            description: true,
                            have_gift: true,
                            status: true,
                            allow_multiple_entry: true,
                            ticket_type_distribution: {
                                select: {
                                    ticket_distribution_adjustment: true
                                }
                            },
                            ticket_type_to_section: {
                                select: {
                                    ticket_section: true
                                }
                            },
                            ticket_type_to_variation: {
                                select: {
                                    ticket_variation: true
                                }
                            },
                            ticket_to_gift: {
                                select: {
                                    products: true
                                }
                            }
                        }
                    },
                }
            }
        },
        where: {
            id: eventId
        }
    })
}