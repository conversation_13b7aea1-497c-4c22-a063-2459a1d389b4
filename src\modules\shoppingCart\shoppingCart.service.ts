import db from '@prismaClient';
import { getAllInfoByISO } from 'iso-country-currency';

import { generateTimeNow, getUnixAfterDays, languageCodeSorter } from '@utils/common';
import { Language, LanguageCode } from '@shared/enums';
import { PrismaTransaction } from '@shared/types/prisma.type';

import * as orderService from '@modules/order/order.service';
import * as productService from '@modules/product/product.service';
import * as idempotencyKeyService from '@modules/idempotencyKey/idempotencyKey.service';
import { PrismaIdempotencyKeyStatus } from '@modules/idempotencyKey/enum/idempotencyKey.enum';
import {
  retrieveCountryByCode,
  retrieveCurrencyByCountryCode,
} from '@modules/country/country.service';

import {
  CartType,
  PrismaCartActivityType,
  PrismaCartItemType,
  PrismaCartType,
} from './enums/shoppingCart.enum';
import {
  AddShoppingCartItemRequestType,
  ChangeShoppingCartItemQuantityRequestType,
  DeleteShoppingCartItemRequestType,
  SelectShoppingCartItemRequestType,
} from './schema/shoppingCart.schema';
import { ValidatorContext } from './policies/context/validator.context';
import { UseCase } from './enums/useCase.enum';
import { CreateProductOrderSchemaType } from '../order/schema/order.schema';

export const retrieveShoppingCartByUserId = async (userId: number, language: Language) => {
  // find any cart
  const countShoppingCart = await db.shopping_cart.count({
    where: {
      user_id: userId,
      expires_at: { gte: generateTimeNow() },
    },
  });

  const isExistsShoppingCart: boolean = countShoppingCart > 0;

  if (!isExistsShoppingCart) {
    const now = generateTimeNow();
    const expire = getUnixAfterDays(30);

    await db.shopping_cart.create({
      data: {
        user_id: userId,
        cart_type: 'productOnly',
        cart_total: 0,
        discount_total: 0,
        item_count: 0,
        created_at: now,
        updated_at: now,
        expires_at: expire,
      },
    });
  }

  const shoppingCart = await db.shopping_cart.findUnique({
    select: {
      cart_id: true,
      cart_type: true,
      cart_total: true,
      discount_total: true,
      item_count: true,
      expires_at: true,
      shoppingCartItems: {
        select: {
          shopping_cart_item_id: true,
          cart_item_type: true,
          product_id: true,
          sku_id: true,
          quantity: true,
          original_price: true,
          sale_price: true,
          total_price: true,
          added_at: true,
          created_at: true,
          updated_at: true,
          notes: true,
          is_selected: true,
          products: {
            select: {
              product_id: true,
              region: true,
              event_id: true,
              event: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                },
              },
              product_sku: {
                select: {
                  sku_id: true,
                  original_price: true,
                  sale_price: true,
                  product_media: {
                    select: { url: true },
                    where: {
                      is_main: true,
                      media_type: 'image',
                      is_active: true,
                    },
                  },
                  product_sku_attributes: {
                    select: {
                      product_attributes: {
                        select: {
                          product_attribute_categories: {
                            select: {
                              product_attribute_translation: {
                                select: { name: true, language_code: true },
                                where: { language_code: { in: [language, 'EN'] } },
                              },
                            },
                          },
                          product_attribute_values: {
                            select: {
                              product_attribute_translation: {
                                select: { name: true, language_code: true },
                                where: { language_code: { in: [language, 'EN'] } },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              product_translations: {
                select: { name: true, language_code: true },
                where: { language_code: { in: [language, 'EN'] } },
              },
            },
          },
        },
      },
    },
    where: {
      user_id: userId,
      expires_at: { gte: generateTimeNow() },
    },
  });

  // Get the unique region code from cart items
  const cartRegion = shoppingCart?.shoppingCartItems
    .filter((x) => x.cart_item_type === 'product')
    .reduce<Set<string>>((a, v) => {
      return v?.products?.region ? a.add(v?.products?.region) : a;
    }, new Set()) ?? [null];
  const countryCode = [...cartRegion][0];

  const countryInfo = await retrieveCountryByCode(countryCode ?? '', language);

  // Get currency
  const currency = retrieveCurrencyByCountryCode(countryCode ?? '');

  // filter only selected cart items
  const selectedCartItems =
    shoppingCart?.shoppingCartItems.filter((cartItem) => cartItem.is_selected) ?? [];

  // Calculate total amount from selected cart items.
  const itemSubTotal = selectedCartItems
    .reduce((a, v) => {
      const tp = Number.parseFloat(v?.total_price.toString());
      const sp = Number.parseFloat(v?.sale_price.toString());
      const qty = Number(v.quantity);

      // check the total_price is validate
      if (tp === sp * qty) {
        return a + tp;
      }
      return a + sp * qty;
    }, 0)
    .toFixed(2);

  // Count only selected cart items
  const itemCount = selectedCartItems.reduce((a, v) => {
    return a + v.quantity;
  }, 0);

  // Transform to shoppingCartItems
  const shoppingCartItems = shoppingCart?.shoppingCartItems.map((item) => {
    const productEvent = item?.products?.event;
    const productTranslation = item?.products?.product_translations?.sort(languageCodeSorter)?.[0];
    const productSKU = item?.products?.product_sku?.find((sku) => sku.sku_id === item.sku_id);
    const productSKUAttribute = productSKU?.product_sku_attributes ?? [];
    const skuAttribute = productService.skuAttributesMapper(productSKUAttribute);

    return {
      shippingCartItemId: item.shopping_cart_item_id,
      shippingCartItemType: item.cart_item_type,
      is_selected: item.is_selected,
      productId: item.product_id,
      skuId: item.sku_id,
      productName: productTranslation?.name,
      thumbnail: productSKU?.product_media?.[0]?.url,
      event: {
        id: productEvent?.id,
        name: ([LanguageCode.TC, LanguageCode.TS] as string[]).includes(language)
          ? productEvent?.name
          : productEvent?.name_en,
      },
      price: {
        salePrice: Number.parseFloat(productSKU?.sale_price?.toString() ?? '0'),
        originalPrice: Number.parseFloat(productSKU?.original_price?.toString() ?? '0'),
      },
      quantity: item?.quantity,
      skuAttribute,
    };
  });

  // Event
  const cartEvent =
    shoppingCart?.shoppingCartItems?.reduce((a, v) => {
      const id = v?.products?.event_id?.toString();
      const name = ([LanguageCode.TC, LanguageCode.TS] as string[]).includes(language)
        ? v.products?.event?.name
        : v?.products?.event?.name_en;

      return {
        ...a,
        ...(id && name && !Object.keys(a).includes(id)
          ? { [id]: { eventId: Number(id), eventName: name } }
          : {}),
      };
    }, {}) ?? {};

  return {
    shoppingCartId: shoppingCart?.cart_id,
    shoppingCartType: shoppingCart?.cart_type,
    region: countryCode,
    regionName: countryInfo?.name,
    currency,
    summary: {
      cartTotalQuantity: itemCount,
      cartTotalAmount: Number(itemSubTotal),
    },
    shoppingCartItems,
    events: Object.values(cartEvent),
  };
};

export const retrieveShoppingCartCountByUserId = async (userId: number) => {
  const countShoppingCart = await db.shopping_cart_item.findMany({
    select: { quantity: true },
    where: {
      shoppingCart: {
        user_id: userId,
        expires_at: { gte: generateTimeNow() },
      },
    },
  });
  return { count: countShoppingCart.reduce((a, { quantity }) => a + quantity, 0) };
};

const updateCartAfterCarItemChange = async (tx: any, cartId: number) => {
  const resultCartItems = await tx.shopping_cart_item.findMany({
    select: { quantity: true, total_price: true, sale_price: true, original_price: true },
    where: { cart_id: cartId },
  });
  const { cartTotal, itemCount, discountTotal } = (resultCartItems as any[]).reduce<{
    cartTotal: number;
    itemCount: number;
    discountTotal: number;
  }>(
    (a, v) => {
      return {
        cartTotal: a.cartTotal + v.total_price.toNumber(),
        discountTotal: a.discountTotal + 0,
        itemCount: a.itemCount + v.quantity,
      };
    },
    { cartTotal: 0, itemCount: 0, discountTotal: 0 },
  );

  await tx.shopping_cart.update({
    data: {
      cart_total: Number(cartTotal.toFixed(2)),
      discount_total: Number(discountTotal.toFixed(2)),
      item_count: itemCount,
    },
    where: { cart_id: cartId },
  });
};

export const addShoppingCartItem = async (
  userId: number,
  cartItem: AddShoppingCartItemRequestType,
) => {
  await ValidatorContext.execute(UseCase.addProductToCart, { userId, ...cartItem });

  await db.$transaction(async (tx) => {
    const now = generateTimeNow();

    // Get cart id
    const { cart_id: cartId } = await tx.shopping_cart.findUniqueOrThrow({
      select: { cart_id: true },
      where: { user_id: userId },
    });

    // get product info
    const productSkuInfo = await db.product_sku.findUniqueOrThrow({
      where: { product_id: cartItem.productId, sku_id: cartItem.skuId },
    });
    const { original_price: originalPrice, sale_price: salePrice } = productSkuInfo;

    // get old cart item
    const oldCartItem = await tx.shopping_cart_item.findUnique({
      where: {
        cart_id_cart_item_type_product_id_sku_id: {
          cart_id: cartId,
          cart_item_type: cartItem.cartItemType,
          product_id: cartItem.productId,
          sku_id: cartItem.skuId,
        },
      },
    });

    const upsertCartItem = await tx.shopping_cart_item.upsert({
      where: {
        cart_id_cart_item_type_product_id_sku_id: {
          cart_id: cartId,
          cart_item_type: cartItem.cartItemType,
          product_id: cartItem.productId,
          sku_id: cartItem.skuId,
        },
      },
      create: {
        cart_item_type: cartItem.cartItemType,
        is_selected: true,
        quantity: cartItem.quantity,
        original_price: originalPrice,
        sale_price: salePrice,
        total_price: salePrice.mul(cartItem.quantity).toNumber(),
        added_at: now,
        created_at: now,
        updated_at: now,
        products: { connect: { product_id: cartItem.productId } },
        product_sku: { connect: { sku_id: cartItem.skuId } },
        shoppingCart: {
          connect: { cart_id: cartId, user_id: userId },
        },
      },
      update: {
        quantity: { increment: cartItem.quantity },
        total_price: { increment: salePrice.mul(cartItem.quantity).toNumber() },
      },
    });

    await updateCartAfterCarItemChange(tx, cartId);

    await tx.shopping_cart_activity_log.create({
      data: {
        user_id: userId,
        cart_activity_type: PrismaCartActivityType.addCartItem,
        old_value: oldCartItem
          ? {
              cartId,
              userId,
              cartItemType: oldCartItem.cart_item_type,
              product_id: oldCartItem.product_id,
              sku_id: oldCartItem.sku_id,
              quantity: oldCartItem.quantity,
            }
          : {},
        new_value: {
          cartId,
          userId,
          cartItemType: upsertCartItem.cart_item_type,
          product_id: upsertCartItem.product_id,
          sku_id: upsertCartItem.sku_id,
          quantity: upsertCartItem.quantity,
        },
        extra_info: {},
        operation_time: now,
        shopping_cart_item_id: upsertCartItem.shopping_cart_item_id,
        cart_id: cartId,
      },
    });
  });

  return {};
};

export const changeShoppingCartItemQuantity = async (
  userId: number,
  cartItem: ChangeShoppingCartItemQuantityRequestType,
) => {
  await ValidatorContext.execute(UseCase.changeCartItem, { userId, ...cartItem });

  await db.$transaction(async (tx) => {
    const now = generateTimeNow();

    const { cart_id: cartId } = await tx.shopping_cart.findUniqueOrThrow({
      select: { cart_id: true },
      where: { user_id: userId },
    });

    const { sale_price: salePrice } = await tx.product_sku.findUniqueOrThrow({
      where: { product_id: cartItem.productId, sku_id: cartItem.skuId },
    });

    const oldCartItem = await tx.shopping_cart_item.findUniqueOrThrow({
      where: {
        cart_id_cart_item_type_product_id_sku_id: {
          cart_id: cartId,
          cart_item_type: cartItem.cartItemType,
          product_id: cartItem.productId,
          sku_id: cartItem.skuId,
        },
      },
    });

    // Update cart item
    const updateCartItem = await tx.shopping_cart_item.update({
      where: {
        cart_id_cart_item_type_product_id_sku_id: {
          cart_id: cartId,
          cart_item_type: cartItem.cartItemType,
          product_id: cartItem.productId,
          sku_id: cartItem.skuId,
        },
      },
      data: {
        quantity: cartItem.quantity,
        total_price: salePrice.mul(cartItem.quantity).toNumber(),
        updated_at: now,
      },
    });

    await updateCartAfterCarItemChange(tx, cartId);

    // Cart item created success, insert log
    await tx.shopping_cart_activity_log.create({
      data: {
        user_id: userId,
        cart_activity_type: PrismaCartActivityType.addCartItem,
        old_value: {
          cartId,
          userId,
          cartItemType: oldCartItem.cart_item_type,
          product_id: oldCartItem.product_id,
          sku_id: oldCartItem.sku_id,
          quantity: oldCartItem.quantity,
        },
        new_value: {
          cartId,
          userId,
          cartItemType: updateCartItem.cart_item_type,
          product_id: updateCartItem.product_id,
          sku_id: updateCartItem.sku_id,
          quantity: updateCartItem.quantity,
        },
        extra_info: {},
        operation_time: now,
        shopping_cart_item_id: updateCartItem.shopping_cart_item_id,
        cart_id: cartId,
      },
    });
  });

  return {};
};

export const deleteShoppingCartItem = async (
  userId: number,
  cartItem: DeleteShoppingCartItemRequestType,
) => {
  await ValidatorContext.execute(UseCase.deleteCartItem, { userId, ...cartItem });

  await db.$transaction(async (tx) => {
    const now = generateTimeNow();

    const { cart_id: cartId } = await tx.shopping_cart.findUniqueOrThrow({
      select: { cart_id: true },
      where: { user_id: userId },
    });

    const oldCartItem = await tx.shopping_cart_item.findUniqueOrThrow({
      where: {
        cart_id_cart_item_type_product_id_sku_id: {
          cart_id: cartId,
          cart_item_type: cartItem.cartItemType,
          product_id: cartItem.productId,
          sku_id: cartItem.skuId,
        },
      },
    });

    // Delete cart item
    const deleteCartItem = await tx.shopping_cart_item.delete({
      where: {
        cart_id_cart_item_type_product_id_sku_id: {
          cart_id: cartId,
          cart_item_type: cartItem.cartItemType,
          product_id: cartItem.productId,
          sku_id: cartItem.skuId,
        },
      },
    });

    await updateCartAfterCarItemChange(tx, cartId);

    // Cart item created success, insert log
    await tx.shopping_cart_activity_log.create({
      data: {
        user_id: userId,
        cart_activity_type: PrismaCartActivityType.removeCartItem,
        old_value: {
          cartId,
          userId,
          cartItemType: oldCartItem.cart_item_type,
          product_id: oldCartItem.product_id,
          sku_id: oldCartItem.sku_id,
          quantity: oldCartItem.quantity,
        },
        new_value: {},
        extra_info: {},
        operation_time: now,
        cart_id: cartId,
        shopping_cart_item_id: deleteCartItem.shopping_cart_item_id,
      },
    });
  });
};

export const selectShoppingCartItem = async (
  userId: number,
  cartItem: SelectShoppingCartItemRequestType,
) => {
  const validateParameter = {
    userId,
    cartItemType: cartItem.cartItemType,
    products: cartItem.products.map(({ productId, skuId }) => ({
      productId,
      skuId,
      cartItemType: cartItem.cartItemType,
    })),
  };
  await ValidatorContext.execute(UseCase.selectCartItem, validateParameter);

  await db.$transaction(async (tx) => {
    const now = generateTimeNow();

    const { cart_id: cartId } = await tx.shopping_cart.findUniqueOrThrow({
      select: { cart_id: true },
      where: { user_id: userId },
    });

    const oldCartItem = await tx.shopping_cart_item.findMany({
      select: { product_id: true, sku_id: true, is_selected: true },
      where: {
        cart_id: cartId,
        product_id: { in: cartItem.products.map((x) => x.productId) },
        sku_id: { in: cartItem.products.map((x) => x.skuId) },
      },
    });

    const updateCartItems = await Promise.all(
      cartItem.products.map(({ productId, skuId, isSelected }) => {
        return tx.shopping_cart_item.update({
          data: { is_selected: isSelected },
          where: {
            cart_id_cart_item_type_product_id_sku_id: {
              cart_id: cartId,
              cart_item_type: cartItem.cartItemType,
              product_id: productId,
              sku_id: skuId,
            },
          },
        });
      }),
    );

    const createCartLogData = updateCartItems.map((item) => ({
      user_id: userId,
      cart_activity_type: PrismaCartActivityType.selectCartItem,
      old_value: {
        is_selected: oldCartItem.find(
          (x) => x.product_id === item.product_id && x.sku_id === item.sku_id,
        )?.is_selected,
      },
      new_value: {
        is_selected: item.is_selected,
      },
      extra_info: {
        cartId,
        userId,
        cartItemType: item.cart_item_type,
        product_id: item.product_id,
        sku_id: item.sku_id,
      },
      operation_time: now,
      cart_id: cartId,
      shopping_cart_item_id: item.shopping_cart_item_id,
    }));

    await tx.shopping_cart_activity_log.createMany({
      data: createCartLogData,
    });
  });
};

export const clearShoppingCartItem = async (userId: number) => {
  await ValidatorContext.execute(UseCase.emptyCart, { userId, cartItemType: 'product' });

  await db.$transaction(async (tx) => {
    const now = generateTimeNow();

    // Get cartId by user
    const { cart_id: cartId } = await tx.shopping_cart.findUniqueOrThrow({
      select: { cart_id: true },
      where: { user_id: userId },
    });

    const oldCartItem = await tx.shopping_cart_item.findMany({
      where: { cart_id: cartId },
    });

    // Delete cart item
    await tx.shopping_cart_item.deleteMany({
      where: { cart_id: cartId },
    });

    await updateCartAfterCarItemChange(tx, cartId);

    // create many parameter
    const cartLogParam = oldCartItem.map((item) => ({
      user_id: userId,
      cart_activity_type: PrismaCartActivityType.clearCart,
      old_value: {
        cartId,
        userId,
        cartItemType: item.cart_item_type,
        product_id: item.product_id,
        sku_id: item.sku_id,
        quantity: item.quantity,
      },
      new_value: {},
      extra_info: {},
      operation_time: now,
      cart_id: cartId,
      shopping_cart_item_id: item.shopping_cart_item_id,
    }));

    // Cart item created success, insert log
    await tx.shopping_cart_activity_log.createMany({
      data: cartLogParam,
    });
  });
};

export const getCheckoutIdempotencyKey = async (
  userId: number,
  cartId: number,
  cartType: CartType,
) => {
  const checkoutRoute = {
    httpMethod: 'POST',
    routePath: '/api/public/v1/shopping-cart/checkout',
    requestHash: JSON.stringify({ userId: userId, cartId, cartType }),
  };

  return idempotencyKeyService.getIdempotencyKey(userId, checkoutRoute);
};

export const checkoutShoppingCart = async (
  userId: number,
  cartId: number,
  cartType: CartType,
  token: string,
) => {
  // validation
  await ValidatorContext.execute(UseCase.checkout, { userId, cartId });

  // Get cart
  const cart = await db.shopping_cart.findUnique({
    where: {
      cart_id: cartId,
      cart_type: cartType,
      user_id: userId,
    },
  });

  // Get region
  const shoppingCartRegion = await db.shopping_cart_item.findFirst({
    select: { products: { select: { region: true } } },
    where: { cart_id: cartId },
  });

  // Get cart items
  const cartItems = await db.shopping_cart_item.findMany({
    where: { cart_id: cartId, cart_item_type: PrismaCartItemType.product, is_selected: true },
  });
  const orderItems = cartItems
    .filter((x) => x.product_id && x.sku_id)
    .map((x) => ({
      productId: x.product_id ?? 0,
      skuId: x.sku_id ?? 0,
      quantity: x.quantity,
      unitPrice: x.sale_price.toNumber(),
      originalPrice: x.original_price.toNumber(),
      totalPrice: x.total_price.toNumber(),
      discountAmount: x.original_price.minus(x.sale_price).mul(x.quantity).toNumber(),
    }));

  const { cart_total: cartTotal, discount_total: discountTotal } = cart ?? {};
  const { region } = shoppingCartRegion?.products ?? {};
  const { currency } = getAllInfoByISO(region ?? '');

  return await db.$transaction(async (prisma) => {
    const createProductOrderResult = await orderService.createProductOrder(prisma, {
      cartType: cartType,
      userId: userId,
      region: region || '',
      totalAmount: cartTotal?.toNumber() ?? 0,
      discountAmount: discountTotal?.toNumber() ?? 0,
      currency: currency,
      orderItems: orderItems,
    });
    // Update product inventory
    for (const orderItems of createProductOrderResult.orderItems) {
      await productService.updateProductInventoryAfterOrderCreate(prisma, {
        orderId: createProductOrderResult.orderId,
        productId: orderItems.productId,
        skuId: orderItems.skuId,
        quantity: orderItems.quantity,
      });
    }
    // Update Idempotency key status => used.
    await idempotencyKeyService.updateIdempotencyKeyStatus(
      prisma,
      token,
      PrismaIdempotencyKeyStatus.used,
    );
    // Remove is selected cart items.
    await removeCheckoutCartItems(prisma, userId, cartId);

    return createProductOrderResult.orderId;
  });
};

export const removeCheckoutCartItems = async (
  prisma: PrismaTransaction,
  userId: number,
  cartId: number,
) => {
  const findShoppingCartItem = await prisma.shopping_cart_item.findMany({
    where: {
      is_selected: true,
      shoppingCart: { cart_id: cartId, user_id: userId },
    },
  });

  if (findShoppingCartItem?.length > 0) {
    const cartItemIds = findShoppingCartItem.map((x) => x.shopping_cart_item_id);
    const countDeleteCartItem = await prisma.shopping_cart_item.deleteMany({
      where: { shopping_cart_item_id: { in: cartItemIds } },
    });
    await updateCartAfterCarItemChange(prisma, cartId);

    if (countDeleteCartItem) {
      for (const item of findShoppingCartItem) {
        await prisma.shopping_cart_activity_log.create({
          data: {
            cart_id: cartId,
            user_id: userId,
            cart_activity_type: PrismaCartActivityType.checkout,
            shopping_cart_item_id: item.shopping_cart_item_id,
            old_value: item,
            new_value: {},
            extra_info: {},
            operation_time: generateTimeNow(),
          },
        });
      }
    }
  }
};

export const getCheckoutOnlyOneIdempotencyKey = async (
  cartType: CartType,
  userId: number,
  productId: number,
  skuId: number,
  quantity: number,
) => {
  const checkoutRoute = {
    httpMethod: 'POST',
    routePath: '/api/public/v1/shopping-cart/checkout/only-one',
    requestHash: JSON.stringify({ userId: userId, cartType, productId, skuId, quantity }),
  };

  return idempotencyKeyService.getIdempotencyKey(userId, checkoutRoute);
};

export const checkoutShoppingCartOnlyOneProduct = async (
  cartType: CartType,
  userId: number,
  productId: number,
  skuId: number,
  quantity: number,
  token: string,
) => {
  // Get product
  const findOneProduct = await db.products.findUniqueOrThrow({
    select: { region: true },
    where: { product_id: productId },
  });
  const region: string = findOneProduct.region;

  // Get product sku
  const productSkuInfo = await db.product_sku.findUniqueOrThrow({
    where: { product_id: productId, sku_id: skuId },
  });
  const { original_price: originalPrice, sale_price: salePrice } = productSkuInfo;

  const orderItems = [
    {
      productId: productId,
      skuId: skuId,
      quantity: quantity,
      unitPrice: salePrice.toNumber(),
      originalPrice: originalPrice.toNumber(),
      totalPrice: salePrice.mul(quantity).toNumber(),
      discountAmount: 0,
    },
  ];

  const { currency } = getAllInfoByISO(region ?? '');

  return await db.$transaction(async (prisma) => {
    const createProductOrderResult = await orderService.createProductOrder(prisma, {
      cartType: PrismaCartType.productOnly,
      userId: userId,
      region: region || '',
      totalAmount: salePrice.mul(quantity).toNumber(),
      discountAmount: 0,
      currency: currency,
      orderItems: orderItems,
    });
    // Update product inventory
    for (const orderItems of createProductOrderResult.orderItems) {
      await productService.updateProductInventoryAfterOrderCreate(prisma, {
        orderId: createProductOrderResult.orderId,
        productId: orderItems.productId,
        skuId: orderItems.skuId,
        quantity: orderItems.quantity,
      });
    }
    // Update Idempotency key status => used.
    await idempotencyKeyService.updateIdempotencyKeyStatus(
      prisma,
      token,
      PrismaIdempotencyKeyStatus.used,
    );

    return createProductOrderResult.orderId;
  });
};
