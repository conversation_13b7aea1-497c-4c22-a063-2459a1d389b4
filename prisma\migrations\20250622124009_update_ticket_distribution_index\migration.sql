/*
  Warnings:

  - You are about to drop the `ticket_inventory` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[ticket_type_id,ticket_date_inventory_id]` on the table `ticket_distribution` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `ticket_date_inventory_id` to the `ticket_distribution` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `ticket_inventory` DROP FOREIGN KEY `ticket_inventory_ticket_section_id_fkey`;

ALTER TABLE `ticket_distribution` DROP FOREIGN KEY `ticket_distribution_ticket_section_id_fkey` ; 

ALTER TABLE `ticket_distribution` DROP FOREIGN KEY `ticket_distribution_ticket_type_id_fkey` ; 

-- DropIndex
DROP INDEX `ticket_distribution_ticket_type_id_ticket_section_id_key` ON `ticket_distribution`;

-- AlterTable
ALTER TABLE `ticket_distribution` ADD COLUMN `ticket_date_inventory_id` INTEGER NOT NULL;

-- DropTable
DROP TABLE `ticket_inventory`;

-- CreateTable
CREATE TABLE `ticket_date_inventory` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `total` INTEGER NOT NULL,
    `available` INTEGER NOT NULL,
    `on_hold` INTEGER NOT NULL,
    `timestamp` INTEGER NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,
    `ticket_section_id` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `ticket_distribution_ticket_type_id_ticket_date_inventory_id_key` ON `ticket_distribution`(`ticket_type_id`, `ticket_date_inventory_id`);

-- AddForeignKey
ALTER TABLE `ticket_date_inventory` ADD CONSTRAINT `ticket_date_inventory_ticket_section_id_fkey` FOREIGN KEY (`ticket_section_id`) REFERENCES `ticket_section`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ticket_distribution` ADD CONSTRAINT `ticket_distribution_ticket_date_inventory_id_fkey` FOREIGN KEY (`ticket_date_inventory_id`) REFERENCES `ticket_date_inventory`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
