import dayjs from 'dayjs';
import { Prisma } from '@prisma/client';
import { getAllInfoByISO } from 'iso-country-currency';

import { LanguageCode } from '@/shared/enums';

import db from '../client/index';

import { productSeed2 } from './seed.product2';
import { productSeed3 } from './seed.product3';
import { attributeSeed } from './seed.attributes';

export default async () => {
  const attributes = await attributeSeed();

  const currentTimeUNIX = () => dayjs().unix();
  const { currency } = getAllInfoByISO('HK');
  const convertToUnixTime = (year: number, month: number, day: number) => {
    const padding = (n: number) => `${n}`.padStart(2, '0');
    const date = new Date(`${year}-${padding(month)}-${padding(day)}T00:00:00.000+00:00`);
    return dayjs(date).unix();
  };

  const result = await db.event.findFirst({
    select: { id: true },
    where: { name: '鏈鋸人動畫展 - 香港' },
  });
  const countProducts = await db.products.count({
    where: {
      product_translations: {
        some: {
          language_code: LanguageCode.EN,
          name: 'Character Acrylic Coasters - Set of 8',
        },
      },
    },
  });

  const { id: eventId } = result ?? {};

  if (!!eventId && eventId !== 0 && countProducts === 0 && attributes) {
    // NOTE: Product Master
    const result = await db.products.create({
      data: {
        event_id: eventId,
        region: 'HK',
        is_active: true,
        status: 'listed',
        type: 'main',
        currency: currency,
        // default_sku_id: 1, // temp, update later
        pickup_start_date: convertToUnixTime(2025, 2, 27),
        pickup_end_date: convertToUnixTime(2025, 3, 5),
        shipping_methods: JSON.stringify(['storePickup', 'express']),
        sale_start_date: convertToUnixTime(2024, 12, 20),
        sale_end_date: convertToUnixTime(2024, 12, 27),
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      },
    });

    const { product_id: productId } = result;

    // NOTE: product translation
    await db.product_translations.createMany({
      data: [
        {
          product_id: productId,
          language_code: 'EN',
          name: 'Character Acrylic Coasters - Set of 8',
          description: 'Irure exercitation nulla tempor aliqua velit sunt.',
          introduction: '<p>Ea ea cillum aute qui aliqua culpa officia.</p>',
          tag: JSON.stringify(['Chainsaw Man', 'Animation Exhibition', 'Hong Kong']),
          pickup_venue: 'Mong Kok, Nathan Rd, Chong Hing Square, B1及B2',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          language_code: 'TC',
          name: '角色壓克力杯墊 - 全8種',
          description: 'Irure exercitation nulla tempor aliqua velit sunt.',
          introduction: '<p>Ea ea cillum aute qui aliqua culpa officia.</p>',
          tag: JSON.stringify(['鏈鋸人  ', '動畫展', '香港']),
          pickup_venue: '旺角創興廣場地庫B2 INCUBASE Arena',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          language_code: 'TS',
          name: '角色壓克力杯墊 - 全8種',
          description: 'Irure exercitation nulla tempor aliqua velit sunt.',
          introduction: '<p>Ea ea cillum aute qui aliqua culpa officia.</p>',
          tag: JSON.stringify(['鏈鋸人  ', '動畫展', '香港']),
          pickup_venue: '旺角創興廣場地庫B2 INCUBASE Arena',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
      ],
    });

    // NOTE: product sku
    await db.product_sku.createMany({
      data: [
        {
          product_id: productId,
          is_default: true,
          sku_code: 'CAC8S-LIGHTCORAL-S-6572',
          original_price: 100.0,
          sale_price: 90.0,
          cost_price: 10.0,
          tax: 0.0,
          weight: 1.27,
          volume: 56,
          length: 32,
          width: 26,
          height: 40,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CAC8S-LIGHTCORAL-M-6573',
          original_price: 120.0,
          sale_price: 95.0,
          cost_price: 15.0,
          tax: 0.0,
          weight: 3.27,
          volume: 516,
          length: 85,
          width: 15,
          height: 63,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CAC8S-LIGHTPINK-S-6581',
          original_price: 125.0,
          sale_price: 75.0,
          cost_price: 7.5,
          tax: 0.0,
          weight: 6.12,
          volume: 48,
          length: 58,
          width: 41,
          height: 85,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CAC8S-LIGHTPINK-M-6582',
          original_price: 125.0,
          sale_price: 75.0,
          cost_price: 7.5,
          tax: 0.0,
          weight: 6.12,
          volume: 48,
          length: 58,
          width: 41,
          height: 85,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
      ],
    });

    // NOTE: update product default sku
    const resultDefaultSKUByProductId = await db.product_sku.findFirst({
      select: { sku_id: true },
      where: { product_id: productId, is_default: true },
    });
    const { sku_id } = resultDefaultSKUByProductId ?? {};
    if (sku_id) {
      await db.products.update({
        data: { default_sku_id: sku_id },
        where: { product_id: productId },
      });
    }

    // NOTE: Product Media
    const resultProductSKU = await db.product_sku.findMany({
      select: { sku_id: true, sku_code: true },
      where: {
        sku_code: {
          in: [
            'CAC8S-LIGHTCORAL-S-6572',
            'CAC8S-LIGHTCORAL-M-6573',
            'CAC8S-LIGHTPINK-S-6581',
            'CAC8S-LIGHTPINK-M-6582',
          ],
        },
      },
    });
    const productMediaParameter = resultProductSKU.map<Prisma.product_mediaCreateManyInput>(
      ({ sku_id: skuId }, index) => ({
        product_id: productId,
        sku_id: skuId,
        media_type: 'image',
        is_main: true,
        url: 'url',
        priority: 1,
        is_active: true,
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      }),
    );
    await db.product_media.createMany({
      data: productMediaParameter,
    });

    // NOTE: product attributes
    const { product_attribute_id: attributeLightCoral } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.colorId,
        product_attribute_value_id: attributes.value.color.lightCoral,
      },
    });
    const { product_attribute_id: attributeLightPink } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.colorId,
        product_attribute_value_id: attributes.value.color.lightPink,
      },
    });
    const { product_attribute_id: attributeSizeS } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.sizeId,
        product_attribute_value_id: attributes.value.size.sizeS,
      },
    });
    const { product_attribute_id: attributeSizeM } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.sizeId,
        product_attribute_value_id: attributes.value.size.sizeM,
      },
    });

    const mapping: { [index: string]: number[] } = {
      'CAC8S-LIGHTCORAL-S-6572': [attributeLightCoral, attributeSizeS],
      'CAC8S-LIGHTCORAL-M-6573': [attributeLightCoral, attributeSizeM],
      'CAC8S-LIGHTPINK-S-6581': [attributeLightPink, attributeSizeS],
      'CAC8S-LIGHTPINK-M-6582': [attributeLightPink, attributeSizeM],
    };
    const parameterColor: Prisma.product_sku_attributesCreateManyInput[] = resultProductSKU.flatMap(
      ({ sku_id: skuId, sku_code: skuCode }) =>
        mapping[skuCode].map((attributeId) => ({
          sku_id: skuId,
          product_attribute_id: attributeId,
        })),
    );
    await db.product_sku_attributes.createMany({
      data: [...parameterColor],
    });

    // NOTE: product inventory
    const productInventory = resultProductSKU.map<Prisma.product_inventoryCreateManyInput>(
      ({ sku_id: skuId }) => ({
        sku_id: skuId,
        product_id: productId,
        total_quantity: Math.floor(Math.random() * 100) + 1,
        available_quantity: Math.floor(Math.random() * 90) + 1,
        reserved_quantity: Math.floor(Math.random() * 10) + 1,
        returned_quantity: 0,
        version: 1,
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      }),
    );
    await db.product_inventory.createMany({
      data: productInventory,
    });
  }

  await productSeed2();
  await productSeed3();

  console.log('[Prisma] Seed products insert progress.');
};
