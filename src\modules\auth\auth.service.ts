import db from '@prismaClient';
import { TokenRegisterRequestType } from './schema/auth.schema';
import { UserType } from '@shared/types/user.type';

export const registerToken = async ({
  token,
  userId,
  userEmail,
  userName,
}: TokenRegisterRequestType) => {
  await db.user.create({
    data: {
      user_ref_id: userId,
      name: userName,
      email: userEmail,
      token: token,
      password: '',
      isActive: true,
      role: 'visitor',
    },
  });
};

export const retrieveUserByToken = async (token: string): Promise<UserType | null> => {
  const getUserByToken = await db.user.findFirst({
    select: { id: true, user_ref_id: true, email: true, name: true },
    where: { token: token },
  });

  if (!getUserByToken) {
    return null;
  }

  return {
    userId: getUserByToken.id,
    userRefId: getUserByToken.user_ref_id,
    userEmail: getUserByToken.email,
    userName: getUserByToken.name ?? '',
  };
};
