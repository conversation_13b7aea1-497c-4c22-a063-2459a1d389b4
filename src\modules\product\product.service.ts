import { Prisma } from '@prisma/client';

import db from '@prismaClient';
import { Language, LanguageCode, SortOrder } from '@shared/enums';
import * as countryService from '@modules/country/country.service';
import { retrieveCurrencyByCountryCode } from '@modules/country/country.service';
import {
  convertCountryToTimezone,
  generateTimeNow,
  languageCodeSorter,
  todayWithinDateRange,
} from '@utils/common';
import { PrismaTransaction } from '@shared/types/prisma.type';

import {
  PrismaProductInventoryHoldStatus,
  PrismaProductMediaType,
  PrismaProductStatus,
  ProductStatusType,
} from './enums/product.enum';
import { ValidatorContext } from './policies/context/validator.context';
import { UseCase } from './enums/strategy.enum';
import { getAllInfoByISO } from 'iso-country-currency';
import * as productSchema from './schema/product.schema';

export const retrieveAllProducts = async (
  page: number,
  size: number,
  language: Language,
  sort?: string,
  sortOrder?: SortOrder,
  region?: string,
  eventId?: number,
) => {
  const result = await retrieveProductsWithCondition({
    page,
    size,
    language,
    sort,
    sortOrder,
    where: {
      ...(region ? { region } : {}),
      ...(eventId ? { event_id: eventId } : {}),
    },
  });

  const count = result.length;

  return { result, count: count };
};

export const retrieveSortFields = async () => {
  return {
    sortFields: ['productId', 'region', 'event', 'saleDate', 'pickupDate'],
    defaultSort: { productId: 1 },
  };
};

export const retrieveProductById = async (productId: number, language: Language) => {
  const item = await db.products.findFirst({
    select: {
      product_id: true,
      product_translations: {
        select: {
          language_code: true,
          name: true,
          description: true,
          introduction: true,
          pickup_venue: true,
        },
        where: { language_code: { in: ['EN', language] } },
      },
      sale_start_date: true,
      sale_end_date: true,
      category: true,
      region: true,
      defaultSKU: {
        select: {
          sku_id: true,
          original_price: true,
          sale_price: true,
          product_inventory: {
            select: {
              available_quantity: true,
            },
          },
          product_media: {
            select: {
              media_type: true,
              is_main: true,
              url: true,
              priority: true,
            },
            where: {
              is_active: true,
            },
          },
          product_sku_attributes: {
            select: {
              product_attributes: {
                select: {
                  product_attribute_categories: {
                    select: {
                      product_attribute_translation: {
                        select: { language_code: true, name: true },
                        where: { language_code: { in: ['EN', language] } },
                      },
                    },
                  },
                  product_attribute_values: {
                    select: {
                      product_attribute_translation: {
                        select: { language_code: true, name: true },
                        where: { language_code: { in: ['EN', language] } },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      event_id: true,
      event: {
        select: {
          name: true,
          name_en: true,
        },
      },
      pickup_start_date: true,
      pickup_end_date: true,
      shipping_methods: true,
      product_attributes: {
        select: {
          product_attribute_categories: {
            select: {
              product_attribute_translation: {
                select: { language_code: true, name: true },
                where: { language_code: { in: ['EN', language] } },
              },
            },
          },
          product_attribute_values: {
            select: {
              product_attribute_translation: {
                select: { language_code: true, name: true },
                where: { language_code: { in: ['EN', language] } },
              },
            },
          },
        },
      },
      product_sku: {
        select: {
          sku_id: true,
          original_price: true,
          sale_price: true,
          product_inventory: {
            select: {
              available_quantity: true,
            },
          },
          product_media: {
            select: {
              media_type: true,
              is_main: true,
              url: true,
              priority: true,
            },
            where: {
              is_active: true,
            },
          },
          product_sku_attributes: {
            select: {
              product_attributes: {
                select: {
                  product_attribute_categories: {
                    select: {
                      product_attribute_translation: {
                        select: { language_code: true, name: true },
                        where: { language_code: { in: ['EN', language] } },
                      },
                    },
                  },
                  product_attribute_values: {
                    select: {
                      product_attribute_translation: {
                        select: { language_code: true, name: true },
                        where: { language_code: { in: ['EN', language] } },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    where: { product_id: productId },
  });

  // NOTE: Mapper
  const productSKUMediaMapper = (productMedia: any, filterMediaType: string) => {
    return productMedia
      ?.filter(({ media_type: mediaType }: any) => mediaType === filterMediaType)
      ?.map(({ is_main: isMain, url: path, priority }: any) => ({
        isMain,
        path,
        priority,
      }));
  };

  // NOTE: Mapper
  const productSKUMapper = (productSKU: any[]) => {
    return productSKU.map((sku) => ({
      skuId: sku.sku_id,
      price: {
        originalPrice: sku?.original_price.toString(),
        salePrice: sku?.sale_price.toString(),
      },
      media: {
        image: productSKUMediaMapper(sku?.product_media, PrismaProductMediaType.image),
        video: productSKUMediaMapper(sku?.product_media, PrismaProductMediaType.video),
      },
      skuAttribute: skuAttributesMapper(sku?.product_sku_attributes ?? []),
      isAvailable: (sku?.product_inventory?.available_quantity ?? 0) > 0,
    }));
  };

  // NOTE: Mapper
  const defaultSKUAttributeMapper = (defaultSKU: any[]) => {
    return defaultSKU.map((x) => {
      const categoryName =
        x.product_attributes.product_attribute_values.product_attribute_translation.sort(
          languageCodeSorter,
        )?.[0]?.name;
      const valueName =
        x.product_attributes.product_attribute_categories.product_attribute_translation.sort(
          languageCodeSorter,
        )?.[0]?.name;

      return { category: categoryName, value: valueName };
    });
  };

  // NOTE: Mapper
  const skuAttributeMapper = (productSKU: any[]) => {
    return productSKU.reduce((a, { sku_id: skuId, product_sku_attributes: skuAttribute }) => {
      return {
        ...a,
        [skuId]: skuAttribute.map(
          ({
            product_attributes: {
              product_attribute_categories: {
                product_attribute_translation: categoriesTranslation,
              },
              product_attribute_values: { product_attribute_translation: valuesTranslation },
            },
          }: any) => {
            const category = categoriesTranslation.sort(languageCodeSorter)?.[0]?.name;
            const value = valuesTranslation.sort(languageCodeSorter)?.[0]?.name;
            return { category, value };
          },
        ),
      };
    }, {});
  };

  // NOTE: calculate product is available to sell (sell date, product_inventory?.available_quantity)
  // sell date base on region, in sell time slot
  // available_quantity > 0
  const productTimezone = convertCountryToTimezone(item?.region ?? '');
  const isWithinSaleDate = todayWithinDateRange(
    productTimezone[0],
    item?.sale_start_date ?? 0,
    item?.sale_end_date ?? 0,
  );
  const isInStock = (item?.defaultSKU?.product_inventory?.available_quantity ?? 0) > 0;
  const isProductAvailable = isWithinSaleDate && isInStock;

  // Get currency
  const currency = retrieveCurrencyByCountryCode(item?.region ?? '');

  // product transaction
  const productTransaction = item?.product_translations?.sort(languageCodeSorter)?.[0];

  // NOTE: Result mapping
  const result = {
    productId: item?.product_id,
    skuId: item?.defaultSKU?.sku_id,
    productName: productTransaction?.name,
    description: productTransaction?.description,
    productIntroduction: productTransaction?.introduction,
    productPickupVenue: productTransaction?.pickup_venue,
    saleStartDate: item?.sale_start_date,
    salesEndDate: item?.sale_end_date,
    category: item?.category,
    currency,
    price: {
      originalPrice: item?.defaultSKU?.original_price.toString(), // NOTE: using zod convert to number
      salePrice: item?.defaultSKU?.sale_price.toString(), // NOTE: using zod convert to number
    },
    event: {
      id: item?.event_id,
      name: ([LanguageCode.TC, LanguageCode.TS] as string[]).includes(language)
        ? item?.event?.name
        : item?.event?.name_en,
    },
    media: {
      image: productSKUMediaMapper(item?.defaultSKU?.product_media, PrismaProductMediaType.image),
      video: productSKUMediaMapper(item?.defaultSKU?.product_media, PrismaProductMediaType.video),
    },
    shipping: {
      pickupStartDate: item?.pickup_start_date,
      pickupEndDate: item?.pickup_end_date,
      shippingMethod: JSON.parse(item?.shipping_methods?.toString() ?? '{}'),
      pickupVenue: item?.product_translations?.[0]?.pickup_venue,
    },
    isAvailable: isProductAvailable,
    productAttributes: {
      default: defaultSKUAttributeMapper(item?.defaultSKU?.product_sku_attributes ?? []),
      skuAttributes: skuAttributeMapper(item?.product_sku ?? []),
      attributes: productAttributesMapper(item?.product_attributes ?? []),
    },
    // Other sku info
    productSKU: productSKUMapper(item?.product_sku ?? []),
  };

  return result;
};

export const retrieveRelatedProductsById = async (
  productId: number,
  language: Language = LanguageCode.EN,
) => {
  // Retrieve product's  eventId & region by productId
  const resultProductEventIdAndRegion = await db.products.findFirst({
    select: { event_id: true, region: true },
    where: { product_id: productId },
  });
  const { event_id: productEventId, region: productRegion } = resultProductEventIdAndRegion ?? {};

  // Retrieve product by event & region
  const result = await retrieveProductsWithCondition({
    language,
    where: {
      ...(productRegion ? { region: productRegion } : {}),
      ...(productEventId ? { event_id: productEventId } : {}),
    },
  });

  const count = result.length;

  return { result, count: count };
};

export const retrieveProductSearchCriteria = async (language: Language = LanguageCode.EN) => {
  const eventItems = await db.event.findMany({
    select: {
      id: true,
      name: true,
      name_en: true,
    },
    where: {
      products: {
        some: { status: PrismaProductStatus.listed, is_active: true },
      },
    },
  });

  const events = eventItems.map(({ id: eventId, name, name_en }) => ({
    eventId,
    eventName: ([LanguageCode.TC, LanguageCode.TS] as string[]).includes(language) ? name : name_en,
  }));

  // Retrieve existing listed products attribute
  const productAttributeItems = await db.product_attributes.findMany({
    select: {
      product_attribute_categories: {
        select: {
          product_attribute_translation: {
            select: { name: true, language_code: true },
            where: { language_code: { in: [language, 'EN'] } },
          },
        },
      },
      product_attribute_values: {
        select: {
          product_attribute_translation: {
            select: { name: true, language_code: true },
            where: { language_code: { in: [language, 'EN'] } },
          },
        },
      },
    },
  });
  const productAttributes = productAttributesMapper(productAttributeItems);

  // Retrieve existing listed products region
  const region = await countryService.retrieveCountryByListedProduct(language);

  return {
    events,
    productAttributes,
    region,
  };
};

// Retrieve products with condition
type RetrieveProductsWithConditionParameterType = {
  page?: number;
  size?: number;
  sort?: string;
  sortOrder?: SortOrder;
  language: Language;
  where: Prisma.productsWhereInput;
};
const retrieveProductsWithCondition = async ({
  page = 1,
  size = 2000,
  sort = 'productId',
  sortOrder = SortOrder.ASC,
  language = LanguageCode.EN,
  where,
}: RetrieveProductsWithConditionParameterType) => {
  const items = await db.products.findMany({
    skip: Math.max(0, page - 1) * size,
    take: Number(size),
    ...orderByMapping(sort, sortOrder),
    select: {
      product_id: true,
      product_translations: {
        select: { language_code: true, name: true },
        where: { language_code: { in: ['EN', language] } },
      },
      region: true,
      country: {
        select: {
          country_code: true,
          countryTranslations: {
            select: { language_code: true, name: true },
            where: { language_code: { in: ['EN', language] } },
          },
        },
      },
      sale_start_date: true,
      sale_end_date: true,
      defaultSKU: {
        select: {
          original_price: true,
          sale_price: true,
          product_inventory: {
            select: { available_quantity: true },
          },
          product_media: {
            select: { url: true },
            where: {
              media_type: 'image',
              is_active: true,
              is_main: true,
            },
            take: 1,
          },
        },
      },
      event_id: true,
      event: {
        select: {
          name: true,
          name_en: true,
        },
      },
    },
    where: {
      is_active: true,
      status: PrismaProductStatus.listed,
      ...where,
    },
  });

  // NOTE: mapping
  return items.map((item) => ({
    productId: item.product_id,
    productName: item.product_translations?.sort(languageCodeSorter)?.[0]?.name ?? '',
    thumbnail: item.defaultSKU?.product_media?.[0]?.url ?? '',
    region: item.region ?? '',
    saleStartDate: item.sale_start_date,
    saleEndDate: item.sale_end_date,
    country: {
      code: item.country.country_code,
      name: item.country.countryTranslations?.sort(languageCodeSorter)?.[0]?.name ?? '',
    },
    currency: retrieveCurrencyByCountryCode(item.country.country_code),
    event: {
      id: item.event_id,
      name: ([LanguageCode.TC, LanguageCode.TS] as string[]).includes(language)
        ? item.event?.name
        : item.event?.name_en,
    },
    price: Math.max(
      parseFloat(item.defaultSKU?.original_price?.toPrecision(2) ?? '0'),
      parseFloat(item.defaultSKU?.sale_price?.toPrecision(2) ?? '0'),
    ),
    inventory: {
      stock: (item.defaultSKU?.product_inventory?.available_quantity ?? '0')?.toString(),
    },
    isAvailable: (item.defaultSKU?.product_inventory?.available_quantity ?? 0) > 0,
  }));
};

// create orderby for prisma.products
const orderByMapping = (sort?: string, sortOrder?: SortOrder): Prisma.productsFindManyArgs => {
  const mapping: { [key: string]: any } = {
    productId: { orderBy: { product_id: sortOrder } },
    region: { orderBy: [{ region: sortOrder }, { product_id: 'asc' }] },
    event: { orderBy: [{ event_id: sortOrder }, { product_id: 'asc' }] },
    saleDate: { orderBy: [{ sale_start_date: sortOrder }, { product_id: 'asc' }] },
    pickupDate: { orderBy: [{ pickup_start_date: sortOrder }, { product_id: 'asc' }] },
  };
  return sort && sortOrder ? (mapping[sort] ?? {}) : {};
};

const productAttributesMapper = (productAttribute: any[]) => {
  /**
   * {
   *   color: ['red', 'blue'],
   *   size: ['S', 'M', 'L']
   *  }
   */
  const afterMap = productAttribute.reduce<{ [key: string]: [string] }>(
    (a: { [key: string]: [string] }, item) => {
      const { product_attribute_categories: categories, product_attribute_values: values } = item;

      const categoryTransaction = categories?.product_attribute_translation;
      const valueTransaction = values?.product_attribute_translation;
      const categoryName = categoryTransaction?.sort(languageCodeSorter)?.[0]?.name;
      const valueName = valueTransaction?.sort(languageCodeSorter)?.[0]?.name;

      if (!Object.keys(a).includes(categoryName)) {
        Object.assign(a, { [categoryName]: [valueName] });
      } else {
        // Check valueName duplicate
        if (!a[categoryName].includes(valueName)) {
          Object.assign(a, { [categoryName]: [...a[categoryName], valueName] });
        }
      }
      return a;
    },
    {},
  );

  /**
   * [
   *   { category: 'color', value: ['red', 'blue'] },
   *   { category: 'size', value: ['S', 'M', 'L'] }
   * ]
   */
  return Object.entries(afterMap).map(([k, v]) => ({ category: k, value: v }));
};

export const createProduct = async (product: productSchema.CreateProductRequestType) => {
  await ValidatorContext.execute(UseCase.addProduct, product);

  const now = generateTimeNow();
  const { currency } = getAllInfoByISO(product.region);

  await db.$transaction(async (tx) => {
    // NOTE: product master
    const { product_id: productId } = await tx.products.create({
      select: { product_id: true },
      data: {
        event_id: product.eventId,
        region: product.region,
        is_active: true,
        status: 'draft',
        type: 'main',
        currency: currency,
        pickup_start_date: product.pickupStartDate,
        pickup_end_date: product.pickupEndDate,
        shipping_methods: JSON.stringify(product.shippingMethod),
        sale_start_date: product.saleStartDate,
        sale_end_date: product.saleEndDate,
        created_at: now,
        updated_at: now,
        product_translations: {
          create: product.transaction.map(
            (productTransaction: productSchema.CreateProductTransactionType) => ({
              language_code: productTransaction.languageCode,
              name: productTransaction.name,
              description: productTransaction.description,
              introduction: productTransaction.introduction,
              tag: JSON.stringify(productTransaction.tag),
              pickup_venue: productTransaction.pickupVenue,
              created_at: now,
              updated_at: now,
            }),
          ),
        },
      },
    });

    // NOTE: product sku
    for (const sku of product.sku) {
      const productMedia = sku.media;
      const productInventory = sku.productInventory;
      const skuAttribute = sku.attribute;

      const { sku_id: skuId } = await tx.product_sku.create({
        select: { sku_id: true },
        data: {
          product_id: productId,
          is_default: sku.isDefault,
          sku_code: sku.skuCode,
          original_price: sku.originalPrice,
          sale_price: sku.salePrice,
          cost_price: sku.costPrice,
          tax: sku.tax,
          weight: sku.weight,
          volume: sku.volume,
          length: sku.length,
          width: sku.width,
          height: sku.height,
          created_at: now,
          updated_at: now,
          product_media: {
            createMany: {
              skipDuplicates: true,
              data: productMedia.map((media: productSchema.CreateProductMediaType) => ({
                product_id: productId,
                media_type: media.mediaType,
                is_main: media.isMain,
                url: media.url,
                priority: media.priority,
                is_active: true,
                created_at: now,
                updated_at: now,
              })),
            },
          },
          product_inventory: {
            create: {
              product_id: productId,
              total_quantity: productInventory.totalQuantity,
              available_quantity: productInventory.totalQuantity,
              reserved_quantity: 0,
              returned_quantity: 0,
              version: 1,
              created_at: now,
              updated_at: now,
            },
          },
        },
      });

      if (sku.isDefault) {
        await tx.products.update({
          data: { default_sku_id: skuId },
          where: { product_id: productId },
        });
      }

      type ProductAttribute = {
        productAttributeId: number;
        category: number;
        value: number;
      };
      const productAttributeCollection: ProductAttribute[] = [];
      for (const attribute of skuAttribute) {
        const category = attribute.category;
        const value = attribute.value;

        const count = await tx.product_attributes.count({
          where: {
            product_id: productId,
            product_attribute_category_id: attribute.category,
            product_attribute_value_id: attribute.value,
          },
        });
        if (count === 0) {
          const { product_attribute_id: productAttributeId } = await tx.product_attributes.create({
            select: {
              product_attribute_id: true,
              product_attribute_category_id: true,
              product_attribute_value_id: true,
            },
            data: {
              product_id: productId,
              product_attribute_category_id: attribute.category,
              product_attribute_value_id: attribute.value,
            },
          });
          productAttributeCollection.push({ productAttributeId, category, value });
        } else {
          const findProductAttribute = await tx.product_attributes.findFirstOrThrow({
            select: { product_attribute_id: true },
            where: {
              product_attribute_category_id: attribute.category,
              product_attribute_value_id: attribute.value,
            },
          });
          const { product_attribute_id: productAttributeId } = findProductAttribute;
          productAttributeCollection.push({ productAttributeId, category, value });
        }
      }

      await tx.product_sku_attributes.createMany({
        data: productAttributeCollection.map((productAttribute: ProductAttribute) => ({
          sku_id: skuId,
          product_attribute_id: productAttribute.productAttributeId,
        })),
      });
    }
  });
};

export const updateProductStatus = async (product: {
  productId: number;
  status: ProductStatusType;
}) => {
  // validation
  await ValidatorContext.execute(UseCase.updateProductStatus, {
    id: product.productId,
    status: product.status,
  });

  await db.products.update({
    data: { status: product.status },
    where: { product_id: product.productId },
  });
};

export const batchPublishProduct = async (
  parameter: productSchema.BatchUpdateProductStatusRequestType,
) => {
  // validation
  await ValidatorContext.execute(UseCase.batchUpdateProductStatus, parameter.batch);

  await db.$transaction(async (tx) => {
    for (const { id, status } of parameter.batch) {
      await tx.products.update({ data: { status }, where: { product_id: id } });
    }
  });
};

export const updateProductInventoryAfterOrderCreate = async (
  prisma: PrismaTransaction,
  {
    orderId,
    productId,
    skuId,
    quantity,
  }: { orderId: number; productId: number; skuId: number; quantity: number },
) => {
  const now = generateTimeNow();
  // Crete order, record the on hold reserve quantity
  await prisma.product_inventory_hold.create({
    data: {
      order_id: orderId,
      product_id: productId,
      sku_id: skuId,
      hold_quantity: quantity,
      hold_status: PrismaProductInventoryHoldStatus.hold,
      created_at: now,
      updated_at: now,
    },
  });
  // decrease available_quantity & reserved_quantity
  const { version } = await prisma.product_inventory.findUniqueOrThrow({
    select: { version: true },
    where: {
      product_id: productId,
      sku_id: skuId,
    },
  });

  await prisma.product_inventory.update({
    data: {
      available_quantity: { decrement: quantity },
      reserved_quantity: { increment: quantity },
      version: { increment: 1 },
      updated_at: now,
    },
    where: {
      product_id: productId,
      sku_id: skuId,
      version: version,
    },
  });
};

export const skuAttributesMapper = (productSKUAttributes: any[]) => {
  return productSKUAttributes.reduce<{ category: string; value: string }[]>((a, v) => {
    const cats = v.product_attributes.product_attribute_categories;
    const vals = v.product_attributes.product_attribute_values;
    const category = cats.product_attribute_translation?.sort(languageCodeSorter)?.[0].name;
    const value = vals.product_attribute_translation?.sort(languageCodeSorter)?.[0].name;

    return a.every((x) => x.category !== category) ? [...a, { category, value }] : a;
  }, []);
};

export const updateProductInventoryAfterOrderComplete = async (
  prisma: PrismaTransaction,
  {
    orderId,
    productId,
    skuId,
    quantity,
  }: { orderId: number; productId: number; skuId: number; quantity: number },
) => {
  const now = generateTimeNow();

  await prisma.product_inventory_hold.updateMany({
    data: { hold_status: PrismaProductInventoryHoldStatus.deducted, updated_at: now },
    where: { order_id: orderId, product_id: productId, sku_id: skuId },
  });

  const { version } = await prisma.product_inventory.findUniqueOrThrow({
    select: { version: true },
    where: { product_id: productId, sku_id: skuId },
  });
  await prisma.product_inventory.update({
    data: {
      total_quantity: { decrement: quantity },
      reserved_quantity: { decrement: quantity },
      version: { increment: 1 },
      updated_at: now,
    },
    where: { sku_id: skuId, version: version },
  });
};
