import dayjs from 'dayjs';

import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkPickupDateValidStrategy: Strategy = async (parameter: ParameterType) => {
  const { pickupStartDate, pickupEndDate } = parameter as CreateProductRequestType;

  const isPickupDateAfter = dayjs.unix(pickupEndDate).isAfter(dayjs.unix(pickupStartDate));

  if (!isPickupDateAfter) {
    throw new CustomErrorException(errorCodeTable.addProductInvalidPickupDate);
  }
};
