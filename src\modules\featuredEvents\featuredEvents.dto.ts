// import { z } from 'zod';

// const FeaturedEventDTOSchema = z.object({
//   eventName: z.string(),
//   description: z.string(),
//   eventStartDate: z.number(),
//   eventEndDate: z.number(),
//   region: z.string(),
//   timeZone: z.string(),
//   eventTime: z.string(),
//   aboutTime: z.string(),
//   thumbnail: z.string().optional(),
//   Venue: z.string().optional(),
//   category: z.string(),
//   createdAt: z.number().optional(),
//   updatedAt: z.number().optional().nullable(),
// });

// export type FeaturedEventDTO = z.infer<typeof FeaturedEventDTOSchema>;
