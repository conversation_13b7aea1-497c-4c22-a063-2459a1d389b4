import { Router } from 'express';

import adminProduct from './admin/product';
import adminEvent from './admin/event';
import adminTicket from './admin/ticket';
import adminOrder from './admin/order';

import publicAuth from './public/auth';
import publicProduct from './public/product';
import publicFeatureEvent from './public/featureEvent';
import publicLabPay from './public/labPay';
import publicCountry from './public/country';
import publicShoppingCart from './public/shoppingCart';
import publicEvent from './public/event';
import publicTicket from './public/ticket';
import publicOrder from './public/order';
import publicPayment from './public/payment';

const router = Router();

// Admin api
router.use('/admin/v1/products', adminProduct);
router.use('/admin/v1/order', adminOrder);
router.use('/admin/v1/event', adminEvent);
router.use('/admin/v1/ticket', adminTicket);
router.use('/admin/v1/country', publicCountry);

// public api
router.use('/public/v1/auth', publicAuth);
router.use('/public/v1/products', publicProduct);
router.use('/public/v1/featureEvent', publicFeatureEvent);
router.use('/public/v1/labPay', publicLabPay);
router.use('/public/v1/country', publicCountry);
router.use('/public/v1/shopping-cart', publicShoppingCart);
router.use('/public/v1/event', publicEvent);
router.use('/public/v1/ticket', publicTicket);
router.use('/public/v1/order', publicOrder);
router.use('/public/v1/payment', publicPayment);

export default router;
