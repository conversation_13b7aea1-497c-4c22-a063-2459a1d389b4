import _ from 'lodash';
import { errorHandler, handleAsync, jwtMiddleware, validateRequest } from '@middleware';
import * as ticketService from '@modules/ticket/ticket.service';
import { requestSchema } from '@/shared/schema/request.schema';

import * as ticketSchema from './ticket.dto';
import { uploader } from '@/middleware/file';
import { sendErrorResponse, sendSuccessResponse } from '@/utils/apiResponse';
import { JwtDto } from '@/models/commonDto';
import { StatusCodes } from 'http-status-codes';

// admin only
// create ticket setting
export const createTicketSetting = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.TicketSettingDto)),
  handleAsync(async (req, res) => {
    const {
      body,
    } = req

    const ticketSettingId = await ticketService.createTicketSetting(body);
    return sendSuccessResponse(res, {ticketSettingId});
  }),
];

// update ticket setting
export const updateTicketSetting = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.UpdateTicketSettingDto)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req
    const { id } = params;

    const ticketSettingId = await ticketService.updateTicketSetting(Number(id), body);
    return sendSuccessResponse(res, {ticketSettingId});
  }),
];

// ticket sections
export const createTicketSection = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.TicketSectionDto)),
  handleAsync(async (req, res) => {
    const {
      body,
    } = req

    await ticketService.createTicketSection(body);
    return sendSuccessResponse(res, {});
  }),
];

// ticket variation
export const createTicketVariation = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.TicketVariationDto)),
  handleAsync(async (req, res) => {
    const {
      body,
    } = req

    const ticketVariationId = await ticketService.createTicketVariation(body);
    return sendSuccessResponse(res, { ticketVariationId });
  }),
];

export const updateTicketVariation = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.UpdateTicketVariationDto)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req
    const {
      id
    } = params
    await ticketService.updateTicketVariation(Number(id), body);
    return sendSuccessResponse(res, {});
  }),
];

// ticket type
export const createTicketType = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.TicketTypeDto)),
  handleAsync(async (req, res) => {
    const {
      body,
    } = req

    const ticketTypeId = await ticketService.createTicketType(body);
    return sendSuccessResponse(res, { ticketTypeId });
  }),
];


export const updateTicketType = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.UpdateTicketTypeDto)),
  handleAsync(async (req, res) => {
    const {
      body,
      params
    } = req
    const { id } = params;

    await ticketService.updateTicketType(Number(id), body);
    return sendSuccessResponse(res, {});
  }),
];

export const exportTicket = [
  // jwtMiddleware,
  validateRequest(requestSchema.body(ticketSchema.ExportTicketDto)),
  handleAsync(async (req, res, next) => {
    const {
      body
    } = req
    // const { sub: userId } = JwtDto.parse(req.user);
    // TODO: auth the role of request
    const { file, name } = await ticketService.exportTicket(body)

    res.set({
      'Content-Type': 'application/zip',
      'Content-Disposition': `attachment; filename=${name}`,
      'Content-Length': file.length,
    });
    return res.send(file);
  }),
]

// public
export const listTicket = [
  // jwtMiddleware,
  validateRequest(requestSchema.query(ticketSchema.ListTicketDto)),
  handleAsync(async (req, res) => {
    const {
      query
    } = req
    const { sub: userId } = JwtDto.parse(req.user);

    const tickets = await ticketService.listUserTicket(userId, query)
    return sendSuccessResponse(res, { tickets });
  }),
];

export const getTicket = [
  // jwtMiddleware,
  validateRequest(requestSchema.query(ticketSchema.GetTicketDto)),
  handleAsync(async (req, res) => {
    const {
      query
    } = req
    const { sub: userId } = JwtDto.parse(req.user);
    const { ticketId } = query

    const tickets = await ticketService.getTicket(userId, ticketId)
    return sendSuccessResponse(res, { tickets });
  }),
];

export const validateTicket = [
  // jwtMiddleware,
  validateRequest(requestSchema.params(ticketSchema.ValidateTicketDto)),
  handleAsync(async (req, res, next) => {
    const {
      params
    } = req
    const { sub: userId } = JwtDto.parse(req.user);
    // TODO: auth the role of request
    const { code } = params

    const ticket = await ticketService.validateTicket(code)
    return sendSuccessResponse(res, { ticket });
  }),
];