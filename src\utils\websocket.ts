import { Server, WebSocketServer, WebSocket } from 'ws';
import { logger } from './logger';
import url from 'url';

class WebSocketService {
  private connection: { [id: string]: WebSocket[] };
  private ws: WebSocketServer;

  constructor(ws: WebSocketServer) {
    this.ws = ws;
    this.ws.on('connection', (event: WebSocket, rest: any) => this.onConnection(event, rest));
    this.ws.on('disconnection', () => this.onDisconnection);
    this.connection = {};

    this.onConnection = this.onConnection.bind(this);
  }

  async onConnection(event: WebSocket, rest: any) {
    const parsedUrl = url.parse(rest.url, true);
    if (parsedUrl?.pathname === '/order') {
      event.on('message', async (data) => {
        logger.info('[received message >> ] >> ' + data.toString());
      });
      if (!this.connection[parsedUrl?.query?.id as string])
        this.connection[parsedUrl?.query?.id as string] = [event];
      else this.connection[parsedUrl?.query?.id as string].push(event);
    }
  }

  public sendOrderMessage(id: string, message: string = 'done') {
    logger.info(`[websocket] >> order: ${id} >> ${message}`);
    this.connection[id]?.forEach((socket) => {
      socket.send(`${message}`);
      socket.terminate();
    });
  }

  private onDisconnection() {}
}

export const wss = new WebSocketService(new WebSocketServer({ port: 8080 }));
