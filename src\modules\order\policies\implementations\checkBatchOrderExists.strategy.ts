import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { OrderCheckStrategyParameterType } from '@modules/order/schema/strategy.schema';
import { OrderCheckStrategy } from '@modules/order/policies/abstractions/orderCheck.interface';

type ParameterType = OrderCheckStrategyParameterType;
type Strategy = OrderCheckStrategy<ParameterType>;

export const checkBatchOrderExistStrategy: Strategy = async (parameter: ParameterType) => {
  if ('orderIds' in parameter) {
    const { orderIds } = parameter;

    if (orderIds?.length) {
      const countOrder = await db.orders.count({
        where: { order_id: { in: orderIds } },
      });

      if (countOrder !== orderIds.length) {
        throw new CustomErrorException(errorCodeTable.updateOrderShippingOrderIdNotExists);
      }
    }
  }
};
