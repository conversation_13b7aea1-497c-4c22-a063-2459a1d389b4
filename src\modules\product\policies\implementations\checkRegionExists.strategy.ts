import db from '@prismaClient';
import { ProductCheckStrategyParameterType } from '@modules/product/schema/strategy.schema';
import { CreateProductRequestType } from '@modules/product/schema/product.schema';
import { ProductCheckStrategy } from '../abstractions/productCheck.interface';
import { CustomErrorException, errorCodeTable } from '@/errors';

type ParameterType = ProductCheckStrategyParameterType;
type Strategy = ProductCheckStrategy<ParameterType>;

export const checkRegionStrategy: Strategy = async (parameter: ParameterType) => {
  const { region } = parameter as CreateProductRequestType;

  const country = await db.country.findUnique({
    where: { country_code: region },
  });

  if (!country) {
    throw new CustomErrorException(errorCodeTable.addProductInvalidRegion);
  }
};
