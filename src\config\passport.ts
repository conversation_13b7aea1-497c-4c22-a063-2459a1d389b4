import passport from 'passport';
import * as bearer from 'passport-http-bearer';

import * as authService from '@modules/auth/auth.service';

const BearerStrategy = bearer.Strategy;

// Check user token is exists.
passport.use(
  new BearerStrategy(async (token: string, done: any) => {
    try {
      const user = await authService.retrieveUserByToken(token);
      if (!user) {
        return done(null, false);
      }
      done(null, user);
    } catch (err) {
      console.error(1000, err);
      done(err);
    }
  }),
);

export default passport;
