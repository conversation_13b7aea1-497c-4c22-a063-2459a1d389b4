/*
  Warnings:

  - You are about to drop the column `country_name_en` on the `country` table. All the data in the column will be lost.
  - You are about to drop the column `country_name_tc` on the `country` table. All the data in the column will be lost.
  - You are about to drop the column `country_name_ts` on the `country` table. All the data in the column will be lost.
  - You are about to alter the column `language_code` on the `product_attribute_translation` table. The data in that column could be lost. The data in that column will be cast from `Enum(EnumId(8))` to `VarChar(5)`.
  - Made the column `language_code` on table `product_translations` required. This step will fail if there are existing NULL values in that column.

*/

-- CreateTable
CREATE TABLE `country_translations` (
    `country_translations_id` INTEGER NOT NULL AUTO_INCREMENT,
    `country_code` VARCHAR(3) NOT NULL,
    `language_code` VARCHAR(5) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `created_at` INTEGER NOT NULL,
    `updated_at` INTEGER NOT NULL,

    INDEX `idx_language_code`(`country_code`, `language_code`),
    UNIQUE INDEX `uk_country_language`(`country_code`, `language_code`),
    PRIMARY KEY (`country_translations_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `country_translations` ADD CONSTRAINT `country_translations_country_code_fkey` FOREIGN KEY (`country_code`) REFERENCES `country`(`country_code`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE `country` DROP COLUMN `country_name_en`,
    DROP COLUMN `country_name_tc`,
    DROP COLUMN `country_name_ts`;

-- AlterTable
ALTER TABLE `product_attribute_translation` MODIFY `language_code` VARCHAR(5) NOT NULL;

-- AlterTable
ALTER TABLE `product_translations` MODIFY `language_code` VARCHAR(5) NOT NULL;
