import * as bcrypt from 'bcryptjs';
import crypto from 'crypto';
import jwt, { JwtPayload } from 'jsonwebtoken';

const alphaCap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const alpha = 'abcdefghijklmnopqrstuvwxyz';
const numeric = '0123456789';
const specialChar = '!@#$&*_-';

const hashPassword = async (password: string) => {
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  return hashedPassword;
};

const generatePassword = async (length: number = 8) => {
  return crypto.randomBytes(length).toString('base64url');
};

const generateSecurityPassword = async (length: number = 8) => {
  const chars = [alphaCap, alpha, numeric, specialChar];
  const randInt = (this_max: number) => {
    const umax = Math.pow(2, 32);
    const max = umax - (umax % this_max);
    const r = new Uint32Array(1);
    do {
      crypto.getRandomValues(r);
    } while (r[0] > max);
    return r[0] % this_max;
  };

  const randPassword = [
    [1, 1, 5, 1]
      .map((len, i) => {
        return new Array(len)
          .fill(chars[i])
          .map((x) =>
            ((chars) => {
              const umax = Math.pow(2, 32),
                r = new Uint32Array(1),
                max = umax - (umax % chars.length);
              do {
                crypto.getRandomValues(r);
              } while (r[0] > max);
              return chars[r[0] % chars.length];
            })(x),
          )
          .join('');
      })
      .join(''),
  ].map((s) => {
    const arr = s.split('');
    for (let i = 0, n = arr.length; i < n - 2; i++) {
      const j = randInt(n - i);
      [arr[j], arr[i]] = [arr[i], arr[j]];
    }
    return arr.join('');
  })[0];
  return randPassword;
};

const verifyPassword = async (password: string, hashedPassword: string) => {
  const validPassword = await bcrypt.compare(password, hashedPassword);

  return validPassword;
};

const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000);
};

const generateUniqueID = () => {
  return crypto.randomBytes(20).toString('hex');
};

const secretKey = () => process.env.JWT_SECRET || '';

const signAccessToken = (payload: Partial<string | JwtPayload>) => {
  return jwt.sign(payload, secretKey(), { expiresIn: '1h' });
};

const signRefreshToken = (payload: Partial<string | JwtPayload>) => {
  return jwt.sign(payload, secretKey());
};

const verifyToken = (token: string) => {
  return jwt.verify(token, secretKey());
};

const generateJWT = (payload: Partial<string | JwtPayload>) => {
  let options = {}
  if (typeof payload === 'object') options = {expiresIn: '1h'}
  return jwt.sign(payload, secretKey(), options);
};

const verifyJWT = verifyToken

export {
  verifyPassword,
  hashPassword,
  generateOTP,
  generateUniqueID,
  signAccessToken,
  signRefreshToken,
  verifyToken,
  generatePassword,
  generateSecurityPassword,
  generateJWT,
  verifyJWT,
  alphaCap,
  alpha,
  numeric,
  specialChar,
};
