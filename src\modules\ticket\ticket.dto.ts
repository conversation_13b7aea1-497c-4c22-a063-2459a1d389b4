import { z } from 'zod';
import { $Enums } from '@prisma/client';
import { PaginationDto } from '@/models/commonDto';

const EventId = z.number()

const TicketSettingField = z.object({
    saleStartDatetime: z.number().optional(),
    saleEndDatetime: z.number().optional(),
    currency: z.nativeEnum($Enums.Currency),
    saleTimeThershold: z.number().optional(),
})

export const TicketSettingDto = z.object({
    ...TicketSettingField.shape,
    eventId: EventId
})
export type TicketSettingType = z.infer<typeof TicketSettingDto>;

export const UpdateTicketSettingDto = TicketSettingField
export type UpdateTicketSettingType = z.infer<typeof UpdateTicketSettingDto>;

const TicketSectionField = z.object({
    ticketSettingId: z.number(),
    sections: z.array(z.object({
        id: z.number().optional(),
        day: z.number().optional(),
        date: z.number().optional(),
        startTime: z.number(),
        endTime: z.number(),
        quantity: z.number(),
        seq: z.number(),
        deleteSection: z.boolean().optional(),
    }))
})

export const TicketSectionDto = TicketSectionField

export type TicketSectionType = z.infer<typeof TicketSectionDto>;

const TicketVariationField = z.object({
    ticketSettingId: z.number(),
    name: z.string(),
    // type: z.array(z.string()),
    value: z.array(z.string()).optional(),
    timeSlot: z.array(z.object({
        from: z.number(),
        to: z.number(),
    })).optional()
})

export const TicketVariationDto = TicketVariationField
export const UpdateTicketVariationDto = z.object({
    name: z.string().optional(),
    variations: z.array(z.object({
        id: z.number().optional(),
        value: z.string().optional(),
        timeSlot: z.object({
            from: z.number(),
            to: z.number(),
        }).optional(),
        deleteVariation: z.boolean().optional()
    }))
})

export type TicketVariationType = z.infer<typeof TicketVariationDto>;
export type UpdateTicketVariationType = z.infer<typeof UpdateTicketVariationDto>;

const TicketTypeField = z.object({
    ticketSettingId: z.number(),
    sellingLocation: z.number().default(1),
    thirdPartyName: z.string().optional(),
    sellingType: z.number().default(1),
    multiply: z.number().optional(),
    name: z.string(),
    description: z.string(),
    isAllDay: z.boolean().default(false),
    preSaleStartTime: z.number().optional(),
    preSaleEndTime: z.number().optional(),
    price: z.number().optional(),
    priceUnit: z.string().optional(),
    allowMultipleEntry: z.boolean().default(false),
    haveGift: z.boolean().default(false),
    
    ticketSections: z.array(z.number()),
    ticketVariation: z.array(z.number()),
    ticketGift: z.array(z.number()),
})

export const TicketTypeDto = TicketTypeField
export const UpdateTicketTypeDto = TicketTypeField.partial()

export type TicketTypeType = z.infer<typeof TicketTypeDto>;
export type UpdateTicketTypeType = z.infer<typeof UpdateTicketTypeDto>;

export const ExportTicketDto = z.object({
    branch: z.array(z.object({
        ticketTypeId: z.number(),
        ticketDateInventoryId: z.number(),
        ticketSectionId: z.number(),
        quantity: z.number()
    }))
})
export type ExportTicketDtoType = z.infer<typeof ExportTicketDto>

export const ListTicketDto = z.object({
    ...PaginationDto.shape,
    search: z.string().optional()
})
export type ListTicketDtoType = z.infer<typeof ListTicketDto>

export const GetTicketDto = z.object({
    ticketId: z.number()
})
export type GetTicketDtoType = z.infer<typeof GetTicketDto>

export const ValidateTicketDto = z.object({
    code: z.string()
})
export type ValidateTicketDtoType = z.infer<typeof ValidateTicketDto>

