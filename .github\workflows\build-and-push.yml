name: <PERSON>uild, Push and Deploy to E<PERSON>

on:
  push:
    branches:
      - 'main'
      - 'releases/sit'
      - 'releases/preprd'
      - 'releases/prd'
      - 'refs/heads/releases/sit'
      - 'refs/heads/releases/preprd'
      - 'refs/heads/releases/prd'
  workflow_dispatch:

env:
  ECR_DOMAIN: ${{secrets.VEXMETA_ECR_AWS_ACCOUNT_ID}}.dkr.ecr.${{secrets.VEXMETA_ECR_AWS_REGION}}.amazonaws.com
  ECR_BASE_REPOSITORY: incutix-nodejs-api
  IMAGE_TAG: ${{ github.sha }}
  K8S_CLUSTER_NAME: vex-runtime-server
  K8S_BASE_NAMESPACE_NAME: incutix
  K8S_WORKLOAD_NAME: incutix-nodejs-api

jobs:
  # Debug job to print GitHub context
  debug:
    runs-on: ubuntu-latest
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ to<PERSON><PERSON>(github) }}
        run: |
          echo "GitHub Context: $GITHUB_CONTEXT"
          echo "Branch ref: ${{ github.ref }}"
          echo "Is releases/prd? ${{ github.ref == 'refs/heads/releases/prd' }}"
          echo "Starts with release/prd? ${{ startsWith(github.ref, 'refs/heads/releases/prd') }}"

  # Job to bump version on main branch
  bump-version:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
      
      - name: Bump version and commit
        id: bump-version
        run: |
          # Get current version
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "Current version: $CURRENT_VERSION"
          
          # Parse version components
          MAJOR=$(echo $CURRENT_VERSION | cut -d. -f1)
          MINOR=$(echo $CURRENT_VERSION | cut -d. -f2)
          PATCH=$(echo $CURRENT_VERSION | cut -d. -f3)
          
          # Get the last commit message
          COMMIT_MSG=$(git log -1 --pretty=%B)
          echo "Commit message: $COMMIT_MSG"
          
          # Determine which version component to bump based on commit message
          if echo "$COMMIT_MSG" | grep -i -E "add|new|migrate" > /dev/null; then
            # Bump major version
            NEW_MAJOR=$((MAJOR + 1))
            NEW_MINOR=0
            NEW_PATCH=0
            VERSION_TYPE="major"
          elif echo "$COMMIT_MSG" | grep -i -E "update" > /dev/null; then
            # Bump minor version
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$((MINOR + 1))
            NEW_PATCH=0
            VERSION_TYPE="minor"
          else
            # Default: Bump patch version (for 'fix', 'debug', 'patch', or any other message)
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$MINOR
            NEW_PATCH=$((PATCH + 1))
            VERSION_TYPE="patch"
          fi
          
          NEW_VERSION="$NEW_MAJOR.$NEW_MINOR.$NEW_PATCH"
          echo "Bumping $VERSION_TYPE version to: $NEW_VERSION"
          
          # Update package.json with new version
          sed -i "s/\"version\": \"$CURRENT_VERSION\"/\"version\": \"$NEW_VERSION\"/g" package.json
          
          # Commit the change
          git add package.json
          git commit -m "Bump version to $NEW_VERSION [skip ci]"
          git push origin HEAD:${GITHUB_REF#refs/heads/}
          
          # Set output and env variables
          echo "VERSION=$NEW_VERSION" >> $GITHUB_ENV
          echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT

  # Job to build and push to ECR on release branches
  build-and-push:
    if: startsWith(github.ref, 'refs/heads/releases/')
    runs-on: ubuntu-latest
    outputs:
      ecr_full_path: ${{ steps.build-push.outputs.ecr_full_path }}
      image_version: ${{ steps.extract-version.outputs.version }}
      registry_uri: ${{ steps.login-ecr.outputs.registry }}
      environment: ${{ steps.set-environment.outputs.environment }}
      namespace_suffix: ${{ steps.set-environment.outputs.namespace_suffix }}
      ecr_repository: ${{ steps.set-environment.outputs.ecr_repository }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set environment variables
        id: set-environment
        run: |
          # Determine environment and namespace suffix based on branch
          if [[ "${{ github.ref }}" == "refs/heads/releases/sit" ]]; then
            ENVIRONMENT="sit"
            NAMESPACE_SUFFIX="private"
            ECR_REPO_SUFFIX="sit"
          elif [[ "${{ github.ref }}" == "refs/heads/releases/preprd" ]]; then
            ENVIRONMENT="preprd"
            NAMESPACE_SUFFIX="public"
            ECR_REPO_SUFFIX="preprd"
          elif [[ "${{ github.ref }}" == "refs/heads/releases/prd" ]]; then
            ENVIRONMENT="prd"
            NAMESPACE_SUFFIX="public"
            ECR_REPO_SUFFIX="prd"
          else
            echo "ERROR: Unsupported branch for deployment: ${{ github.ref }}"
            exit 1
          fi

          # Construct ECR repository name with suffix
          ECR_REPOSITORY="${{ env.ECR_BASE_REPOSITORY }}-${ECR_REPO_SUFFIX}"

          echo "environment=${ENVIRONMENT}" >> $GITHUB_OUTPUT
          echo "namespace_suffix=${NAMESPACE_SUFFIX}" >> $GITHUB_OUTPUT
          echo "ecr_repo_suffix=${ECR_REPO_SUFFIX}" >> $GITHUB_OUTPUT
          echo "ecr_repository=${ECR_REPOSITORY}" >> $GITHUB_OUTPUT

          echo "Environment: ${ENVIRONMENT}"
          echo "Namespace suffix: ${NAMESPACE_SUFFIX}"
          echo "ECR repository: ${ECR_REPOSITORY}"
      
      - name: Extract version from package.json
        id: extract-version
        run: |
          # Get version from package.json
          VERSION=$(node -p "require('./package.json').version")
          echo "Package version: $VERSION"

          # Set output and env variables
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_ECR_AWS_REGION }}

      - name: Install Podman
        run: |
          sudo apt-get update
          sudo apt-get install -y podman
          podman --version
      
      - name: Login to Amazon ECR
        id: login-ecr
        run: |
          ECR_REPOSITORY="${{ steps.set-environment.outputs.ecr_repository }}"

          aws ecr get-login-password --region ${{ secrets.VEXMETA_ECR_AWS_REGION }} | podman login --username AWS --password-stdin ${{ env.ECR_DOMAIN }}
          echo "registry=$(aws ecr describe-repositories --repository-names ${ECR_REPOSITORY} --query 'repositories[0].repositoryUri' --output text || aws ecr create-repository --repository-name ${ECR_REPOSITORY} --query 'repository.repositoryUri' --output text)" >> $GITHUB_OUTPUT

      - name: Build and push image with Podman
        id: build-push
        run: |
          # Get registry URI and version
          REGISTRY_URI="${{ steps.login-ecr.outputs.registry }}"
          VERSION="${{ steps.extract-version.outputs.version }}"
          ECR_REPOSITORY="${{ steps.set-environment.outputs.ecr_repository }}"
          ENVIRONMENT="${{ steps.set-environment.outputs.environment }}"

          # Validate inputs
          if [ -z "$REGISTRY_URI" ]; then
            echo "ERROR: Registry URI is empty"
            exit 1
          fi

          if [ -z "$VERSION" ]; then
            echo "ERROR: Version is empty"
            exit 1
          fi

          # Define the full image path (repository name includes environment suffix)
          FULL_IMAGE_PATH="${REGISTRY_URI}:v${VERSION}"
          echo "Building and pushing image: $FULL_IMAGE_PATH"
          echo "Environment: ${ENVIRONMENT}"
          echo "ECR Repository: ${ECR_REPOSITORY}"

          # Build the image
          echo "Building Docker image..."
          podman build -t $FULL_IMAGE_PATH .

          # Push the image
          echo "Pushing image to ECR..."
          podman push $FULL_IMAGE_PATH

          # Verify the push was successful
          echo "Verifying image was pushed successfully..."
          aws ecr describe-images --repository-name ${ECR_REPOSITORY} --image-ids imageTag=v${VERSION} --region ${{ secrets.VEXMETA_ECR_AWS_REGION }}

          # Set outputs for next job
          echo "ecr_full_path=$FULL_IMAGE_PATH" >> $GITHUB_OUTPUT

          echo "Build and push completed successfully!"
          echo "Image: $FULL_IMAGE_PATH"

  # Job to deploy to EKS
  deploy-to-eks:
    needs: build-and-push
    if: startsWith(github.ref, 'refs/heads/releases/')
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code for fallback method
        uses: actions/checkout@v3

      - name: Set deployment environment
        id: set-deploy-env
        run: |
          # Determine environment and namespace suffix based on branch
          if [[ "${{ github.ref }}" == "refs/heads/releases/sit" ]]; then
            ENVIRONMENT="sit"
            NAMESPACE_SUFFIX="private"
          elif [[ "${{ github.ref }}" == "refs/heads/releases/preprd" ]]; then
            ENVIRONMENT="preprd"
            NAMESPACE_SUFFIX="public"
          elif [[ "${{ github.ref }}" == "refs/heads/releases/prd" ]]; then
            ENVIRONMENT="prd"
            NAMESPACE_SUFFIX="public"
          else
            echo "ERROR: Unsupported branch for deployment: ${{ github.ref }}"
            exit 1
          fi

          K8S_NAMESPACE_NAME="${K8S_BASE_NAMESPACE_NAME}-${NAMESPACE_SUFFIX}"

          echo "environment=${ENVIRONMENT}" >> $GITHUB_OUTPUT
          echo "namespace_suffix=${NAMESPACE_SUFFIX}" >> $GITHUB_OUTPUT
          echo "k8s_namespace_name=${K8S_NAMESPACE_NAME}" >> $GITHUB_OUTPUT

          echo "=== EKS DEPLOYMENT STARTING ==="
          echo "Environment: ${ENVIRONMENT}"
          echo "Cluster: ${K8S_CLUSTER_NAME}"
          echo "Namespace: ${K8S_NAMESPACE_NAME}"
          echo "Deployment: ${K8S_WORKLOAD_NAME}"
          echo "Image: ${{ needs.build-and-push.outputs.ecr_full_path }}"
          echo "Version: ${{ needs.build-and-push.outputs.image_version }}"
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "==============================="
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_K8S_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_K8S_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_K8S_AWS_REGION }}
      
      - name: Install eksctl
        run: |
          # Download and install eksctl
          curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
          sudo mv /tmp/eksctl /usr/local/bin
          # Verify installation
          eksctl version
          
      - name: Connect to EKS cluster
        run: |
          K8S_NAMESPACE_NAME="${{ steps.set-deploy-env.outputs.k8s_namespace_name }}"

          echo "Connecting to EKS cluster: ${K8S_CLUSTER_NAME}"

          # Update kubeconfig
          aws eks update-kubeconfig --name ${K8S_CLUSTER_NAME} --region ${{ secrets.VEXMETA_K8S_AWS_REGION }}

          # Verify connection
          echo "Verifying cluster connection..."
          kubectl cluster-info

          # Check if namespace exists
          echo "Checking namespace: ${K8S_NAMESPACE_NAME}"
          if ! kubectl get namespace ${K8S_NAMESPACE_NAME} >/dev/null 2>&1; then
            echo "WARNING: Namespace ${K8S_NAMESPACE_NAME} does not exist"
            echo "Please create the namespace through Rancher dashboard first"
            exit 1
          fi

          echo "Successfully connected to EKS cluster"
      
      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Validate deployment prerequisites
        run: |
          K8S_NAMESPACE_NAME="${{ steps.set-deploy-env.outputs.k8s_namespace_name }}"
          ENVIRONMENT="${{ steps.set-deploy-env.outputs.environment }}"

          echo "Validating deployment prerequisites..."
          echo "Environment: $ENVIRONMENT"
          echo "Namespace: $K8S_NAMESPACE_NAME"
          echo "Deployment: $K8S_WORKLOAD_NAME"

          # Check if deployment exists
          if ! kubectl get deployment ${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME} >/dev/null 2>&1; then
            echo "ERROR: Deployment ${K8S_WORKLOAD_NAME} does not exist in namespace ${K8S_NAMESPACE_NAME}"
            echo "Available deployments in namespace:"
            kubectl get deployments -n ${K8S_NAMESPACE_NAME} || echo "No deployments found"
            echo ""
            echo "Please create the deployment through Rancher dashboard first with the following specifications:"
            echo "  - Name: ${K8S_WORKLOAD_NAME}"
            echo "  - Namespace: ${K8S_NAMESPACE_NAME}"
            echo "  - Container name: ${K8S_WORKLOAD_NAME}"
            echo "  - Initial image: any valid image (will be updated by this workflow)"
            echo "  - Environment: ${ENVIRONMENT}"
            exit 1
          fi

          echo "Deployment validation passed!"
          kubectl get deployment ${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME}
      
      - name: Deploy to EKS
        run: |
          # Get environment variables
          K8S_NAMESPACE_NAME="${{ steps.set-deploy-env.outputs.k8s_namespace_name }}"
          ENVIRONMENT="${{ steps.set-deploy-env.outputs.environment }}"

          # Get components from previous job outputs
          REGISTRY_URI="${{ needs.build-and-push.outputs.registry_uri }}"
          IMAGE_VERSION="${{ needs.build-and-push.outputs.image_version }}"
          ECR_FULL_PATH="${{ needs.build-and-push.outputs.ecr_full_path }}"

          # Debug output
          echo "Retrieved from previous job:"
          echo "  - Environment: $ENVIRONMENT"
          echo "  - Namespace: $K8S_NAMESPACE_NAME"
          echo "  - REGISTRY_URI: $REGISTRY_URI"
          echo "  - IMAGE_VERSION: $IMAGE_VERSION"
          echo "  - ECR_FULL_PATH: $ECR_FULL_PATH"

          # Validate that we have the required information
          if [ -z "$ECR_FULL_PATH" ]; then
            echo "Warning: ECR_FULL_PATH is empty, trying to construct from components"

            # Method 1: Use registry_uri and image_version outputs
            if [ -n "$REGISTRY_URI" ] && [ -n "$IMAGE_VERSION" ]; then
              ECR_FULL_PATH="${REGISTRY_URI}:v${IMAGE_VERSION}"
              echo "Method 1: Constructed ECR_FULL_PATH: $ECR_FULL_PATH"
            # Method 2: Use package.json as fallback
            elif [ -f "package.json" ]; then
              VERSION="$(cat package.json | grep version | head -1 | awk -F: '{ print $2 }' | sed 's/[\"\, ]//g')"
              ECR_REPOSITORY="${{ needs.build-and-push.outputs.ecr_repository }}"
              ECR_FULL_PATH="${{ env.ECR_DOMAIN }}/${ECR_REPOSITORY}:v$VERSION"
              echo "Method 2: Using package.json version: $ECR_FULL_PATH"
            else
              echo "ERROR: Could not determine ECR image path using any available method"
              echo "Both Method 1 (outputs from previous job) and Method 2 (package.json) failed"
              exit 1
            fi
          fi

          # Validate final ECR path
          if [ -z "$ECR_FULL_PATH" ]; then
            echo "ERROR: ECR_FULL_PATH is still empty after all attempts"
            exit 1
          fi

          echo "Deploying to EKS:"
          echo "  - Cluster: ${K8S_CLUSTER_NAME}"
          echo "  - Namespace: ${K8S_NAMESPACE_NAME}"
          echo "  - Deployment: ${K8S_WORKLOAD_NAME}"
          echo "  - Image: $ECR_FULL_PATH"

          # Update deployment image
          echo "Updating deployment image..."
          kubectl set image deployment/${K8S_WORKLOAD_NAME} ${K8S_WORKLOAD_NAME}=${ECR_FULL_PATH} -n ${K8S_NAMESPACE_NAME}

          # Verify deployment with rollback on failure
          echo "Waiting for deployment to complete..."
          if kubectl rollout status deployment/${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME} --timeout=300s; then
            echo "Deployment completed successfully!"
            kubectl get deployment ${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME}
            kubectl get pods -l app=${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME}
          else
            echo "ERROR: Deployment failed or timed out"
            echo "Rolling back to previous version..."
            kubectl rollout undo deployment/${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME}
            echo "Waiting for rollback to complete..."
            kubectl rollout status deployment/${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME} --timeout=180s
            echo "Rollback completed. Current deployment status:"
            kubectl get deployment ${K8S_WORKLOAD_NAME} -n ${K8S_NAMESPACE_NAME}
            exit 1
          fi

      - name: Post-deployment summary
        if: always()
        run: |
          K8S_NAMESPACE_NAME="${{ steps.set-deploy-env.outputs.k8s_namespace_name }}"
          ENVIRONMENT="${{ steps.set-deploy-env.outputs.environment }}"

          echo "=== DEPLOYMENT SUMMARY ==="
          echo "Environment: ${ENVIRONMENT}"
          echo "Cluster: ${K8S_CLUSTER_NAME}"
          echo "Namespace: ${K8S_NAMESPACE_NAME}"
          echo "Deployment: ${K8S_WORKLOAD_NAME}"
          echo "Image: ${{ needs.build-and-push.outputs.ecr_full_path }}"
          echo "Version: ${{ needs.build-and-push.outputs.image_version }}"
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "=========================="
