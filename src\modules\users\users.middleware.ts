import { NextFunction, Response, Request } from 'express';
import { ForbiddenException } from '../../exceptions/HttpException';
import { IdDto, JwtDto } from '../../models/commonDto';

export const userByIdMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const { id } = IdDto.parse(req.params);
  const { sub } = JwtDto.parse(req.user);

  if (id !== sub) {
    throw new ForbiddenException();
  }

  next();
};
