import db from '../client/index';

export default async () => {
  const currentDateTime = Math.round(new Date().valueOf() / 1000);
  const convertToTime = (time: string) => new Date(`1970-01-01T${time}.000+00:00`);
  const convertToUnixTime = (year: number, month: number, day: number) => {
    const padding = (n: number) => `${n}`.padStart(2, '0');
    const date = new Date(`${year}-${padding(month)}-${padding(day)}T00:00:00.000+00:00`);
    return Math.round(date.valueOf() / 1000);
  };

  const result = await db.event.findFirst({
    where: {
      name: '鏈鋸人動畫展 - 香港',
    },
  });

  if (Object.entries(result ?? {}).length === 0) {
    const result = await db.event.create({
      data: {
        name: '鏈鋸人動畫展 - 香港',
        name_en: 'Chainsaw Man Animation Exhibition - Hong Kong',
        description: '',
        region_code: 'HK',
        timezone: 'HKT',
        start_date: convertToUnixTime(2025, 4, 24),
        end_date: convertToUnixTime(2025, 5, 18),
        start_time: convertToUnixTime(2025, 3, 1),
        end_time: convertToUnixTime(2025, 3, 10),
        duration: 1,
        venue: 'Exhibition Space, 4/F, Kai Tak Mall 2, Hong Kong',
        status: 'ACTIVE',
        created_at: currentDateTime,
        updated_at: currentDateTime,
      },
    });

    const { id: eventId } = result;
  }

  console.log('[Prisma] Seed events insert progress.');
};
