import { Prisma } from '@prisma/client';
import { ZodError } from 'zod';
import { logger } from '../utils/logger';
import { errorCodeTable } from './customErrorCode';

export const errorHandler = (err: any) => {
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    logger.error(`[Prisma error] ${err.stack}`);
  } else if (err instanceof ZodError) {
    logger.error(`[Zod error] ${err.stack}`);
  } else if (err instanceof Error) {
    logger.error(`[Custom error] ${err.message}`);
  } else {
    logger.error(`[Unknown error] ${err.stack || errorCodeTable.badRequest.message}`);
  }
};
