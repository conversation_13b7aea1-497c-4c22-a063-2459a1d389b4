# GitHub Actions EKS Multi-Environment Deployment Setup

## Overview
The GitHub Actions workflow has been enhanced to support multi-environment deployments to AWS EKS with environment-specific namespace routing. The workflow now supports UAT, Pre-Production, and Production environments with proper namespace segregation, error handling, validation, and rollback capabilities.

## Environment Configuration

The workflow now supports three environments with automatic namespace routing:

| Branch | Environment | Namespace | ECR Repository | Image Tag | Description |
|--------|-------------|-----------|----------------|-----------|-------------|
| `release/uat` | UAT | `incutix-private` | `incutix-nodejs-api-dev` | `v{version}` | User Acceptance Testing |
| `release/preprd` | Pre-Prod | `incutix-public` | `incutix-nodejs-api-preprd` | `v{version}` | Pre-Production |
| `release/prd` | Production | `incutix-public` | `incutix-nodejs-api-prd` | `v{version}` | Production |

## Key Changes Made

### 1. Multi-Environment Support
- **Environment Detection**: Automatic environment detection based on branch name
- **Namespace Routing**: Dynamic namespace assignment (`private` for UAT, `public` for Pre-Prod/Prod)
- **ECR Repository Naming**: Environment-specific repository names (e.g., `incutix-nodejs-api-dev`, `incutix-nodejs-api-prd`)
- **Consistent Versioning**: Standard version tagging across all environments (`v1.1.2`)

### 2. Fixed Naming Consistency
- **ECR Repository**: Changed from `incutix-netcore-api-mock` to `incutix-nodejs-api`
- **Kubernetes Deployment**: Changed from `incutix-netcore-api-mock-docs` to `incutix-nodejs-api`
- **Container Name**: Now consistently uses `incutix-nodejs-api`

### 3. Improved Job Output Handling
- Fixed job dependencies and output passing between `build-and-push` and `deploy-to-eks` jobs
- Added proper validation of job outputs
- Implemented fallback mechanisms for image path construction

### 4. Enhanced Error Handling
- Added validation steps for all prerequisites
- Implemented automatic rollback on deployment failure
- Added comprehensive error messages with actionable instructions
- Environment-specific error reporting

### 5. Better Deployment Process
- Added pre-deployment validation to check if deployment exists
- Improved cluster connection verification
- Added namespace existence checks
- Enhanced deployment status monitoring
- Environment-aware deployment validation

### 6. Added Monitoring and Logging
- Pre-deployment information display with environment details
- Post-deployment summary (runs always, even on failure)
- Detailed logging throughout the process
- Environment-specific logging

## Prerequisites for Rancher Dashboard Setup

Before the GitHub Actions workflow can successfully deploy, you need to create the following resources through Rancher Dashboard for each environment:

### 1. Namespaces
Create the following namespaces in your EKS cluster:

#### UAT Environment
- **Name**: `incutix-private`
- **Cluster**: `vex-runtime-server`

#### Pre-Production & Production Environments
- **Name**: `incutix-public`
- **Cluster**: `vex-runtime-server`

### 2. Deployments
Create deployments in each namespace:

#### For UAT (incutix-private namespace)
- **Name**: `incutix-nodejs-api`
- **Namespace**: `incutix-private`
- **Container Name**: `incutix-nodejs-api`
- **Initial Image**: Any valid Node.js image (will be updated by workflow)
- **Ports**:
  - Container Port: 5000
  - Container Port: 8080

#### For Pre-Prod & Prod (incutix-public namespace)
- **Name**: `incutix-nodejs-api`
- **Namespace**: `incutix-public`
- **Container Name**: `incutix-nodejs-api`
- **Initial Image**: Any valid Node.js image (will be updated by workflow)
- **Ports**:
  - Container Port: 5000
  - Container Port: 8080

### 3. Services (Optional but Recommended)
Create services for each environment:

#### UAT Service
- **Name**: `incutix-nodejs-api-service`
- **Namespace**: `incutix-private`
- **Type**: ClusterIP or LoadBalancer (based on your needs)
- **Selector**: `app=incutix-nodejs-api`
- **Ports**:
  - Port 5000 → Target Port 5000
  - Port 8080 → Target Port 8080

#### Pre-Prod & Prod Service
- **Name**: `incutix-nodejs-api-service`
- **Namespace**: `incutix-public`
- **Type**: ClusterIP or LoadBalancer (based on your needs)
- **Selector**: `app=incutix-nodejs-api`
- **Ports**:
  - Port 5000 → Target Port 5000
  - Port 8080 → Target Port 8080

## Required GitHub Secrets

Ensure the following secrets are configured in your GitHub repository:

### ECR Access (for building and pushing images)
- `VEXMETA_ECR_AWS_ACCOUNT_ID`: Your AWS account ID
- `VEXMETA_ECR_AWS_ACCESS_KEY_ID`: AWS access key for ECR operations
- `VEXMETA_ECR_AWS_SECRET_ACCESS_KEY`: AWS secret key for ECR operations
- `VEXMETA_ECR_AWS_REGION`: AWS region (e.g., ap-southeast-1)

### EKS Access (for deployment)
- `VEXMETA_K8S_AWS_ACCESS_KEY_ID`: AWS access key for EKS operations
- `VEXMETA_K8S_AWS_SECRET_ACCESS_KEY`: AWS secret key for EKS operations
- `VEXMETA_K8S_AWS_REGION`: AWS region for EKS cluster

## Workflow Triggers

The workflow runs on:
1. **Push to `main` branch**: Bumps version in package.json
2. **Push to `release/uat` branch**: Builds, pushes to ECR with `-uat` suffix, and deploys to UAT environment
3. **Push to `release/preprd` branch**: Builds, pushes to ECR with `-preprd` suffix, and deploys to Pre-Prod environment
4. **Push to `release/prd` branch**: Builds, pushes to ECR with `-prd` suffix, and deploys to Production environment
5. **Manual trigger**: Via GitHub Actions UI (workflow_dispatch)

## Deployment Process

### For Main Branch (Development)
1. Commit changes to `main` branch
2. Workflow automatically bumps version in `package.json`
3. Version bump is committed back to repository

### For Environment Deployments

#### UAT Deployment
1. Create/push to `release/uat` branch
2. Workflow automatically:
   - Extracts version from `package.json`
   - Builds Docker image using existing Dockerfile
   - Pushes image to ECR repository `incutix-nodejs-api-dev` with tag `v{version}`
   - Connects to EKS cluster
   - Validates deployment prerequisites in `incutix-private` namespace
   - Updates deployment with new image
   - Monitors rollout status
   - Rolls back automatically if deployment fails

#### Pre-Production Deployment
1. Create/push to `release/preprd` branch
2. Workflow automatically:
   - Extracts version from `package.json`
   - Builds Docker image using existing Dockerfile
   - Pushes image to ECR repository `incutix-nodejs-api-preprd` with tag `v{version}`
   - Connects to EKS cluster
   - Validates deployment prerequisites in `incutix-public` namespace
   - Updates deployment with new image
   - Monitors rollout status
   - Rolls back automatically if deployment fails

#### Production Deployment
1. Create/push to `release/prd` branch
2. Workflow automatically:
   - Extracts version from `package.json`
   - Builds Docker image using existing Dockerfile
   - Pushes image to ECR repository `incutix-nodejs-api-prd` with tag `v{version}`
   - Connects to EKS cluster
   - Validates deployment prerequisites in `incutix-public` namespace
   - Updates deployment with new image
   - Monitors rollout status
   - Rolls back automatically if deployment fails

## Monitoring and Troubleshooting

### Success Indicators
- All workflow steps complete successfully
- Deployment shows "Deployment completed successfully!"
- Post-deployment summary displays correct information

### Common Issues and Solutions

1. **"Namespace does not exist"**
   - Create the `incutix-private` namespace in Rancher Dashboard

2. **"Deployment does not exist"**
   - Create the `incutix-nodejs-api` deployment in Rancher Dashboard
   - Ensure the deployment name matches exactly

3. **"Registry URI is empty"**
   - Check ECR secrets are correctly configured
   - Verify ECR repository exists or can be created

4. **"Deployment failed or timed out"**
   - Check pod logs in Rancher Dashboard
   - Verify image can be pulled from ECR
   - Check resource limits and requests

### Viewing Deployment Status
You can monitor the deployment through:
- GitHub Actions logs
- Rancher Dashboard
- kubectl commands (if you have cluster access)

## Next Steps

1. **Create the required resources in Rancher Dashboard** as outlined above
2. **Verify GitHub secrets** are properly configured
3. **Test the workflow** by pushing to `release/prd` branch
4. **Monitor the deployment** through GitHub Actions and Rancher Dashboard

## Support

If you encounter issues:
1. Check the GitHub Actions logs for detailed error messages
2. Verify all prerequisites are met
3. Check Rancher Dashboard for deployment status
4. Review the ECR repository for pushed images
