import dayjs from 'dayjs';
import { Prisma } from '@prisma/client';
import { getAllInfoByISO } from 'iso-country-currency';

import { LanguageCode } from '@/shared/enums';

import db from '../client/index';
import { attributeSeed } from './seed.attributes';

export const productSeed2 = async () => {
  const attributes = await attributeSeed();

  const currentTimeUNIX = () => dayjs().unix();
  const { currency } = getAllInfoByISO('HK');
  const convertToUnixTime = (year: number, month: number, day: number) => {
    const padding = (n: number) => `${n}`.padStart(2, '0');
    const date = new Date(`${year}-${padding(month)}-${padding(day)}T00:00:00.000+00:00`);
    return dayjs(date).unix();
  };

  const result = await db.event.findFirst({
    select: { id: true },
    where: { name: '鏈鋸人動畫展 - 香港' },
  });
  const duplicateProduct = await db.products.count({
    where: {
      product_translations: {
        some: {
          language_code: LanguageCode.EN,
          name: 'Character Cards - All 22',
        },
      },
    },
  });

  const { id: eventId } = result ?? {};

  if (!!eventId && eventId !== 0 && duplicateProduct === 0 && attributes) {
    // NOTE: Product Master
    const result = await db.products.create({
      data: {
        event_id: eventId,
        region: 'HK',
        is_active: true,
        status: 'listed',
        type: 'main',
        currency: currency,
        // default_sku_id: 1, // temp, update later
        pickup_start_date: convertToUnixTime(2025, 6, 1),
        pickup_end_date: convertToUnixTime(2025, 6, 14),
        shipping_methods: JSON.stringify(['storePickup', 'express']),
        sale_start_date: convertToUnixTime(2024, 5, 16),
        sale_end_date: convertToUnixTime(2024, 5, 30),
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      },
    });

    const { product_id: productId } = result;

    // NOTE: product translation
    await db.product_translations.createMany({
      data: [
        {
          product_id: productId,
          language_code: 'EN',
          name: 'Character Cards - All 22',
          description:
            'Officia dolor voluptate esse magna commodo velit cupidatat irure consectetur.',
          introduction:
            '<p>Laboris do velit elit incididunt anim culpa culpa consectetur dolore fugiat laboris quis ut.</p>',
          tag: JSON.stringify(['Chainsaw Man', 'Animation Exhibition', 'Hong Kong']),
          pickup_venue: 'Mong Kok, Nathan Rd, Chong Hing Square, B1及B2',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          language_code: 'TC',
          name: '角色卡片 - 全22種',
          description:
            'Officia dolor voluptate esse magna commodo velit cupidatat irure consectetur.',
          introduction:
            '<p>Laboris do velit elit incididunt anim culpa culpa consectetur dolore fugiat laboris quis ut.</p>',
          tag: JSON.stringify(['鏈鋸人  ', '動畫展', '香港']),
          pickup_venue: '旺角創興廣場地庫B2 INCUBASE Arena',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          language_code: 'TS',
          name: '角色卡片 - 全22種',
          description:
            'Officia dolor voluptate esse magna commodo velit cupidatat irure consectetur.',
          introduction:
            '<p>Laboris do velit elit incididunt anim culpa culpa consectetur dolore fugiat laboris quis ut.</p>',
          tag: JSON.stringify(['鏈鋸人  ', '動畫展', '香港']),
          pickup_venue: '旺角創興廣場地庫B2 INCUBASE Arena',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
      ],
    });

    // NOTE: product sku
    await db.product_sku.createMany({
      data: [
        {
          product_id: productId,
          is_default: true,
          sku_code: 'CCA22-MEDIUMORCHID-M-1001',
          original_price: 66.0,
          sale_price: 44.0,
          cost_price: 15.0,
          tax: 0.0,
          weight: 5.4,
          volume: 7.0,
          length: 9.0,
          width: 23,
          height: 8,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CCA22-MEDIUMORCHID-L-1002',
          original_price: 146.0,
          sale_price: 11.0,
          cost_price: 2.0,
          tax: 0.0,
          weight: 5.69,
          volume: 45,
          length: 12,
          width: 24,
          height: 25,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CCA22-LIGHTPINK-M-1011',
          original_price: 120.0,
          sale_price: 99.0,
          cost_price: 7.5,
          tax: 0.0,
          weight: 4.23,
          volume: 23,
          length: 32,
          width: 89,
          height: 98,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CCA22-LIGHTPINK-L-1012',
          original_price: 120.0,
          sale_price: 99.0,
          cost_price: 7.5,
          tax: 0.0,
          weight: 4.23,
          volume: 23,
          length: 32,
          width: 89,
          height: 98,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
      ],
    });

    // NOTE: update product default sku
    const resultDefaultSKUByProductId = await db.product_sku.findFirst({
      select: { sku_id: true },
      where: { product_id: productId, is_default: true },
    });
    const { sku_id } = resultDefaultSKUByProductId ?? {};
    if (sku_id) {
      await db.products.update({
        data: { default_sku_id: sku_id },
        where: { product_id: productId },
      });
    }

    // NOTE: Product Media
    const resultProductSKU = await db.product_sku.findMany({
      select: { sku_id: true, sku_code: true },
      where: {
        sku_code: {
          in: [
            'CCA22-MEDIUMORCHID-M-1001',
            'CCA22-MEDIUMORCHID-L-1002',
            'CCA22-LIGHTPINK-M-1011',
            'CCA22-LIGHTPINK-L-1012',
          ],
        },
      },
    });
    const productMediaParameter = resultProductSKU.map<Prisma.product_mediaCreateManyInput>(
      ({ sku_id: skuId }, index) => ({
        product_id: productId,
        sku_id: skuId,
        media_type: 'image',
        is_main: true,
        url: 'url',
        priority: index + 1,
        is_active: true,
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      }),
    );
    await db.product_media.createMany({
      data: productMediaParameter,
    });

    // NOTE: product attributes
    const { product_attribute_id: attributeMediumOrchid } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.colorId,
        product_attribute_value_id: attributes.value.color.mediumOrchid,
      },
    });
    const { product_attribute_id: attributeLightPink } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.colorId,
        product_attribute_value_id: attributes.value.color.lightPink,
      },
    });
    const { product_attribute_id: attributeSizeM } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.sizeId,
        product_attribute_value_id: attributes.value.size.sizeM,
      },
    });
    const { product_attribute_id: attributeSizeL } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.sizeId,
        product_attribute_value_id: attributes.value.size.sizeL,
      },
    });

    const mapping: { [index: string]: number[] } = {
      'CCA22-MEDIUMORCHID-M-1001': [attributeMediumOrchid, attributeSizeM],
      'CCA22-MEDIUMORCHID-L-1002': [attributeMediumOrchid, attributeSizeL],
      'CCA22-LIGHTPINK-M-1011': [attributeLightPink, attributeSizeM],
      'CCA22-LIGHTPINK-L-1012': [attributeLightPink, attributeSizeL],
    };
    const parameterColor: Prisma.product_sku_attributesCreateManyInput[] = resultProductSKU.flatMap(
      ({ sku_id: skuId, sku_code: skuCode }) =>
        mapping[skuCode].map((attributeId) => ({
          sku_id: skuId,
          product_attribute_id: attributeId,
        })),
    );
    await db.product_sku_attributes.createMany({
      data: [...parameterColor],
    });

    // NOTE: product inventory
    const productInventory = resultProductSKU.map<Prisma.product_inventoryCreateManyInput>(
      ({ sku_id: skuId }) => ({
        sku_id: skuId,
        product_id: productId,
        total_quantity: Math.floor(Math.random() * 100) + 1,
        available_quantity: Math.floor(Math.random() * 90) + 1,
        reserved_quantity: Math.floor(Math.random() * 10) + 1,
        returned_quantity: 0,
        version: 1,
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      }),
    );
    await db.product_inventory.createMany({
      data: productInventory,
    });
  }

  console.log('[Prisma] Seed products insert progress.');
};
