import dayjs from 'dayjs';
import { Prisma } from '@prisma/client';
import { getAllInfoByISO } from 'iso-country-currency';

import { LanguageCode } from '@/shared/enums';

import db from '../client/index';
import { attributeSeed } from './seed.attributes';

export const productSeed3 = async () => {
  const attributes = await attributeSeed();

  const currentTimeUNIX = () => dayjs().unix();
  const { currency } = getAllInfoByISO('HK');
  const convertToUnixTime = (year: number, month: number, day: number) => {
    const padding = (n: number) => `${n}`.padStart(2, '0');
    const date = new Date(`${year}-${padding(month)}-${padding(day)}T00:00:00.000+00:00`);
    return dayjs(date).unix();
  };

  const result = await db.event.findFirst({
    select: { id: true },
    where: { name: '鏈鋸人動畫展 - 香港' },
  });

  const duplicateProduct = await db.products.count({
    where: {
      product_translations: {
        some: {
          language_code: LanguageCode.EN,
          name: 'Character Acrylic Magnets - All 8 Types',
        },
      },
    },
  });

  const { id: eventId } = result ?? {};

  if (!!eventId && eventId !== 0 && duplicateProduct === 0 && attributes) {
    // NOTE: Product Master
    const result = await db.products.create({
      data: {
        event_id: eventId,
        region: 'HK',
        is_active: true,
        status: 'listed',
        type: 'main',
        currency: currency,
        // default_sku_id: 1, // temp, update later
        pickup_start_date: convertToUnixTime(2026, 1, 10),
        pickup_end_date: convertToUnixTime(2026, 1, 24),
        shipping_methods: JSON.stringify(['storePickup', 'express']),
        sale_start_date: convertToUnixTime(2026, 2, 7),
        sale_end_date: convertToUnixTime(2026, 2, 21),
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      },
    });

    const { product_id: productId } = result;

    // NOTE: product translation
    await db.product_translations.createMany({
      data: [
        {
          product_id: productId,
          language_code: 'EN',
          name: 'Character Acrylic Magnets - All 8 Types',
          description:
            'Excepteur ea non incididunt voluptate minim Lorem consequat occaecat do adipisicing proident ut culpa adipisicing.',
          introduction:
            '<p>Excepteur ea non incididunt voluptate minim Lorem consequat occaecat do adipisicing proident ut culpa adipisicing.</p>',
          tag: JSON.stringify(['Chainsaw Man', 'Animation Exhibition', 'Hong Kong']),
          pickup_venue: 'Mong Kok, Nathan Rd, Chong Hing Square, B1及B2',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          language_code: 'TC',
          name: '角色壓克力磁鐵 - 全8種',
          description:
            'Excepteur ea non incididunt voluptate minim Lorem consequat occaecat do adipisicing proident ut culpa adipisicing.',
          introduction:
            '<p>Excepteur ea non incididunt voluptate minim Lorem consequat occaecat do adipisicing proident ut culpa adipisicing.</p>',
          tag: JSON.stringify(['鏈鋸人  ', '動畫展', '香港']),
          pickup_venue: '旺角創興廣場地庫B2 INCUBASE Arena',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          language_code: 'TS',
          name: '角色壓克力磁鐵 - 全8種',
          description:
            'Excepteur ea non incididunt voluptate minim Lorem consequat occaecat do adipisicing proident ut culpa adipisicing.',
          introduction:
            '<p>Excepteur ea non incididunt voluptate minim Lorem consequat occaecat do adipisicing proident ut culpa adipisicing.</p>',
          tag: JSON.stringify(['鏈鋸人  ', '動畫展', '香港']),
          pickup_venue: '旺角創興廣場地庫B2 INCUBASE Arena',
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
      ],
    });

    // NOTE: product sku
    await db.product_sku.createMany({
      data: [
        {
          product_id: productId,
          is_default: true,
          sku_code: 'CAMA8-LIGHTCORAL-S-8765',
          original_price: 100 + Math.round(Math.random() * 10000) / 100,
          sale_price: Math.round(Math.random() * 10000) / 100,
          cost_price: Math.round(Math.random() * 5000) / 100,
          tax: 0.0,
          weight: Math.round(Math.random() * 1000) / 100,
          volume: Math.round(Math.random() * 10000) / 100,
          length: Math.round(Math.random() * 10000) / 100,
          width: Math.round(Math.random() * 10000) / 100,
          height: Math.round(Math.random() * 10000) / 100,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CAMA8-LIGHTCORAL-L-4485',
          original_price: 100 + Math.round(Math.random() * 10000) / 100,
          sale_price: Math.round(Math.random() * 10000) / 100,
          cost_price: Math.round(Math.random() * 5000) / 100,
          tax: 0.0,
          weight: Math.round(Math.random() * 1000) / 100,
          volume: Math.round(Math.random() * 10000) / 100,
          length: Math.round(Math.random() * 10000) / 100,
          width: Math.round(Math.random() * 10000) / 100,
          height: Math.round(Math.random() * 10000) / 100,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CAMA8-MEDIUMORCHID-S-9972',
          original_price: 100 + Math.round(Math.random() * 10000) / 100,
          sale_price: Math.round(Math.random() * 10000) / 100,
          cost_price: Math.round(Math.random() * 5000) / 100,
          tax: 0.0,
          weight: Math.round(Math.random() * 1000) / 100,
          volume: Math.round(Math.random() * 10000) / 100,
          length: Math.round(Math.random() * 10000) / 100,
          width: Math.round(Math.random() * 10000) / 100,
          height: Math.round(Math.random() * 10000) / 100,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
        {
          product_id: productId,
          is_default: false,
          sku_code: 'CAMA8-MEDIUMORCHID-L-9972',
          original_price: 100 + Math.round(Math.random() * 10000) / 100,
          sale_price: Math.round(Math.random() * 10000) / 100,
          cost_price: Math.round(Math.random() * 5000) / 100,
          tax: 0.0,
          weight: Math.round(Math.random() * 1000) / 100,
          volume: Math.round(Math.random() * 10000) / 100,
          length: Math.round(Math.random() * 10000) / 100,
          width: Math.round(Math.random() * 10000) / 100,
          height: Math.round(Math.random() * 10000) / 100,
          created_at: currentTimeUNIX(),
          updated_at: currentTimeUNIX(),
        },
      ],
    });

    // NOTE: update product default sku
    const resultDefaultSKUByProductId = await db.product_sku.findFirst({
      select: { sku_id: true },
      where: { product_id: productId, is_default: true },
    });
    const { sku_id } = resultDefaultSKUByProductId ?? {};
    if (sku_id) {
      await db.products.update({
        data: { default_sku_id: sku_id },
        where: { product_id: productId },
      });
    }

    // NOTE: Product Media
    const resultProductSKU = await db.product_sku.findMany({
      select: { sku_id: true, sku_code: true },
      where: {
        sku_code: {
          in: [
            'CAMA8-LIGHTCORAL-S-8765',
            'CAMA8-LIGHTCORAL-L-4485',
            'CAMA8-MEDIUMORCHID-S-9972',
            'CAMA8-MEDIUMORCHID-L-9973',
          ],
        },
      },
    });
    const productMediaParameter = resultProductSKU.map<Prisma.product_mediaCreateManyInput>(
      ({ sku_id: skuId }, index) => ({
        product_id: productId,
        sku_id: skuId,
        media_type: 'image',
        is_main: true,
        url: 'url',
        priority: index + 1,
        is_active: true,
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      }),
    );
    await db.product_media.createMany({
      data: productMediaParameter,
    });

    // NOTE: product attributes
    const { product_attribute_id: attributeLightCoral } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.colorId,
        product_attribute_value_id: attributes.value.color.lightCoral,
      },
    });
    const { product_attribute_id: attributeMediumOrchid } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.colorId,
        product_attribute_value_id: attributes.value.color.mediumOrchid,
      },
    });
    const { product_attribute_id: attributeSizeS } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.sizeId,
        product_attribute_value_id: attributes.value.size.sizeS,
      },
    });
    const { product_attribute_id: attributeSizeL } = await db.product_attributes.create({
      data: {
        product_id: productId,
        product_attribute_category_id: attributes.category.sizeId,
        product_attribute_value_id: attributes.value.size.sizeL,
      },
    });

    const mapping: { [index: string]: number[] } = {
      'CAMA8-LIGHTCORAL-S-8765': [attributeLightCoral, attributeSizeS],
      'CAMA8-LIGHTCORAL-L-4485': [attributeLightCoral, attributeSizeL],
      'CAMA8-MEDIUMORCHID-S-9972': [attributeMediumOrchid, attributeSizeS],
      'CAMA8-MEDIUMORCHID-L-9973': [attributeMediumOrchid, attributeSizeL],
    };
    const parameterColor: Prisma.product_sku_attributesCreateManyInput[] = resultProductSKU.flatMap(
      ({ sku_id: skuId, sku_code: skuCode }) =>
        mapping[skuCode].map((attributeId) => ({
          sku_id: skuId,
          product_attribute_id: attributeId,
        })),
    );
    await db.product_sku_attributes.createMany({
      data: [...parameterColor],
    });

    // NOTE: product inventory
    const productInventory = resultProductSKU.map<Prisma.product_inventoryCreateManyInput>(
      ({ sku_id: skuId }) => ({
        sku_id: skuId,
        product_id: productId,
        total_quantity: Math.floor(Math.random() * 100) + 1,
        available_quantity: Math.floor(Math.random() * 90) + 1,
        reserved_quantity: Math.floor(Math.random() * 10) + 1,
        returned_quantity: 0,
        version: 1,
        created_at: currentTimeUNIX(),
        updated_at: currentTimeUNIX(),
      }),
    );
    await db.product_inventory.createMany({
      data: productInventory,
    });
  }

  console.log('[Prisma] Seed products insert progress.');
};
