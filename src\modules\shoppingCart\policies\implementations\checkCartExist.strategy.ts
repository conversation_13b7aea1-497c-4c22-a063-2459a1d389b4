import db from '@prismaClient';
import { CustomErrorException, errorCodeTable } from '@errors';
import { generateTimeNow } from '@utils/common';
import { CartCheckStrategyParameterType } from '@modules/shoppingCart/schema/strategy.schema';
import { CartCheckStrategy } from '@modules/shoppingCart/policies/abstractions/cartCheck.interface';

type ParameterType = CartCheckStrategyParameterType;
type Strategy = CartCheckStrategy<ParameterType>;

export const checkCartExistStrategy: Strategy = async (parameter: ParameterType) => {
  const { userId } = parameter;

  // Check the cart is exists
  const countShoppingCart = await db.shopping_cart.findUnique({
    select: { cart_id: true },
    where: { user_id: userId, expires_at: { gte: generateTimeNow() } },
  });

  if (!countShoppingCart || (countShoppingCart && !countShoppingCart.cart_id)) {
    throw new CustomErrorException(errorCodeTable.cartNotExist);
  }
};
