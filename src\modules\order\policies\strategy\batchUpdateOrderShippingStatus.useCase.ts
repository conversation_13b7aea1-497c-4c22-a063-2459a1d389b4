import { OrderCheckStrategy } from '@modules/order/policies/abstractions/orderCheck.interface';
import { OrderCheckStrategyParameterType } from '@modules/order/schema/strategy.schema';

import { checkBatchOrderExistStrategy } from '../implementations';

type ParameterType = OrderCheckStrategyParameterType;
export const batchUpdateOrderShippingStatusStrategyMap: OrderCheckStrategy<ParameterType>[] = [
  checkBatchOrderExistStrategy,
];
