# ✅ 使用官方 Docker 映像作為執行環境
image: docker:24.0.5

# ✅ 開啟 Docker-in-Docker（DinD）服務，用來在 runner 裡建置 Docker image
services:
  - name: docker:24.0.5-dind
    command: ["--tls=false"]

# ✅ 定義整體 pipeline 的階段
stages:
  - build
  - push

# ✅ 環境共用變數
variables:
  AWS_REGION: ap-southeast-1
  ECR_REGISTRY: 145059817412.dkr.ecr.ap-southeast-1.amazonaws.com
  IMAGE_NAME: incutix/backend
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""

# ✅ dev 環境：建置與推送
build_dev:
  stage: build
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "🔧 建置 DEV 映像"
    - docker build -t $IMAGE_NAME:dev .
  only:
    - dev

push_dev:
  stage: push
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "📦 推送 DEV 映像到 ECR"
    - docker tag $IMAGE_NAME:dev $ECR_REGISTRY/$IMAGE_NAME:dev
    - docker push $ECR_REGISTRY/$IMAGE_NAME:dev
  only:
    - dev

# ✅ uat 環境
build_uat:
  stage: build
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "🔧 建置 UAT 映像"
    - docker build -t $IMAGE_NAME:uat .
  only:
    - uat

push_uat:
  stage: push
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "📦 推送 UAT 映像到 ECR"
    - docker tag $IMAGE_NAME:uat $ECR_REGISTRY/$IMAGE_NAME:uat
    - docker push $ECR_REGISTRY/$IMAGE_NAME:uat
  only:
    - uat

# ✅ main/production 環境
build_prod:
  stage: build
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "🔧 建置 Production 映像"
    - docker build -t $IMAGE_NAME:latest .
  only:
    - main

push_prod:
  stage: push
  before_script:
    - apk add --no-cache curl python3 py3-pip
    - pip install awscli
    - echo "🔐 設定 AWS 憑證與區域"
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $AWS_REGION
    - echo "🔐 登入 AWS ECR"
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - echo "📦 推送 Production 映像到 ECR"
    - docker tag $IMAGE_NAME:latest $ECR_REGISTRY/$IMAGE_NAME:latest
    - docker push $ECR_REGISTRY/$IMAGE_NAME:latest
  only:
    - main


