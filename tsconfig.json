{
  "ts-node": {
    // Do not forget to `npm i -D tsconfig-paths`
    "require": ["tsconfig-paths/register"]
  },
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig to read more about this file */

    "target": "es2016",
    "module": "commonjs",
    "outDir": "./dist",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,

    /* Type Checking */
    "strict": true,

    /* Completeness */
    "skipLibCheck": true,

    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@modules/*": ["src/modules/*"],
      "@middleware": ["src/middleware"],
      "@models/*": ["src/models/*"],
      "@utils/*": ["src/utils/*"],
      "@config/*": ["src/config/*"],
      "@shared/*": ["src/shared/*"],
      "@routes": ["src/routes"],
      "@routes/*": ["src/routes/*"],
      "@errors": ["src/errors"],
      "@prismaClient": ["prisma/client"]
    }
  }
}
