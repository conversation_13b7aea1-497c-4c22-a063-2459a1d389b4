import winston from 'winston';
import 'winston-daily-rotate-file';

const formatMeta = (meta: any) => {
  const splat = meta[Symbol.for('splat')];
  if (splat && splat.length) {
    return splat.length === 1 ? JSON.stringify(splat[0]) : JSON.stringify(splat);
  }
  return '';
};

const excludeErrorLevel = winston.format((info) => {
  if (info.level === 'error') {
    return false;
  }
  return info;
});

const customFormat = winston.format.printf(
  ({ timestamp, level, message, label = '', ...meta }) =>
    `[${timestamp}] ${level}\t ${label} ${message} ${formatMeta(meta)}`,
);

const requestLogFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.splat(),
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY/MM/DD HH:mm:ss' }),
  customFormat,
);

const infoLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY/MM/DD HH:mm:ss' }),
  winston.format.splat(),
  excludeErrorLevel(),
  customFormat,
);

const errorFormat = winston.format.printf(({ level, message, timestamp, stack }) => {
  return `[${timestamp}] ${level}\t ${message}\n${stack || 'No stack trace'}`;
});
const errorLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY/MM/DD HH:mm:ss' }),
  winston.format.splat(),
  errorFormat,
);

export const logger = winston.createLogger({
  transports: [
    new winston.transports.Console({ level: 'debug', format: requestLogFormat }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/common-%DATE%.log',
      datePattern: 'YYYY-MM-DD-HH-MM',
      level: 'info',
      format: infoLogFormat,
      maxSize: '20m',
      maxFiles: '14d',
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD-HH-MM',
      level: 'error',
      format: errorLogFormat,
      maxSize: '20m',
      maxFiles: '14d',
    }),
  ],
});
