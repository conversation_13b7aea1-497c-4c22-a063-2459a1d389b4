/*
  Warnings:

  - You are about to drop the column `attribute_name` on the `product_attributes` table. All the data in the column will be lost.
  - You are about to drop the column `attribute_value` on the `product_attributes` table. All the data in the column will be lost.
  - Added the required column `product_attribute_category_id` to the `product_attributes` table without a default value. This is not possible if the table is not empty.
  - Added the required column `product_attribute_value_id` to the `product_attributes` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `product_attributes` DROP COLUMN `attribute_name`,
    DROP COLUMN `attribute_value`,
    ADD COLUMN `product_attribute_category_id` INTEGER NOT NULL,
    ADD COLUMN `product_attribute_value_id` INTEGER NOT NULL;

-- CreateTable
CREATE TABLE `product_attribute_values` (
    `product_attribute_value_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_attribute_category_id` INTEGER NOT NULL,
    `note` TEXT NULL,

    INDEX `idx_product_attribute_category_id`(`product_attribute_category_id`),
    PRIMARY KEY (`product_attribute_value_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_attribute_categories` (
    `product_attribute_category_id` INTEGER NOT NULL AUTO_INCREMENT,
    `category_code` VARCHAR(20) NOT NULL,

    INDEX `idx_category_code`(`category_code`),
    PRIMARY KEY (`product_attribute_category_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_attribute_translation` (
    `attributes_translation_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_attribute_category_id` INTEGER NULL,
    `product_attribute_value_id` INTEGER NULL,
    `language_code` ENUM('EN', 'TC', 'TS') NOT NULL DEFAULT 'EN',
    `name` VARCHAR(100) NOT NULL,

    INDEX `idx_category_language`(`product_attribute_category_id`, `language_code`),
    INDEX `idx_value_language`(`product_attribute_value_id`, `language_code`),
    PRIMARY KEY (`attributes_translation_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `product_attributes` ADD CONSTRAINT `product_attributes_product_attribute_category_id_fkey` FOREIGN KEY (`product_attribute_category_id`) REFERENCES `product_attribute_categories`(`product_attribute_category_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `product_attributes` ADD CONSTRAINT `product_attributes_product_attribute_value_id_fkey` FOREIGN KEY (`product_attribute_value_id`) REFERENCES `product_attribute_values`(`product_attribute_value_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `product_attribute_values` ADD CONSTRAINT `product_attribute_values_product_attribute_category_id_fkey` FOREIGN KEY (`product_attribute_category_id`) REFERENCES `product_attribute_categories`(`product_attribute_category_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `product_attribute_translation` ADD CONSTRAINT `product_attribute_translation_product_attribute_category_id_fkey` FOREIGN KEY (`product_attribute_category_id`) REFERENCES `product_attribute_categories`(`product_attribute_category_id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `product_attribute_translation` ADD CONSTRAINT `product_attribute_translation_product_attribute_value_id_fkey` FOREIGN KEY (`product_attribute_value_id`) REFERENCES `product_attribute_values`(`product_attribute_value_id`) ON DELETE SET NULL ON UPDATE CASCADE;
