{"name": "incutix-api", "version": "1.1.5", "description": "", "scripts": {"build": "tsc", "dev": "nodemon --max-old-space-size=4096", "start": "ts-node src/server.ts", "dev:win": "powershell -Command \"$env:NODE_ENV='dev'; nodemon src/server.ts\"", "docker:init": "docker compose down && docker compose up -d", "mocha": "export NODE_ENV=test && dotenv -e .env.test -- npx prisma migrate deploy && ts-mocha src/**/*.spec.ts --exit", "test": "npm run mocha", "cov": "nyc --reporter=text npm run test", "deploy": "tsc && serverless deploy function --function api --force", "db:dev": "dotenv -e .env.dev -- npx prisma migrate dev", "db:test": "dotenv -e .env.test -- npx prisma migrate deploy", "db:deploy": "dotenv -e .env.prod-dev -- npx prisma migrate deploy", "prepare": "husky", "lint": "eslint --ext .ts,.tsx .", "format": "prettier . --write", "db:seed": "prisma db seed"}, "prisma": {"seed": "ts-node --transpile-only prisma/seeds/seed.ts", "schema": "./prisma/schema", "migrateOutput": "./prisma/migrations/"}, "dependencies": {"@prisma/client": "^5.15.0", "@prisma/extension-read-replicas": "^0.3.0", "@types/aws-lambda": "^8.10.138", "@types/express": "^4.17.21", "@types/node": "^20.12.13", "adm-zip": "^0.5.16", "axios": "^1.7.7", "base-64": "^1.0.0", "bcryptjs": "^2.4.3", "bullmq": "^5.53.0", "cors": "^2.8.5", "countries-and-timezones": "^3.8.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "expect": "^29.7.0", "express": "^4.19.2", "express-xml-bodyparser": "^0.0.2", "http-status-codes": "^2.3.0", "ioredis": "^5.6.1", "iso-country-currency": "^0.7.2", "jsonwebtoken": "^9.0.2", "money": "^0.2.0", "multer": "^1.4.5-lts.1", "mustache": "^4.2.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-http-bearer": "^1.0.1", "passport-jwt": "^4.0.1", "qrcode": "^1.5.4", "serverless-http": "^3.2.0", "serverless-plugin-include-dependencies": "^6.1.0", "serverless-plugin-typescript": "^2.1.5", "sharp": "^0.33.5", "sinon": "^18.0.0", "supertest": "^7.0.0", "typescript": "^5.5.4", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.0", "xml-js": "^1.6.11", "zod": "^3.23.8"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.26.0", "@types/adm-zip": "^0.5.7", "@types/base-64": "^1.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/expect": "^24.3.0", "@types/express-xml-bodyparser": "^0.3.5", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^10.0.6", "@types/money": "^0.2.3", "@types/multer": "^1.4.12", "@types/mustache": "^4.2.6", "@types/nodemailer": "^6.4.16", "@types/passport": "^1.0.16", "@types/passport-http-bearer": "^1.0.41", "@types/passport-jwt": "^4.0.1", "@types/qrcode": "^1.5.5", "@types/sinon": "^17.0.3", "@types/supertest": "^6.0.2", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "aws-sdk": "^2.1638.0", "dotenv": "^16.5.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-config-xo": "^0.47.0", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import-x": "^4.11.1", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.1.0", "husky": "^9.0.11", "jiti": "^2.4.2", "mocha": "^10.4.0", "nodemon": "^3.1.4", "nyc": "^17.0.0", "prettier": "^3.5.3", "prisma": "^5.22.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-plugin-common-excludes": "^4.0.0", "ts-mocha": "^10.0.0", "ts-node": "^10.9.2", "tsx": "^4.17.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0"}}