-- CreateTable
CREATE TABLE `product_attributes` (
    `product_attribute_id` INTEGER NOT NULL AUTO_INCREMENT,
    `product_id` INTEGER NOT NULL,
    `attribute_name` VARCHAR(100) NOT NULL,
    `attribute_value` VARCHAR(100) NOT NULL,

    INDEX `idx_product_id`(`product_id`),
    PRIMARY KEY (`product_attribute_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `product_sku_attributes` (
    `product_sku_attribute_id` INTEGER NOT NULL AUTO_INCREMENT,
    `sku_id` INTEGER NOT NULL,
    `product_attribute_id` INTEGER NOT NULL,

    INDEX `idx_sku_id`(`sku_id`),
    INDEX `idx_product_attribute_id`(`product_attribute_id`),
    PRIMARY KEY (`product_sku_attribute_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `product_attributes` ADD CONSTRAINT `product_attributes_product_id_fkey` FOREIGN KEY (`product_id`) REFERENCES `products`(`product_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `product_sku_attributes` ADD CONSTRAINT `product_sku_attributes_sku_id_fkey` FOREIGN KEY (`sku_id`) REFERENCES `product_sku`(`sku_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `product_sku_attributes` ADD CONSTRAINT `product_sku_attributes_product_attribute_id_fkey` FOREIGN KEY (`product_attribute_id`) REFERENCES `product_attributes`(`product_attribute_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
