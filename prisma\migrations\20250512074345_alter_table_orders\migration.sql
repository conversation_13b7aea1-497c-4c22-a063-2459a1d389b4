/*
  Warnings:

  - You are about to drop the column `cancel_time` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `complete_time` on the `orders` table. All the data in the column will be lost.
  - Added the required column `cancel_at` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `complete_at` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expire_at` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `order_idempotency_key` to the `orders` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `orders` DROP COLUMN `cancel_time`,
    DROP COLUMN `complete_time`,
    ADD COLUMN `cancel_at` INTEGER NOT NULL,
    ADD COLUMN `complete_at` INTEGER NOT NULL,
    ADD COLUMN `expire_at` INTEGER NOT NULL,
    ADD COLUMN `order_idempotency_key` VARCHAR(40) NOT NULL;
