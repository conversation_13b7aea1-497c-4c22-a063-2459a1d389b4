const { JWT_SECRET = '' } = process.env;
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

export const extractUser = (headers: any, extractEmail: boolean = false) => {
  const { authorization } = headers;

  if (!authorization) return undefined;

  const token = authorization.replace('Bearer ', '');
  let userId;

  try {
    const result = jwt.verify(token, JWT_SECRET);

    if (extractEmail) {
      return result as any;
    }
    userId = result.sub;
  } catch (error) {
    logger.error('jwt verification failed');
  }

  return userId;
};
