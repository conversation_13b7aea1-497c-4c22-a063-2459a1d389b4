import { generateTimeNow } from '@/utils/common';
import db from '../client/index';
import countryData from './data/country.json';

const inActiveCountryCode = ['ANT', 'SCG', 'SUN', 'DDR', 'CSK', 'YUG', 'SKM', 'DHY', 'HVO', 'BUR'];

export default async () => {
  const now = generateTimeNow();

  const formattedData = countryData.map((country) => ({
    country_code: country.code,
  }));

  const t2 = countryData.flatMap((x) => {
    const a = { country_code: x.code, language_code: 'EN', name: x.countryNameEN };
    const b = { country_code: x.code, language_code: 'TC', name: x.countryNameTC };
    const c = { country_code: x.code, language_code: 'TS', name: x.countryNameTS };
    const d = { country_code: x.code, language_code: 'TH', name: x.countryNameTH };

    return [a, b, c, d].map((x) => ({ ...x, created_at: now, updated_at: now }));
  });

  // Query All country
  const result = await db.country.findMany({
    select: { country_code: true },
    where: {
      country_code: { in: formattedData.map((x) => x.country_code) },
    },
  });
  const r2 = await db.country_translations.findMany({
    select: { country_code: true },
    where: {
      country_code: { in: t2.map((x) => x.country_code) },
    },
  });

  const duplicateCountryCodes = result.map(({ country_code }) => country_code);
  const duplicateCountryCodes2 = r2.map(({ country_code }) => country_code);
  const newCountryCode = formattedData.filter(
    ({ country_code }) => !duplicateCountryCodes.includes(country_code),
  );
  const newt2 = t2.filter(({ country_code }) => !duplicateCountryCodes2.includes(country_code));

  console.log(1001, newt2);

  // Create many country
  await db.country.createMany({
    data: newCountryCode,
  });

  await db.country_translations.createMany({
    data: newt2,
  });

  // in-active country
  await db.country.updateMany({
    where: { country_code: { in: inActiveCountryCode } },
    data: { is_active: false },
  });

  console.log('[Prisma] Seed country insert progress.');
};
