/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model shopping_cart {
  cart_id                 Int                   @id @default(autoincrement())
  user_id                 Int                   @unique(map: "uk_user_session")
  cart_type               cart_type             
  cart_total              Decimal?              @default(0.00) @db.Decimal(12, 2)
  discount_total          Decimal?              @default(0.00) @db.Decimal(12, 2)
  item_count              Int?                  @default(0)
  created_at              Int             
  updated_at              Int             
  expires_at              Int?
  user                    User?                 @relation(fields: [user_id], references: [id])
  shoppingCartItems shopping_cart_item[]

  @@index([cart_type], map: "idx_cart_type")
  @@index([expires_at], map: "idx_expires")
  @@index([user_id], map: "idx_user_id")
  @@index([user_id, cart_type], map: "idx_uid_cart_check")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model shopping_cart_item {
  shopping_cart_item_id   Int                 @id @default(autoincrement())
  cart_id                 Int
  cart_item_type          cart_item_type
  is_selected             Boolean             @default(false)
  product_id              Int?
  sku_id                  Int?
  quantity                Int                 @default(1)
  original_price          Decimal             @db.Decimal(12, 2)
  sale_price              Decimal             @db.Decimal(12, 2)
  total_price             Decimal             @db.Decimal(12, 2)
  added_at                Int
  created_at              Int
  updated_at              Int
  notes                   String?             @db.VarChar(200)
  shoppingCart            shopping_cart       @relation(fields: [cart_id], references: [cart_id])
  products                products?           @relation(fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction, map: "shopping_cart_item_ibfk_1")
  product_sku             product_sku?        @relation(fields: [sku_id], references: [sku_id], onDelete: NoAction, onUpdate: NoAction, map: "shopping_cart_item_ibfk_2")

  @@unique([cart_id, cart_item_type, product_id, sku_id], map: "uk_cart_item")
  @@index([cart_id], map: "idx_cart_id")
  @@index([cart_item_type], map: "idx_cart_item_type")
  @@index([product_id, sku_id], map: "idx_product_sku")
  @@index([cart_id, product_id, sku_id], map: "idx_user_product")
  @@index([sku_id], map: "sku_id")
}

model shopping_cart_activity_log {
  shopping_cart_activity_log  Int                    @id @default(autoincrement())
  cart_id                     Int
  user_id                     Int
  cart_activity_type          cart_activity_type
  shopping_cart_item_id       Int
  old_value                   Json                 
  new_value                   Json                 
  extra_info                  Json                   
  operation_time              BigInt

  @@index([cart_id], map: "idx_cart_id")
  @@index([user_id, operation_time], map: "idx_user_id_operation")
  @@index([operation_time], map: "idx_operation_time")
  @@index([shopping_cart_item_id], map: "idx_shopping_cart_item_id")
}

enum cart_type {
  productOnly
  ticketOnly
  hybrid
}

enum cart_item_type {
  product
  ticket
}

enum cart_activity_type {
  addCartItem
  removeCartItem
  updateQuantity
  clearCart
  checkout
  selectCartItem
  unSelectCartItem
}

