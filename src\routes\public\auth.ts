import { Router } from 'express';
import * as authController from '@modules/auth/auth.controller';

const router = Router();

router.post('/register-token', authController.registerToken);

// router.post('/register', authController.userRegister);
// router.post('/verify-otp', authController.verifyOTP);
// router.post('/resend-otp', authController.resendOTP);
// router.post('/verify', authController.verifyUser);
// router.post('/change-password', jwtMiddleware, authController.changePassword);
// router.post('/forgot-password', authController.forgotPassword);
// router.post('/verify-passcode', authController.verifyPasscode);
// router.post('/reset-password', authController.resetPassword);
// router.post('/refresh-token', authController.refreshToken);

export default router;
